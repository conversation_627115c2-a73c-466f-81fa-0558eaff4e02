package cloud.demand.app.common.excel.checker;

import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import cn.hutool.core.util.StrUtil;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.nutz.lang.Strings;

public class DateTimeFormatChecker implements ExcelColumnValueChecker {

    private final DateTimeFormatter formatter;

    private final String formatPattern;

    private final boolean skipBlank;

    public DateTimeFormatChecker() {
        this.formatPattern = "yyyy-MM-dd HH:mm:ss";
        this.formatter = DateTimeFormatter.ofPattern(formatPattern);
        this.skipBlank = false;
    }

    public DateTimeFormatChecker(String format) {
        this.formatPattern = format;
        this.formatter = DateTimeFormatter.ofPattern(format);
        this.skipBlank = false;
    }

    public DateTimeFormatChecker(String format, boolean skipBlank) {
        this.formatPattern = format;
        this.formatter = DateTimeFormatter.ofPattern(format);
        this.skipBlank = skipBlank;
    }

    public static DateTimeFormatChecker notSkipBlank(String format) {
        return new DateTimeFormatChecker(format, false);
    }

    public static DateTimeFormatChecker skipBlank(String format) {
        return new DateTimeFormatChecker(format, true);
    }

    @Override
    public void checkValue(int rowIndex, int columnIndex, String columnName, Object value, List<ErrorMessage> errors,
            ParseContext<?> context) {
        try {
            if (skipBlank) {
                if (value == null || Strings.isBlank(value.toString())) {
                    return;
                }
            }
            if (value == null) {
                String msgFormat = "填写值为空，不符合要求的时间日期格式【{}】";
                String msg = StrUtil.format(msgFormat, formatPattern);
                ErrorMessage errorMessage = new ErrorMessage(rowIndex, columnIndex, columnName, msg);
                addNoneNullErrorMessage(errors, errorMessage);
                return;
            }
            formatter.parse(value.toString());
        } catch (Exception e) {
            String msgFormat = "填写值【{}】不符合要求的时间日期格式【{}】";
            String msg = StrUtil.format(msgFormat, value, formatPattern);
            ErrorMessage errorMessage = new ErrorMessage(rowIndex, columnIndex, columnName, msg);
            addNoneNullErrorMessage(errors, errorMessage);
        }
    }
}
