package cloud.demand.app.common.excel;

import cloud.demand.app.common.excel.core.DotExcelListener;
import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ErrorMessage.IGetter;
import cloud.demand.app.common.excel.core.ExcelFieldInfo;
import cloud.demand.app.common.excel.core.ExcelGroup;
import cloud.demand.app.common.excel.core.ParseContext;
import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.common.excel.core.annotation.DotExcelEntity;
import cloud.demand.app.common.excel.core.annotation.DotExcelField;
import cloud.demand.app.common.excel.core.checker.ExcelColumnValueChecker;
import cloud.demand.app.common.excel.core.checker.ExcelResultDataAfterConvertChecker;
import cloud.demand.app.common.excel.core.checker.ExcelRowDataAfterConvertChecker;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.exception.ExcelCommonException;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  excel 读取工具类 <br/>
 *
 *  使用方法 <br/> <br/>
 *
 *  step 1: <br/>
 *  在{@link ExcelGroupEnum} 中定义Excel组，Excel组名关联到{@link GroupConstant} <br/><br/>
 *
 *  step 2: <br/>
 *  通过{@link DotExcelField}定义需要的解析的字段，并在其实体类上面加上注解{@link DotExcelEntity}用于自动扫描注册 <br/>
 *  tips: 每个Excel组中需要解析的字段可以分散在任何实体类中。 <br/><br/>
 *
 *  step 3: <br/>
 *  通过{@link DotExcelReadUtil} 读取 excel  <br/><br/>
 *
 *  更多个性化操作可以使用 {@link DotExcelBuilder} 来完成 <br/><br/>
 *  具体实现参考测试用例： {@code MainTest}
 */
public class DotExcelReadUtil {

    /**
     *   通过 build 进行 excel 数据读取
     * @param builder dotExcel构建器，构建excel数据读取的相关参数
     */
    @SuppressWarnings("checkstyle:GenericWhitespace")
    public static <T> ReadResult<T> read(DotExcelBuilder<T> builder) {
        DotExcelListener<T> listener = builder.checkAndBuild();
        ExcelReaderSheetBuilder readerBuilder = EasyExcel.read(builder.inputStream, listener)
                .useDefaultListener(false)
                .sheet(builder.sheetNo, builder.sheetName)
                .headRowNumber(builder.headRowNumber);
        if (builder.globalConverts != null && builder.globalConverts.size() > 0) {
            for (Converter<?> globalConvert : builder.globalConverts) {
                readerBuilder.registerConverter(globalConvert);
            }
        }
        readerBuilder.doRead();
        return new ReadResult<T>(listener);
    }

    /**
     *  从指定文件读取excel数据 <br/>
     * @param group  excel组 <br/>
     * @param clazz 数据接收的实体类的类型 <br/>
     * @param excelPath excel文件路径 <br/>
     * @param headRowNumber 表头所在的行数。必须大于0，从1开始，1表示第一行是表头(则第二行就是数据)，
     *                       2表示第二行是表头(则第三行就是数据) <br/>
     */
    @SuppressWarnings("checkstyle:GenericWhitespace")
    public static <T>ReadResult<T> read(ExcelGroup group, Class<T> clazz, String excelPath, int headRowNumber) {
        return read(group, clazz, excelPath, headRowNumber, null);
    }

    /**
     *  从指定文件读取excel数据 <br/>
     * @param group  excel组 <br/>
     * @param clazz 数据接收的实体类的类型 <br/>
     * @param excelPath excel文件路径 <br/>
     * @param headRowNumber 表头所在的行数。必须大于0，从1开始，1表示第一行是表头(则第二行就是数据)，
     *                       2表示第二行是表头(则第三行就是数据) <br/>
     * @param context 业务方自定义的上下文信息，可以在各个数据校验器中使用,
     *                 通过 {@link ParseContext#getCustomContext()} 获取 <br/>
     */
    @SuppressWarnings("checkstyle:GenericWhitespace")
    public static <T>ReadResult<T> read(ExcelGroup group, Class<T> clazz, String excelPath, int headRowNumber,
            Map<String, Object> context) {
        checkHeadRowNumber(headRowNumber);
        DotExcelListener<T> listener = new DotExcelListener<>(group, clazz, context);
        EasyExcel.read(excelPath, listener)
                .useDefaultListener(false)
                .sheet()
                .headRowNumber(headRowNumber)
                .doRead();
        return new ReadResult<T>(listener);
    }

    /**
     *  从指定输入流读取excel数据 <br/>
     * @param group excel组 <br/>
     * @param clazz 数据接收的实体类的类型 <br/>
     * @param inputStream excel文件路径 <br/>
     * @param headRowNumber 表头所在的行数。必须大于0，从1开始，1表示第一行是表头(则第二行就是数据)，
     *                       2表示第二行是表头(则第三行就是数据) <br/>
     * @param context 业务方自定义的上下文信息，可以在各个数据校验器中使用,
     *                 通过 {@link ParseContext#getCustomContext()} 获取 <br/>
     */
    @SuppressWarnings("checkstyle:GenericWhitespace")
    public static <T>ReadResult<T> read(ExcelGroup group, Class<T> clazz, InputStream inputStream, int headRowNumber,
            Map<String, Object> context) {
        checkHeadRowNumber(headRowNumber);
        DotExcelListener<T> listener = new DotExcelListener<>(group, clazz, context);
        EasyExcel.read(inputStream, listener)
                .useDefaultListener(false)
                .sheet()
                .headRowNumber(headRowNumber)
                .doRead();
        return new ReadResult<T>(listener);
    }

    /**
     *  从指定输入流读取excel数据 <br/>
     * @param group excel组 <br/>
     * @param clazz 数据接收的实体类的类型 <br/>
     * @param inputStream excel文件路径 <br/>
     * @param headRowNumber 表头所在的行数。必须大于0，从1开始，1表示第一行是表头(则第二行就是数据)，
     *                       2表示第二行是表头(则第三行就是数据) <br/>
     */
    @SuppressWarnings("checkstyle:GenericWhitespace")
    public static <T>ReadResult<T> read(ExcelGroup group, Class<T> clazz, InputStream inputStream, int headRowNumber) {
        return read(group, clazz, inputStream, headRowNumber, null);
    }

    private static void checkHeadRowNumber(int headRowNumber) {
        if (headRowNumber < 1) {
            throw new IllegalArgumentException("the params headRowNumber must greater than 0，input value:"
                    + headRowNumber);
        }
    }

    /**
     *   dotExcel构建器，构建excel数据读取的相关参数
     * @param clazz 数据接收的实体类的类型 <br/>
     * @param group excel组 <br/>
     * @param headRowNumber 表头所在的行数。必须大于0，从1开始，1表示第一行是表头(则第二行就是数据)，
     *                      2表示第二行是表头(则第三行就是数据) <br/>
     */
    public static <R> DotExcelBuilder<R> createBuilder(Class<R> clazz, ExcelGroup group, int headRowNumber) {
        return new DotExcelBuilder<>(clazz, group, headRowNumber);
    }

    /** dotExcel构建器，构建excel数据读取的相关参数 */
    public static class DotExcelBuilder<R> {

        private InputStream inputStream;

        private String excelPath;

        private File excelFile;

        private ExcelGroup group;

        private Class<R> clazz;

        private int headRowNumber;

        private Integer sheetNo;

        private String sheetName;

        private Map<String, Object> context;

        private List<ErrorMessage> errorMessages;

        private List<R> resultList;

        private Map<String, List<ExcelColumnValueChecker>> valueCheckers;

        private List<ExcelRowDataAfterConvertChecker<? super R>> rowCheckers;

        private List<ExcelResultDataAfterConvertChecker<? super R>> resultCheckers;

        private Map<String, Converter<?>> fieldConvertMap;

        private List<ExcelFieldInfo> fieldInfoList;

        private List<Converter<?>> globalConverts;

        private DotExcelBuilder(Class<R> clazz, ExcelGroup group, int headRowNumber) {
            this.clazz = clazz;
            this.group = group;
            this.headRowNumber = headRowNumber;
        }

        private DotExcelListener<R> checkAndBuild() {
            checkHeadRowNumber(this.headRowNumber);
            sourceCheck();
            return new DotExcelListener<>(this.group, this.clazz, this.resultList, this.errorMessages,
                    this.context, this.valueCheckers, this.rowCheckers, this.resultCheckers,
                    this.fieldConvertMap, this.fieldInfoList);
        }

        private void sourceCheck() {
            try {
                if (this.inputStream != null) {
                    return;
                }
                if (this.excelFile != null) {
                    this.inputStream = Files.newInputStream(this.excelFile.toPath());
                    return;
                }
                if (this.excelPath != null) {
                    this.inputStream = Files.newInputStream(Paths.get(this.excelPath));
                    return;
                }
                throw new ExcelCommonException("no excel source params");
            } catch (IOException e) {
                throw new ExcelCommonException("no excel source input", e);
            }
        }

        /**
         *  excel文档数据流, {@link #inputStream}、{@link #excelPath}、{@link #excelFile} 三选一赋值即可
         */
        public DotExcelBuilder<R> inputStream(InputStream inputStream) {
            this.inputStream = inputStream;
            return this;
        }

        /** excel文档路径, {@link #inputStream}、{@link #excelPath}、{@link #excelFile} 三选一赋值即可 */
        public DotExcelBuilder<R> excelPath(String excelPath) {
            this.excelPath = excelPath;
            return this;
        }

        /** excel文件, {@link #inputStream}、{@link #excelPath}、{@link #excelFile} 三选一赋值即可 */
        public DotExcelBuilder<R> excelFile(File excelFile) {
            this.excelFile = excelFile;
            return this;
        }

        /** sheetNo */
        public DotExcelBuilder<R> sheetNo(Integer sheetNo) {
            this.sheetNo = sheetNo;
            return this;
        }

        /** sheetName */
        public DotExcelBuilder<R> sheetName(String sheetName) {
            this.sheetName = sheetName;
            return this;
        }

        /** 数据校验错误结果存储列表，可不传 */
        public DotExcelBuilder<R> errorMessages(List<ErrorMessage> errorMessages) {
            this.errorMessages = errorMessages;
            return this;
        }

        /** 数据解析结果列表，可不传 */
        public DotExcelBuilder<R> resultList(List<R> resultList) {
            this.resultList = resultList;
            return this;
        }

        /** 自定义上下文信息 */
        public DotExcelBuilder<R> registerContext(Map<String, Object> context) {
            if (context == null || context.size() < 1) {
                return this;
            }
            context.forEach(this::registerContext);
            return this;
        }

        /** 自定义上下文信息 */
        public DotExcelBuilder<R> registerContext(String key, Object value) {
            if (key == null) {
                return this;
            }
            if (this.context == null) {
                this.context = new HashMap<>();
            }
            this.context.put(key, value);
            return this;
        }

        /**
         *   列数据校验器, 推荐使用{@link #registerValueCheckerByGetter(IGetter, ExcelColumnValueChecker...)}
         * @param javaFieldName 进行数据校验的 java 字段名
         * @param valueCheckers 列数据校验器
         */
        public DotExcelBuilder<R> registerValueCheckers(String javaFieldName,
                List<ExcelColumnValueChecker> valueCheckers) {
            if (javaFieldName == null || valueCheckers == null || valueCheckers.size() < 1) {
                return this;
            }
            valueCheckers.forEach(checker -> registerValueChecker(javaFieldName, checker));
            return this;
        }

        /**
         *   列数据校验器, 推荐使用{@link #registerValueCheckerByGetter(IGetter, ExcelColumnValueChecker...)}
         * @param javaFieldName 进行数据校验的 java 字段名
         * @param valueChecker 列数据校验器
         */
        public DotExcelBuilder<R> registerValueChecker(String javaFieldName, ExcelColumnValueChecker valueChecker) {
            if (valueChecker == null || javaFieldName == null) {
                return this;
            }
            List<ExcelColumnValueChecker> checkers = getValueCheckers(javaFieldName);
            checkers.add(valueChecker);
            this.valueCheckers.put(javaFieldName, checkers);
            return this;
        }

        /**
         *   列数据校验器
         * @param getter 用于获取进行数据校验的 java 字段名
         * @param valueChecker 列数据校验器
         */
        public DotExcelBuilder<R> registerValueCheckerByGetter(IGetter getter, ExcelColumnValueChecker... valueChecker) {
            if (valueChecker == null || getter == null || valueChecker.length < 1) {
                return this;
            }
            String javaFieldName = ErrorMessage.getFieldNameByGetter(getter);
            for (ExcelColumnValueChecker checker : valueChecker) {
                registerValueChecker(javaFieldName, checker);
            }
            return this;
        }

        private List<ExcelColumnValueChecker> getValueCheckers(String javaFieldName) {
            List<ExcelColumnValueChecker> checkers = null;
            if (this.valueCheckers == null) {
                this.valueCheckers = new HashMap<>();
            } else {
                checkers = this.valueCheckers.get(javaFieldName);
            }
            if (checkers == null) {
                checkers = new ArrayList<>();
            }
            return checkers;
        }

        /** 行数据校验器 */
        public DotExcelBuilder<R> registerRowCheckers(List<ExcelRowDataAfterConvertChecker<? super R>> rowCheckers) {
            if (rowCheckers == null || rowCheckers.size() < 1) {
                return this;
            }
            rowCheckers.forEach(this::registerRowChecker);
            return this;
        }

        /** 行数据校验器 */
        public DotExcelBuilder<R> registerRowChecker(ExcelRowDataAfterConvertChecker<? super R> rowChecker) {
            if (rowChecker == null) {
                return this;
            }
            if (this.rowCheckers == null) {
                this.rowCheckers = new ArrayList<>();
            }
            this.rowCheckers.add(rowChecker);
            return this;
        }

        /** 结果集数据校验器 */
        public DotExcelBuilder<R> registerResultCheckers(List<ExcelResultDataAfterConvertChecker<? super R>> resultCheckers) {
            if (resultCheckers == null || resultCheckers.size() < 1) {
                return this;
            }
            resultCheckers.forEach(this::registerResultChecker);
            return this;
        }

        /** 结果集数据校验器 */
        public DotExcelBuilder<R> registerResultChecker(ExcelResultDataAfterConvertChecker<? super R> resultChecker) {
            if (resultChecker == null) {
                return this;
            }
            if (this.resultCheckers == null) {
                this.resultCheckers = new ArrayList<>();
            }
            this.resultCheckers.add(resultChecker);
            return this;

        }

        /**
         *   字段解析转换器, 推荐使用{@link #registerFieldConvertByGetter(IGetter, Converter)}
         */
        public DotExcelBuilder<R> registerFieldConvertMap(Map<String, Converter<?>> fieldConvertMap) {
            if (fieldConvertMap == null || fieldConvertMap.size() < 1) {
                return this;
            }
            fieldConvertMap.forEach(this::registerFieldConvert);
            return this;
        }

        /**
         *   字段解析转换器, 推荐使用{@link #registerFieldConvertByGetter(IGetter, Converter)}
         * @param javaFieldName 字段名
         * @param converter 转换器
         */
        public DotExcelBuilder<R> registerFieldConvert(String javaFieldName, Converter<?> converter) {
            if (javaFieldName == null || converter == null) {
                return this;
            }
            if (this.fieldConvertMap == null) {
                this.fieldConvertMap = new HashMap<>();
            }
            this.fieldConvertMap.put(javaFieldName, converter);
            return this;
        }

        /**
         *   字段解析转换器
         * @param getter 用于获取字段名
         * @param converter 转换器
         */
        public DotExcelBuilder<R> registerFieldConvertByGetter(IGetter getter, Converter<?> converter) {
            if (getter == null) {
                return this;
            }
            String javaFieldName = ErrorMessage.getFieldNameByGetter(getter);
            return registerFieldConvert(javaFieldName, converter);
        }

        /**
         *  需要解析的字段信息
         */
        public DotExcelBuilder<R> registerFieldInfoList(List<ExcelFieldInfo> fieldInfoList) {
            if (fieldInfoList == null) {
                return this;
            }
            for (ExcelFieldInfo info : fieldInfoList) {
                registerFieldInfo(info);
            }
            return this;
        }

        /**
         *  需要解析的字段信息
         */
        public DotExcelBuilder<R> registerFieldInfo(ExcelFieldInfo fieldInfo) {
            if (fieldInfo == null) {
                return this;
            }
            if (this.fieldInfoList == null) {
                this.fieldInfoList = new ArrayList<>();
            }
            for (int i = 0; i < fieldInfoList.size(); i++) {
                if (ExcelFieldInfo.isSameName(fieldInfoList.get(i), fieldInfo)) {
                    // 若名称相同，则替换
                    fieldInfoList.set(i, fieldInfo);
                    return this;
                }
            }
            this.fieldInfoList.add(fieldInfo);
            return this;
        }

        /**
         *  添加解析过程中全局使用的 convert
         */
        public DotExcelBuilder<R> registerGlobalConvert(Converter<?> converter) {
            if (converter == null) {
                return this;
            }
            if (this.globalConverts == null) {
                this.globalConverts = new ArrayList<>();
            }
            this.globalConverts.add(converter);
            return this;
        }

    }

}
