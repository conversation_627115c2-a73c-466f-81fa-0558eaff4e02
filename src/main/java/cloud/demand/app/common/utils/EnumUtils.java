package cloud.demand.app.common.utils;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.function.Function;

public class EnumUtils {
    public static <T extends Enum<T>, K, V> Map<K, V> enumToMap(Class<T> enumClass, Function<T, K> keyGetter, Function<T, V> valueGetter) {
        Map<K, V> map = new LinkedHashMap<>();

        for (T enumConstant : enumClass.getEnumConstants()) {
            K key = keyGetter.apply(enumConstant);
            V value = valueGetter.apply(enumConstant);
            map.put(key, value);
        }

        return map;
    }

}
