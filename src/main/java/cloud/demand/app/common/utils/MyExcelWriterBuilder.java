package cloud.demand.app.common.utils;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import java.lang.reflect.Field;
import lombok.extern.slf4j.Slf4j;
import yunti.boot.exception.ITException;

@Slf4j
public class MyExcelWriterBuilder extends ExcelWriterBuilder {

    public MyExcelWriterBuilder() {
        super();
    }

    public MyExcelWriterBuilder(ExcelWriterBuilder excelWriterBuilder) {
        super();
        try {
            Field fieldFather = ExcelWriterBuilder.class.getDeclaredField("writeWorkbook");
            fieldFather.setAccessible(true);
            Object objFather = fieldFather.get(excelWriterBuilder);
            Field fieldSon = this.getClass().getSuperclass().getDeclaredField("writeWorkbook");
            fieldSon.setAccessible(true);
            fieldSon.set(this, objFather);
        } catch (Exception e) {
            log.error("", e);
            throw new ITException(e);
        }
    }

    public ExcelWriter build() {
        return new MyExcelWriter(parameter());
    }
}
