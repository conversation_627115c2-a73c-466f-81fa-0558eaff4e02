package cloud.demand.app.common.utils;

import java.util.concurrent.Callable;
import java.util.function.Supplier;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

public class SecurityContextUtils {

    public static <T> Supplier<T> wrapSupplier(Supplier<T> supplier) {
        // 在提交任务的线程中捕获认证信息
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return () -> {
            try {
                // 在异步线程中设置认证
                SecurityContextHolder.getContext().setAuthentication(authentication);
                return supplier.get();
            } finally {
                // 确保清除上下文
                SecurityContextHolder.clearContext();
            }
        };
    }
    
    // 可选：处理Runnable的包装（如果不需要返回值）
    public static Runnable wrapRunnable(Runnable runnable) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return () -> {
            try {
                SecurityContextHolder.getContext().setAuthentication(authentication);
                runnable.run();
            } finally {
                SecurityContextHolder.clearContext();
            }
        };
    }
}