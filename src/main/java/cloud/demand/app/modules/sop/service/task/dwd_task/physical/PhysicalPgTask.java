package cloud.demand.app.modules.sop.service.task.dwd_task.physical;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.sop.domain.SopCommonReq;
import cloud.demand.app.modules.sop.domain.http.SopPurchaseDetailRes;
import cloud.demand.app.modules.sop.domain.http.SopPurchaseDetailResList;
import cloud.demand.app.modules.sop.entity.dwd.DwdSopReportPgDO;
import cloud.demand.app.modules.sop.enums.task.DwdTaskEnum;
import cloud.demand.app.modules.sop.enums.FlagType;
import cloud.demand.app.modules.sop.enums.SopResourcePool;
import cloud.demand.app.modules.sop.service.task.dwd_task.DWDTaskService;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop.util.DataTransformUtil;
import cloud.demand.app.modules.sop.util.SopDateUtils;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

/**
 * 采购净预测 DWD 接口
 */
@Service
@Slf4j
public class PhysicalPgTask extends DWDTaskService {
    @Override
    public DwdTaskEnum task() {
        return DwdTaskEnum.PHYSICAL_NEW_PG;
    }

    @SneakyThrows
    @Override
    @TaskLog(taskName = "sopCreateDWDData@PhysicalPgTask")
    public void createDWDData(String version) {
        StopWatch stopWatch = new StopWatch("采购净预测");
        stopWatch.start("1. 请求上游api获取采购净预测");

        LocalDate dateVersion = commonDbHelper.getStatTimeFromVersion(version);

        // 应上游接口，这里实际作用的是dateVersion,startDate和endDate和dateVersion一致即可，不影响结果
        SopCommonReq req = SopCommonReq.builder()
                .version(version)
                .dateVersion(SopDateUtils.dateVersion(dateVersion))
                .startDate(SopDateUtils.dateRange(dateVersion))
                .build();
        // 上游sop数据拉取
        SopPurchaseDetailRes res = sopService.getSopPurchaseDetailByVersion(req);
        long count = 0L;
        if (res != null && !CollectionUtils.isEmpty(res.getPg())){
            // 起始id
            AtomicLong startId = new AtomicLong(CkDBUtils.startId());
            // 创建时间
            LocalDateTime statTime = DateUtils.toLocalDateTime(new Date());
            // 初始化清洗映射表
            initLocalMap(res.getPg());
            List<DwdSopReportPgDO> saveData = ListUtils.transform(res.getPg(),(item)-> transform(item,version,dateVersion,startId,statTime));
            insert(version, saveData,DwdSopReportPgDO.class);
            count = saveData.size();
        }
        log.info("DWD执行采购净预测 ----> version: [{}], indexDate: [{}], count: [{}]",version, dateVersion,count);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    public DwdSopReportPgDO transform(SopPurchaseDetailResList item,
                                      String version,
                                      LocalDate indexDate,
                                      AtomicLong startId,
                                      LocalDateTime statTime){
        DwdSopReportPgDO ret = new DwdSopReportPgDO();
        ret.setVersion(version);
        ret.setId(startId.getAndIncrement());
        ret.setStatTime(statTime);
        ret.setNum(BigDecimal.valueOf(ObjectUtils.defaultIfNull(item.getAmount(),0)));
        ret.setResType(item.getResourceType());
        SopDateUtils.dateTransform(item,ret);
        ret.setResPoolType(SopResourcePool.getDesc(item.getResourcePool()));
        ret.setObsProjectType(item.getProjectType());
        // 没有数据
//        ret.setBgName(item.getBgName());
        ret.setCustomBgName(item.getCustomBgName());
        ret.setDeptName(item.getDeptName());
        ret.setPlanProductName(item.getPlanProductName());
        ret.setCustomhouseTitle(item.getAreaType());
        ret.setCountryName(item.getCountry());
        ret.setCityName(item.getCity());
        ret.setCmdbCampusName(item.getCampus());
        ret.setCmdbModuleName(item.getModule());
        // 没有数据
//        ret.setTxyZoneName(item.getTxyZoneName());
        ret.setCvmGinsFamily(item.getInstanceType());
        ret.setCvmGinsType(item.getInstanceModel());
        ret.setPhyDeviceFamily(item.getDeviceFamily());
        ret.setPhyDeviceType(item.getDeviceType());
        // isCa默认false
        ret.setIsCa(FlagType.NO.getCode());
        ret.setIsHedge(item.getIsHedging());
        ret.setHasHedged(item.getIsValidHedging());
        ret.setBusinessType(DataTransformUtil.transform(item.getBusinessType()));
        ret.setObsBusinessType(DataTransformUtil.transform(item.getBusinessType()));
        // 需求日期，后面可能会用
        ret.setJsonText(JSON.toJson(ImmutableMap.of("date",item.getDate())));
        cleaning(ret);
        return ret;
    }

}
