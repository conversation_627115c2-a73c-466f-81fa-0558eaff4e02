package cloud.demand.app.modules.sop_device.domain;

import cloud.demand.app.modules.sop_device.domain.IndexVal.ValItem;
import cloud.demand.app.modules.sop_device.entity.SupplyWrapper;
import cloud.demand.app.modules.sop_device.utils.LocalDateConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 供需对冲结果表
 */
@Data
@ToString
@Table("supply_demand_hedging_dto_result")
public class SupplyDemandHedgingResultDTO implements SupplyWrapper {

    /**
     * 主键<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Integer id;
    /**
     * erp业务类型<br/>Column: [business_type]
     */
    @Column(value = "business_type")
    @ExcelProperty(index = 0, value = "erp业务类型")
    private String businessType;
    /**
     * 自定义事业群<br/>Column: [custom_bg]
     */
    @Column(value = "custom_bg")
    @ExcelProperty(index = 1, value = "自定义事业群")
    private String customBg;
    /**
     * 业务部门<br/>Column: [dept_name]
     */
    @Column(value = "dept_name")
    @ExcelProperty(index = 2, value = "业务部门")
    private String deptName;
    /**
     * 规划产品<br/>Column: [plan_product]
     */
    @Column(value = "plan_product")
    @ExcelProperty(index = 3, value = "规划产品")
    private String planProduct;
    /**
     * 规划产品ID<br/>Column: [plan_product_id]
     */
    @Column(value = "plan_product_id")
    @ExcelProperty(index = 4, value = "规划产品ID")
    private Integer planProductId;
    /**
     * 项目类型<br/>Column: [project_type]
     */
    @Column(value = "project_type")
    @ExcelProperty(index = 5, value = "项目类型")
    private String projectType;
    /**
     * 需求地域<br/>Column: [demand_area]
     */
    @Column(value = "demand_area")
    @ExcelProperty(index = 6, value = "需求地域")
    private String demandArea;
    /**
     * 需求region<br/>Column: [demand_region]
     */
    @Column(value = "demand_region")
    @ExcelProperty(index = 7, value = "需求region")
    private String demandRegion;
    /**
     * 需求城市<br/>Column: [demand_city]
     */
    @Column(value = "demand_city")
    @ExcelProperty(index = 8, value = "需求城市")
    private String demandCity;
    /**
     * 需求campus<br/>Column: [demand_campus]
     */
    @Column(value = "demand_campus")
    @ExcelProperty(index = 9, value = "需求campus")
    private String demandCampus;
    /**
     * 需求设备类型<br/>Column: [demand_device_type]
     */
    @Column(value = "demand_device_type")
    @ExcelProperty(index = 10, value = "需求设备类型")
    private String demandDeviceType;
    /**
     * 需求机型族<br/>Column: [demand_device_family]
     */
    @Column(value = "demand_device_family")
    @ExcelProperty(index = 11, value = "需求机型族")
    private String demandDeviceFamily;
    /**
     * 需求日期<br/>Column: [demand_date]
     */
    @Column(value = "demand_date")
    @ExcelProperty(index = 12, value = "需求日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT-8")
    private LocalDate demandDate;
    /**
     * 需求年<br/>Column: [demand_year]
     */
    @Column(value = "demand_year")
    @ExcelProperty(index = 13, value = "需求年")
    private Integer demandYear;
    /**
     * 需求月<br/>Column: [demand_month]
     */
    @Column(value = "demand_month")
    @ExcelProperty(index = 14, value = "需求月")
    private Integer demandMonth;
    /**
     * 需求周<br/>Column: [demand_week]
     */
    @Column(value = "demand_week")
    @ExcelProperty(index = 15, value = "需求周")
    private Integer demandWeek;
    /**
     * 需求数量<br/>Column: [demand_num]
     */
    @Column(value = "demand_num")
    @ExcelProperty(index = 16, value = "需求数量")
    private BigDecimal demandNum;
    /**
     * 预测内数量<br/>Column: [forecast_inner]
     */
    @Column(value = "forecast_inner")
    @ExcelProperty(index = 17, value = "预测内数量")
    private Integer forecastInner;
    /**
     * 弹性内数量
     */
    @Column(value = "forecast_flex")
    @ExcelProperty(index = 18, value = "弹性内数量")
    private Integer forecastFlex;
    /**
     * 预测外数量
     */
    @Column(value = "forecast_outer")
    @ExcelProperty(index = 19, value = "预测外数量")
    private Integer forecastOuter;
    /**
     * 需求预测ID
     */
    @Column(value = "plan_id")
    @ExcelProperty(index = 20, value = "需求预测ID")
    private Integer planId;
    /**
     * 预约单号
     */
    @Column(value = "quota_id")
    @ExcelProperty(index = 21, value = "预约单号")
    private String quotaId;
    /**
     * 资源明细编号
     */
    @Column(value = "resource_code")
    @ExcelProperty(index = 22, value = "资源明细编号")
    private String resourceCode;
    /**
     * 改造单号
     */
    @Column(value = "transform_id")
    @ExcelProperty(index = 23, value = "改造单号")
    private String transformId;
    /**
     * 需求 demand D1 D2 ...
     */
    @Column(value = "order_num")
    @ExcelProperty(index = 24, value = "需求")
    private String orderNum;
    /**
     * 子id
     */
    @Column(value = "demand_sub_id")
    @ExcelProperty(index = 25, value = "子id")
    private String demandSubId;
    /**
     * 供应地域
     */
    @Column(value = "supply_area")
    @ExcelProperty(index = 26, value = "供应地域")
    private String supplyArea;
    /**
     * 供应region
     */
    @Column(value = "supply_region")
    @ExcelProperty(index = 27, value = "供应region")
    private String supplyRegion;
    /**
     * 供应城市
     */
    @Column(value = "supply_city")
    @ExcelProperty(index = 28, value = "供应城市")
    private String supplyCity;
    /**
     * 供应campus
     */
    @Column(value = "supply_campus")
    @ExcelProperty(index = 29, value = "供应campus")
    private String supplyCampus;
    /**
     * 供应设备类型
     */
    @Column(value = "supply_device_type")
    @ExcelProperty(index = 30, value = "供应设备类型")
    private String supplyDeviceType;
    /**
     * 供应机型族
     */
    @Column(value = "supply_device_family")
    @ExcelProperty(index = 31, value = "供应机型族")
    private String supplyDeviceFamily;
    /**
     * 供应日期
     */
    @Column(value = "supply_date")
    @ExcelProperty(index = 32, value = "供应日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT-8")
    private LocalDate supplyDate;
    /**
     * 实际日期
     */
    @Column(value = "actual_date")
    @ExcelProperty(index = 33, value = "实际日期", converter = LocalDateConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT-8")
    private LocalDate actualDate;
    /**
     * 供应数量
     */
    @Column(value = "supply_num")
    @ExcelProperty(index = 34, value = "供应数量")
    private BigDecimal supplyNum;
    /**
     * 固资
     */
    @Column(value = "supply_asset_id")
    @ExcelProperty(index = 35, value = "固资")
    private String supplyAssetId;
    /**
     * 采购明细id
     */
    @Column(value = "supply_purchase_id")
    @ExcelProperty(index = 36, value = "采购明细id")
    private Integer supplyPurchaseId;
    /**
     * 改造执行单号
     */
    @Column(value = "supply_transform_id")
    @ExcelProperty(index = 37, value = "改造执行单号")
    private String supplyTransformId;
    /**
     * 满足方式，采购/退回/库存维度
     */
    @Column(value = "supply_method")
    @ExcelProperty(index = 38, value = "满足方式")
    private String supplyMethod;
    /**
     * 满足类型，S1 S9维度
     */
    @Column(value = "supply_type")
    @ExcelProperty(index = 39, value = "满足类型")
    private String supplyType;
    /**
     * 是否模糊
     */
    @Column(value = "is_fuzzy")
    @ExcelProperty(index = 40, value = "是否模糊")
    private Integer isFuzzy;
    /**
     * 子id
     */
    @Column(value = "supply_sub_id")
    @ExcelProperty(index = 41, value = "子id")
    private String supplySubId;
    /**
     * 数据版本号
     */
    @Column(value = "ver_num")
    @ExcelProperty(index = 42, value = "数据版本号")
    private Long verNum;
    /**
     * 创建时间
     */
    @Column(value = "insert_time")
    @ExcelProperty(index = 43, value = "创建时间")
    private Date insertTime;
    /**
     * 需求 核数&容量
     */
    @Column(value = "demand_op_device_core")
    @ExcelProperty(index = 44, value = "需求 核数&容量")
    private BigDecimal demandOpDeviceCore;
    /**
     * 需求单位
     */
    @Column(value = "demand_unit_name")
    @ExcelProperty(index = 45, value = "需求单位")
    private String demandUnitName;
    /**
     * 需求容量 通过机型族信息计算出来的逻辑数 配合单位能表达一个原子语义
     */
    @Column(value = "demand_op_device_capacity")
    @ExcelProperty(index = 46, value = "需求容量")
    private BigDecimal demandOpDeviceCapacity;
    /**
     * 供应 核数&容量
     */
    @Column(value = "supply_op_device_core")
    @ExcelProperty(index = 47, value = "供应 核数&容量")
    private BigDecimal supplyOpDeviceCore;
    /**
     * 供应单位
     */
    @Column(value = "supply_unit_name")
    @ExcelProperty(index = 48, value = "供应单位")
    private String supplyUnitName;
    /**
     * 供应容量
     */
    @Column(value = "supply_op_device_capacity")
    @ExcelProperty(index = 49, value = "供应容量")
    private BigDecimal supplyOpDeviceCapacity;
    /**
     * 手工修改后采购净预测PG.a
     */
    @Column(value = "purchase_forecast_final")
    @ExcelProperty(index = 50, value = "手工修改后采购净预测PG.a")
    private Float purchaseForecastFinal;

    public static ValItem createValItem(Float num,
            Integer opDeviceCore,
            Integer opDeviceCapacity,
            String unitName) {
        BigDecimal numBigDecimal = num != null ? BigDecimal.valueOf(num) : BigDecimal.ZERO;
        BigDecimal opDeviceCoreBigDecimal = opDeviceCore != null ? BigDecimal.valueOf(opDeviceCore) : BigDecimal.ZERO;
        BigDecimal opDeviceCapacityBigDecimal =
                opDeviceCapacity != null ? BigDecimal.valueOf(opDeviceCapacity) : BigDecimal.ZERO;
        return new ValItem(numBigDecimal, opDeviceCoreBigDecimal, opDeviceCapacityBigDecimal, unitName);
    }

    public static ValItem createValItem(BigDecimal num,
            BigDecimal opDeviceCore,
            BigDecimal opDeviceCapacity,
            String unitName) {
        BigDecimal numBigDecimal = num != null ? num : BigDecimal.ZERO;
        BigDecimal opDeviceCoreBigDecimal = opDeviceCore != null ? opDeviceCore : BigDecimal.ZERO;
        BigDecimal opDeviceCapacityBigDecimal =
                opDeviceCapacity != null ? opDeviceCapacity : BigDecimal.ZERO;
        return new ValItem(numBigDecimal, opDeviceCoreBigDecimal, opDeviceCapacityBigDecimal, unitName);
    }

    public static ValItem createValItem() {
        return new ValItem(BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO, "");
    }

    @Override
    public String getVersion() {
        return String.valueOf(verNum);
    }

    @Override
    public String getDemandArea() {
        return demandArea;
    }

    @Override
    public ValItem getNotHedgeDemand() {
        // 此列数据仅for 与全年进销存对账使用
        // 物理机需求量 内外现在均为空 forecastOuter forecastFlex

        return createValItem();
    }

    @Override
    public ValItem getExeNotMatchDemand() {
        if (orderNum.equals("D1") || orderNum.equals("D2") ||
                orderNum.equals("D4") || orderNum.equals("D5")) {
            return createValItem(demandNum, demandOpDeviceCore, demandOpDeviceCapacity, demandUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getNotExeDemand() {
        // Dc2
        if (orderNum.equals("D9") || orderNum.equals("D8")) {
            if (businessType.equals("云业务") && (
                    projectType.equals("采购buff") || planProduct.equals("腾讯云腾讯待办") || planProduct.equals(
                            "腾讯云机架战略储备")
            )) {
                // 不参与对冲部分
            } else {
                // 台数 核 容量 容量单位
                return createValItem(demandNum, demandOpDeviceCore, demandOpDeviceCapacity, demandUnitName);
            }
        }
        return createValItem();
    }

    @Override
    public ValItem getNotExeNotHedgeDemand() {
        if ((orderNum.equals("Dc3"))) {
            return createValItem(demandNum, demandOpDeviceCore, demandOpDeviceCapacity, demandUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getTransformPlanDemand() {
        if (orderNum.equals("D10")) {
            return createValItem(demandNum, demandOpDeviceCore, demandOpDeviceCapacity, demandUnitName);
        }
        return createValItem();
    }

    private void supplyTypeDefaultValue(String supplyType) {
        if (StringUtils.isBlank(supplyType)) {
            this.supplyType = "";
        }
    }

    private void supplyMethodDefaultValue(String supplyMethod) {
        if (StringUtils.isBlank(supplyMethod)) {
            this.supplyMethod = "";
        }
    }

    @Override
    public ValItem getReturnPlan() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S9")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getInnerStorage() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S1") || supplyType.equals("S3") || supplyType.equals("S10")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getInnerStorageByPurchase() {
        supplyTypeDefaultValue(supplyType);
        supplyMethodDefaultValue(supplyMethod);
        if ((supplyType.equals("S1") || supplyType.equals("S3") || supplyType.equals("S10")) && supplyMethod.equals(
                "PURCHASE")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getInnerStorageByReturn() {
        supplyTypeDefaultValue(supplyType);
        supplyMethodDefaultValue(supplyMethod);
        if ((supplyType.equals("S1") || supplyType.equals("S3") || supplyType.equals("S10")) && supplyMethod.equals(
                "RETURN")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getOuterStorage() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S4") || supplyType.equals("S5")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getTransformPlanStorage() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S13")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    private boolean isFuzzy() {
        // 不参与对冲的无法匹配，直接返回null  Dc3的数据
        if (isFuzzy == null) {
            return false;
        }
        if (!"LEFT".equals(supplyMethod) && isFuzzy != 0) {
            return true;
        }
        return false;
    }

    private boolean isAccurate() {
        // 不参与对冲的无法匹配，直接返回null Dc3的数据
        if (isFuzzy == null) {
            return false;
        }
        if (!"LEFT".equals(supplyMethod) && isFuzzy == 0) {
            return true;
        }
        return false;
    }

    /**
     * 有效对冲 剔除采购的部分
     *
     * @return
     */
    private boolean isEffective() {
        supplyMethodDefaultValue(supplyMethod);
        if (!supplyMethod.startsWith("PURCHASE")) {
            return true;
        }
        return false;
    }

    private boolean isLeft() {
        if ("LEFT".equals(supplyMethod)) {
            return true;
        }
        return false;
    }

    @Override
    public ValItem getExactHedgeByReturn() {
        supplyTypeDefaultValue(supplyType);
        // 精确匹配，返回需求和供给的数量都是相同的
        if (isAccurate() && supplyType.equals("S9")) {
//            if (supplyNum*4supplyOpDeviceCore) {
//                System.out.println(this.toString());
//            }
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    /**
     * supplyMethod 满足方式
     * <p>
     * # INVENTORY：库存
     * # LEFT：剩余的
     * # PURCHASE：采购
     * # PURCHASE EXCHANGE：采购交换
     * # RETURN：退回
     * # TRANSFORM：转化，是互斥的
     *
     * @return
     */
    // OR2("模糊对冲消耗库存-退回",SupplyIndexEnums.SupplyIndexGetter::getFuzzyHedgeInvByReturn),
    @Override
    public ValItem getFuzzyHedgeInvByReturn() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && supplyType.equals("S9")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    // ODr2("模糊对冲满足需求-退回",SupplyIndexEnums.SupplyIndexGetter::getFuzzyHedgeDemandByReturn),
    @Override
    public ValItem getFuzzyHedgeDemandByReturn() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && supplyType.equals("S9")) {
            return createValItem(demandNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getExactHedgeByStorage() {
        supplyTypeDefaultValue(supplyType);
        if (isAccurate() && (supplyType.equals("S1") || supplyType.equals("S3") ||
                supplyType.equals("S10") || supplyType.equals("S4") || supplyType.equals("S5"))) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    // 模糊对冲消耗库存OS2消耗库存，库存取值逻辑
    @Override
    public ValItem getFuzzyHedgeInvByStorage() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && (supplyType.equals("S1") || supplyType.equals("S3") ||
                supplyType.equals("S10") || supplyType.equals("S4") || supplyType.equals("S5"))) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    // Odt2
    @Override
    public ValItem getFuzzyHedgeDemandByStorage() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && (supplyType.equals("S1") || supplyType.equals("S3") ||
                supplyType.equals("S10") || supplyType.equals("S4") || supplyType.equals("S5"))) {
            return createValItem(demandNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getExactHedgeByTransform() {
        supplyTypeDefaultValue(supplyType);
        if (isAccurate() && (supplyType.equals("S13"))) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    // Ost2
    @Override
    public ValItem getFuzzyHedgeInvByTransform() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && (supplyType.equals("S13"))) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    // ODt2
    @Override
    public ValItem getFuzzyHedgeDemandByTransform() {
        supplyTypeDefaultValue(supplyType);
        if (isFuzzy() && (supplyType.equals("S13"))) {
            return createValItem(demandNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getTotalHedgeInv() {
        ValItem OR1 = this.getExactHedgeByReturn();
        ValItem OR2 = this.getFuzzyHedgeInvByReturn();
        ValItem OS1 = this.getExactHedgeByStorage();
        ValItem OS2 = this.getFuzzyHedgeInvByStorage();
        ValItem OSt1 = this.getExactHedgeByTransform();
        ValItem OSt2 = this.getFuzzyHedgeInvByTransform();

        return IndexVal.add(OR1, OR2, OS1, OS2, OSt1, OSt2);
    }

    @Override
    public ValItem getTotalHedgeDemand() {
        ValItem OR1 = this.getExactHedgeByReturn();
        ValItem ODr2 = this.getFuzzyHedgeDemandByReturn();
        ValItem OS1 = this.getExactHedgeByStorage();
        ValItem ODs2 = this.getFuzzyHedgeDemandByStorage();
        ValItem OSt1 = this.getExactHedgeByTransform();
        ValItem ODt2 = this.getFuzzyHedgeDemandByTransform();
        return IndexVal.add(OR1, ODr2, OS1, ODs2, OSt1, ODt2);
    }

    @Override
    public ValItem getReturnLeft() {
        supplyTypeDefaultValue(supplyType);
        // 精确匹配，返回需求和供给的数量都是相同的
        if (isLeft() && supplyType.equals("S9")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getInvLeft() {
        supplyTypeDefaultValue(supplyType);
        // S'=(Si+So)-(OS1+OS2)
        if (isLeft() && (supplyType.equals("S1") || supplyType.equals("S3") || supplyType.equals("S10")
                || supplyType.equals("S4")
                || supplyType.equals("S5"))) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getTransformLeft() {
        supplyTypeDefaultValue(supplyType);
        // 改造剩余St'=St-(OSt1+OSt2)
        if (isLeft() && supplyType.equals("S13")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getTotalLeft() {

        // 默认方法已经实现
        return IndexVal.add(this.getReturnLeft(), this.getInvLeft(), this.getTransformLeft());
    }

    @Override
    public ValItem getPurchaseForecast() {
        // 原始采购净预测PG.b
        if (!isEffective()) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getPurchaseForecastFinal() {

        return SupplyWrapper.super.getPurchaseForecastFinal();
    }

    public void setPurchaseForecastFinal(Float amount) {
        purchaseForecastFinal = amount;
    }

    @Override
    public ValItem getPurchaseForecastGap() {
        return SupplyWrapper.super.getPurchaseForecastGap();
    }

    // 年度独有数据
    @Override
    public ValItem getUnKnown() {
        return createValItem();
    }

    @Override
    public ValItem getBudgetIncrement() {
        if (orderNum.equals("D6")) {
            return createValItem(demandNum, demandOpDeviceCore, demandOpDeviceCapacity, demandUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getBudgetIncrementReturn() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S8")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getConvertedOverallInventory() {
        supplyTypeDefaultValue(supplyType);
        if (supplyType.equals("S7")) {
            return createValItem(supplyNum, supplyOpDeviceCore, supplyOpDeviceCapacity, supplyUnitName);
        }
        return createValItem();
    }

    @Override
    public ValItem getHedgeDemand() {
        // 参与对冲的需求Dc=Dc1+Dc2+Do+Dt
        ValItem Dc1 = this.getExeNotMatchDemand();
        ValItem Dc2 = this.getNotExeDemand();
        ValItem Do = this.getBudgetIncrement();
        ValItem Dt = this.getTransformPlanDemand();
        return IndexVal.add(Dc1, Dc2, Do, Dt);
    }

    @Override
    public String getObsBizType() {
        return businessType;
    }

    @Override
    public String getErpBizType() {
        return businessType;
    }

    @Override
    public String getDemandPlanId() {
        return planId == null ? "" : planId.toString();
    }

    @Override
    public String getDemandQuota() {
        return quotaId;
    }

    @Override
    public String getDemandRsCode() {
        return resourceCode;
    }

    @Override
    public String getDemandTransform() {
        return transformId;
    }

    @Override
    public String getDemandType() {
        return orderNum;
    }

    @Override
    public String getDemandGUPName() {
        // 不支持这个字段
        return SupplyWrapper.super.getDemandGUPName();
    }

    @Override
    public String getDemandCvmDept() {
        return SupplyWrapper.super.getDemandCvmDept();
    }

    @Override
    public String getDemandCvmProduct() {
        return SupplyWrapper.super.getDemandCvmProduct();
    }

    @Override
    public String getDemandInner() {
//        Integer forcaseNum = forecastInner + forecastOuter;
        return "";
    }

    @Override
    public String getDemandMatch() {
        return supplyMethod;
    }

    @Override
    public String getDemandMatchType() {
        return supplyType;
    }

    @Override
    public String getDemandFuzzy() {
        return isFuzzy == null ? "0" : isFuzzy.toString();
    }

    @Override
    public LocalDate getSupplyAccDate() {
        return actualDate;
    }

    @Override
    public String getSupplyAsset() {
        return supplyAssetId;
    }

    @Override
    public String getSupplyGUPName() {
        // 不支持
        return SupplyWrapper.super.getSupplyGUPName();
    }

    @Override
    public String getSupplyTransform() {
        return supplyTransformId;
    }

    @Override
    public String getSupplyPurchase() {
        return supplyPurchaseId == null ? "" : supplyPurchaseId.toString();
    }

    // demandDeviceClass 需求机型大类 供应机型大类都没有
    @Override
    public String getSupplyDeviceClass() {
        return SupplyWrapper.super.getSupplyDeviceClass();
    }

    @Override
    public String getCapacityUnit() {
        return demandUnitName;
    }

    @Override
    public String unImplement() {
        return SupplyWrapper.super.unImplement();
    }

    public LocalDate getDemandDate() {
        if (this.demandDate == null) {
            if (this.demandYear <= 1990 || this.demandMonth == 0 || this.demandMonth > 12) {
                return null; //兼容库存盈余数据没有需求信息
            }
            this.demandDate = LocalDate.of(demandYear, demandMonth, 15);
        }
        return this.demandDate;
    }
}