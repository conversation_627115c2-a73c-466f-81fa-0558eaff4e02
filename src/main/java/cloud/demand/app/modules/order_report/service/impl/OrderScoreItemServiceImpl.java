package cloud.demand.app.modules.order_report.service.impl;

import cloud.demand.app.modules.order_report.entity.report.DwdOrderBaseItemDfDO;
import cloud.demand.app.modules.order_report.entity.report.DwsOrderScoreItemDfDO;
import cloud.demand.app.modules.order_report.entity.report.OrderScoreItem;
import cloud.demand.app.modules.order_report.model.UpdateOrderScore;
import cloud.demand.app.modules.order_report.service.BasOrderScoreLevelService;
import cloud.demand.app.modules.order_report.service.OrderScoreItemService;
import cloud.demand.app.modules.order_report.task.DwdOrderBaseWork;
import cloud.demand.app.modules.order_report.task.DwsOrderScoreWork;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.impl.RedisLock;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class OrderScoreItemServiceImpl implements OrderScoreItemService {
    /** dws 层任务 */
    @Resource
    private DwsOrderScoreWork dwsOrderScoreWork;

    /** dwd 层任务 */
    @Resource
    private DwdOrderBaseWork dwdOrderBaseWork;

    @Resource
    private DBHelper demandDBHelper;

    /** 分级计算 */
    @Resource
    private BasOrderScoreLevelService levelService;

    @Resource
    private RedisHelper redisHelper;

    @Transactional(transactionManager = "demandTransactionManager", rollbackFor = Exception.class)
    @Override
    public void initData(boolean needCheckExist) {
        if (needCheckExist){
            long count = demandDBHelper.getCount(OrderScoreItem.class);
            if (count > 0){
                return;
            }
        }
        String lock = getAndWaitLock();
        try {
            String statTime = DateUtils.formatDate(LocalDate.now());
            // 1. 获取订单基础数据
            List<DwdOrderBaseItemDfDO> dwdData = dwdOrderBaseWork.getOrderBaseData(statTime,null);
            // 2. 转换为 ads数据
            List<DwsOrderScoreItemDfDO> data = ListUtils.transform(dwdData, DwsOrderScoreItemDfDO::transform);
            // 3. 数据处理
            dwsOrderScoreWork.dealData(data);
            // 4. 转为 mysql 类
            List<OrderScoreItem> saveData = ListUtils.transform(data, OrderScoreItem::transform);
            // 4. 删除数据
            demandDBHelper.executeRaw("delete from order_score_item where deleted = 1");
            demandDBHelper.executeRaw("update order_score_item set deleted = 1 where deleted = 0");
            demandDBHelper.insert(saveData);
        }finally {
            releaseLock(lock);
        }
    }

    @Transactional(transactionManager = "demandTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateData(List<String> orderNumber) {
        if (ListUtils.isEmpty(orderNumber)){
            return;
        }
        log.info(String.format("异步更新订单评分，orderNumber：[%s]", orderNumber));
        String lock = getAndWaitLock();
        try {
            String statTime = DateUtils.formatDate(LocalDate.now());
            // 1. 获取订单基础数据
            List<DwdOrderBaseItemDfDO> dwdData = dwdOrderBaseWork.getOrderBaseData(statTime,orderNumber);
            // 2. 转换为 ads数据
            List<DwsOrderScoreItemDfDO> data = ListUtils.transform(dwdData, DwsOrderScoreItemDfDO::transform);
            // 3. 数据处理
            dwsOrderScoreWork.dealData(data);
            // 4. 转为 mysql 类
            List<OrderScoreItem> saveData = ListUtils.transform(data, OrderScoreItem::transform);
            // 5. 删除数据
            demandDBHelper.executeRaw("update order_score_item set deleted = 1 where deleted = 0 and order_number in (?)",orderNumber);
            log.info(String.format("更新订单评分saveData：【%s】", saveData.size()));
            demandDBHelper.insert(saveData);
        }finally {
            releaseLock(lock);
        }
    }

    @Transactional(transactionManager = "demandTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateDateScore(List<UpdateOrderScore> updateOrderScores) {

        checkAndWaitLock(); // 如果有正在初始化或者 updateData 任务这等待执行完成

        Map<String, UpdateOrderScore> dataMap = updateOrderScores.stream()
                .collect(Collectors.toMap(UpdateOrderScore::getDwsId, item -> item));
        List<OrderScoreItem> scoreItems = demandDBHelper.getAll(OrderScoreItem.class,
                "where id in (?) and deleted = 0 for update", dataMap.keySet());
        // 修改得分
        for (OrderScoreItem orderScoreItem : scoreItems) {
            orderScoreItem.setAdjTotalScore(dataMap.get(orderScoreItem.getId()).getAdjTotalScore());
        }
        // 修改分级
        levelService.cleanScoreLevel(scoreItems);
        // 保存数据
        demandDBHelper.update(scoreItems);
    }

    private String getAndWaitLock(){
        String lock = requireLock();
        if (lock == null){
            int waitSeconds = 30; // 默认 30 秒
            while (waitSeconds > 0){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                lock = requireLock();
                if (lock != null){
                    break;
                }
                waitSeconds--;
            }
            if (lock == null){
                throw new BizException("获取锁失败");
            }
        }
        return lock;
    }

    /** 检查并等待锁 */
    private void checkAndWaitLock(){
        boolean lock = checkLock();
        if (lock){
            int waitSeconds = 5; // 默认 5 秒
            while (waitSeconds > 0){
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                lock = checkLock();
                if (!lock){
                    break;
                }
                waitSeconds--;
            }
            if (lock){
                throw new BizException("获取锁失败");
            }
        }
    }

    /** 尝试获取锁 */
    private String requireLock(){
        return redisHelper.requireLock("order_score", "init_data", 60, true);
    }

    /** 校验锁是否存在 */
    private boolean checkLock(){
        return redisHelper.getString(RedisLock.getKey("order_score", "init_data")) != null;
    }

    /** 释放锁 */
    private void releaseLock(String lock){
        if (lock != null){
            redisHelper.releaseLock("order_score", "init_data", lock, true);
        }
    }

}
