package cloud.demand.app.modules.order_report.dto.resp.analysis;

import cloud.demand.app.modules.order_report.enums.IndutryOrderAnalysisFieldEnum;
import cloud.demand.app.modules.order_report.enums.IndutryOrderAnalysisIndexEnum;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import lombok.Data;

/** 元数据 */
@Data

public class IndustryOrderAnalysisMetaData {
    private Map<String,String> dims; // 维度
    private Map<String,String> indexes; // 指标

    public static IndustryOrderAnalysisMetaData build(){
        IndustryOrderAnalysisMetaData ret = new IndustryOrderAnalysisMetaData();
        HashMap<String, String> dims = new LinkedHashMap<>();
        for (IndutryOrderAnalysisFieldEnum value : IndutryOrderAnalysisFieldEnum.values()) {
            dims.put(value.name(), value.getName());
        }
        ret.setDims(dims);
        HashMap<String, String> indexes = new LinkedHashMap<>();
        for (IndutryOrderAnalysisIndexEnum value : IndutryOrderAnalysisIndexEnum.values()) {
            indexes.put(value.name(), value.getName());
        }
        ret.setIndexes(indexes);
        return ret;
    }

    public static void main(String[] args) {
        for (IndutryOrderAnalysisIndexEnum value : IndutryOrderAnalysisIndexEnum.values()) {
            System.out.println("\"" + value.name()+"\",");
        }
    }
}
