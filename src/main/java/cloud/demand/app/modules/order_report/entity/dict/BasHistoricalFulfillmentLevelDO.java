package cloud.demand.app.modules.order_report.entity.dict;

import cloud.demand.app.modules.order_report.enums.score.OrderBillTypeEnum;
import cloud.demand.app.modules.soe.entitiy.dict.BasVersionCommonDO;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;

/** 需求类型得分 */
@Data
@Table("bas_historical_fulfillment_level_score")
public class BasHistoricalFulfillmentLevelDO extends BasVersionCommonDO {
    /** 自增 id<br/>Column: [id] */
    @ExcelIgnore
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 历史评分等级<br/>Column: [historical_fulfillment_level] {@link OrderBillTypeEnum} */
    @Column(value = "historical_fulfillment_level")
    @ExcelProperty(value = "历史评分等级",index = 0)
    private String historicalFulfillmentLevel;

    /** 得分<br/>Column: [score] */
    @Column(value = "score")
    @ContentStyle(dataFormat = 1) // 数字格式
    @ExcelProperty(value = "得分",index = 1)
    private Integer score;

}
