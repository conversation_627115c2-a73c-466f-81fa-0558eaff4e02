package cloud.demand.app.modules.order_report.parse;

import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.soe.entitiy.dict.SoeRegionNameCountryDO;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder.SopWhere;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class CustomhouseTitleWhereParse implements IWhereParser {

    @Override
    public void parse(WhereContent content, SopWhere sopWhere, Object t) {

    }

    @Override
    public void parseSQL(WhereSQL content, SopWhere sopWhere, Object t) {
        Object v = sopWhere.getV();
        if (v instanceof List){
            List<String> titleList = (List<String>) v;
            if (ListUtils.isNotEmpty(titleList)){
                List<String> regionList = new ArrayList<>();
                List<SoeRegionNameCountryDO> region2Country = SpringUtil.getBean(SoeCommonService.class)
                        .getRegion2Country();
                Map<String, List<SoeRegionNameCountryDO>> titleMap = ListUtils.groupBy(region2Country,
                        SoeRegionNameCountryDO::getCustomhouseTitle);
                for (String title : titleList) {
                    List<SoeRegionNameCountryDO> region = titleMap.get(title);
                    if (ListUtils.isNotEmpty(region)){
                        for (SoeRegionNameCountryDO countryDO : region) {
                            regionList.add(countryDO.getRegionName());
                        }
                    }
                }
                content.andIf(ListUtils.isNotEmpty(regionList),sopWhere.getColumnValue()+ " in (?)", regionList);
            }
        }
    }
}
