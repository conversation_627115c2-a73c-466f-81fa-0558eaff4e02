package cloud.demand.app.modules.industry_resource_month_report.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;

@Data
@NoArgsConstructor
public class OrderSeverLevelModel {

    @ExcelProperty(value = "行业部门",index = 0)
    private String industryDept;

    @ExcelProperty(value = "通用客户简称",index = 1)
    private String unCustomerShortName;

    
    @ExcelProperty(value = "订单共识需求核心",index = 2)
    private BigDecimal consensusCore;

    
    @ExcelProperty(value = "订单满足核心",index = 3)
    private BigDecimal matchCore;

    
    @ExcelProperty(value = "履约核心",index = 4)
    private BigDecimal performanceCore;

    
    @ExcelProperty(value = "CVM服务水平(订单满足率)",index = 5)
    private BigDecimal serverLevel;

    
    @ExcelProperty(value = "履约率",index = 6)
    private BigDecimal performanceRate;

    public OrderSeverLevelModel(String industryDept, String unCustomerShortName, BigDecimal consensusCore,
            BigDecimal matchCore, BigDecimal performanceCore, BigDecimal serverLevel, BigDecimal performanceRate) {
        this.industryDept = industryDept;
        this.unCustomerShortName = unCustomerShortName;
        this.consensusCore = ObjectUtils.defaultIfNull(consensusCore, BigDecimal.ZERO);
        this.matchCore = ObjectUtils.defaultIfNull(matchCore, BigDecimal.ZERO);
        this.performanceCore = ObjectUtils.defaultIfNull(performanceCore, BigDecimal.ZERO);
        this.serverLevel = ObjectUtils.defaultIfNull(serverLevel, BigDecimal.ZERO);
        this.performanceRate = ObjectUtils.defaultIfNull(performanceRate, BigDecimal.ZERO);
    }
}
