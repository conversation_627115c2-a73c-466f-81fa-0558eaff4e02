package cloud.demand.app.modules.repo_show.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;

import java.util.List;
import java.util.Objects;

@Getter
public enum ProductUnitEnum {
    CVM("CVM", "万核"),
    METAL("裸金属", "万核"),
    GPU("GPU", "卡"),
    CDB("CDB","TB"),
    COS("COS", "PB"),
    CBS("CBS","PB"),
    NETWORK("网络", "台"),
    EIP("网络EIP", "万个"),
    CLB("网络CLB", "TB");

    private final String type;
    private final String unit;

    ProductUnitEnum(String type,String unit) {
        this.type = type;
        this.unit = unit;
    }

    public static String getUnitByType(String type){
        if (StringUtils.isBlank(type)){
            return "";
        }
        for (ProductUnitEnum value : ProductUnitEnum.values()) {
            if (Objects.equals(value.getType(), type)){
                return value.getUnit();
            }
        }
        return "";
    }

    /**
     * 查询库存晾晒全部产品类型
     */
    public static List<String> getAllProductTypes(){
        List<String> list = Lang.list();
        for (ProductUnitEnum value : ProductUnitEnum.values()) {
            list.add(value.getType());
        }
        return list;
    }


}
