package cloud.demand.app.modules.report_proxy.entity.log;


import cloud.demand.app.modules.report_proxy.model.BeatItem;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.ToString;

import java.util.Date;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import org.mvel2.MVEL;

/**
 * 通用任务日志
 */
@Data
@ToString
@Table("common_task_run_log")
public class CommonTaskRunLogDO {
    /** 自增 id<br/>Column: [id] */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /** 操作命名空间<br/>Column: [namespace] */
    @Column(value = "namespace",maxStringLength = 100)
    private String namespace;

    /** 任务 名称<br/>Column: [name] */
    @Column(value = "name",maxStringLength = 100)
    private String name;

    /** 任务 key<br/>Column: [key] */
    @Column(value = "key",maxStringLength = 200)
    private String key;

    /** 任务 扩展<br/>Column: [ext] */
    @Column(value = "ext",maxStringLength = 400)
    private String ext;

    /** 任务方法<br/>Column: [method] */
    @Column(value = "method",maxStringLength = 300)
    private String method;

    /** 操作参数<br/>Column: [args] */
    @Column(value = "args")
    private String args;

    /** 操作状态：NEW - 初始化，RUNNING - 运行中，FINISH - 完成，ERROR - 失败<br/>Column: [status] */
    @Column(value = "status")
    private String status;

    /** 信息<br/>Column: [msg] */
    @Column(value = "msg", insertValueScript = "''")
    private String msg;

    /** 创建的时间<br/>Column: [create_time] */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /** 心跳时任务运行到的方法 + : + 行数 + ( + 心跳时间 + );<br/>Column: [beat_info] */
    @Column(value = "beat_info", isJSON = true, insertValueScript = "new java.util.ArrayList()")
    private List<BeatItem> beatInfo;

    /** 更新的时间<br/>Column: [update_time] */
    @Column(value = "update_time", setTimeWhenUpdate = true)
    private Date updateTime;

    /** 任务运行 pod 的 ip<br/>Column: [ip] */
    @Column(value = "ip", maxStringLength = 20)
    private String ip;

    /** 链路 id<br/>Column: [trace_id] */
    @Column(value = "trace_id", maxStringLength = 100)
    private String traceId;
}
