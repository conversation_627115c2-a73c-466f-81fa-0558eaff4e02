package cloud.demand.app.modules.report_proxy.modules.task_run_log.service;

import cloud.demand.app.modules.report_proxy.dto.req.TaskRunLogReq;
import cloud.demand.app.modules.report_proxy.dto.resp.MyDirectedGraph;
import cloud.demand.app.modules.report_proxy.dto.resp.TaskRunLogResp;
import cloud.demand.app.modules.report_proxy.dto.resp.TaskRunLogResp.TableVertex;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunLogDO;
import cloud.demand.app.modules.report_proxy.entity.log.CommonTaskRunSqlDO;
import cloud.demand.app.modules.report_proxy.entity.vo.TaskRunLogVO;
import java.util.List;
import java.util.Set;
import org.apache.calcite.util.graph.DefaultEdge;

/** 公共任务运行日志 */
public interface CommonTaskRunLogService {

    /** 写入 or 更新日志（注意：update 只会更新部分字段） */
    public void insertOrUpdate(CommonTaskRunLogDO log);

    /** 写入 sql 信息 */
    public void insertSql(List<CommonTaskRunSqlDO> sqlDOList);

    /** 容器终止，任务状态改为 ERROR，描述改为：容器终止 */
    void shutDown(Set<Long> runningIds);

    /** 查询列表 */
    List<TaskRunLogVO> getList(TaskRunLogReq req);

    /** 获取任务运行图 */
    TaskRunLogResp getGraphWithData(TaskRunLogReq req);

    MyDirectedGraph<TableVertex, DefaultEdge> getGraphWithData(List<TaskRunLogVO> list);
}
