package cloud.demand.app.modules.plan_detail.service.cbs;

import cloud.demand.app.modules.plan_detail.enums.PlanDetailIndicator;
import lombok.Getter;

/**
 * planDetail表CBS的指标枚举
 */
@Getter
public enum PlanDetailCBSIndicatorEnum implements PlanDetailIndicator {

    //  库存
    INVENTORY_ONLINE_GOOD("c11", "存", "线上库存", "好料", "c11=c1-c12"),
    INVENTORY_ONLINE_BAD("c2", "存", "线上库存", "差料", ""),
    INVENTORY_ONLINE_UGLY("c3", "存", "线上库存", "呆料", ""),
    INVENTORY_OFFLINE("c12", "存", "线下库存", "线下可售卖", ""),
    INVENTORY_GOOD("c1", "存", "总库存", "好料", ""),

    // 销售
    SALE_LH("b1", "销", "lighthouse规模", "lighthouse规模", ""),
    SALE_OUT("b2", "销", "外部计费规模", "外部计费规模", ""),
    SALE_IN("b3", "销", "内部售卖规模", "内部售卖规模", ""),

    // 其他
    TOTAL_CAN_USE_CAP("v", "其他", "可用总容量", "可用总容量", ""),
    TOTAL_CAN_SOLD_CAP("d", "其他", "可售总容量", "可售总容量", ""),
    OVER_SOLD("j", "其他", "超卖规模", "超卖规模", "j=v-d"),
    TOTAL_REPO("e", "其他", "仓库总容量", "仓库总容量", "")
    ;

    /** 指标代号 */
    private final String code;
    /** 指标名称 */
    private final String name;
    /** 指标大类 */
    private final String category;
    /** 指标子类 */
    private final String subCategory;
    /** 指标计算公式 */
    private final String formula;

    PlanDetailCBSIndicatorEnum(String code, String category, String subCategory, String name, String formula) {
        this.code = code;
        this.category = category;
        this.subCategory = subCategory;
        this.name = name;
        this.formula = formula;
    }
}
