package cloud.demand.app.modules.rrp_remake.enums;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.modules.rrp_remake.config.DynamicProperties;
import com.pugwoo.wooutils.collect.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.function.Function;

/*
    获取以规划产品为单元的需求流程中，某一步的审批人函数集合
 */
public class GetApproverFunctions {

    // 无审批人
    public static final Function<String, String> noApprovers = (planProduct) -> "";

    public static final Function<String, String> proApprovers = (planProduct) -> handler(AuthRoleEnum.PRODUCT_SUBMITER, planProduct);;
    // 资源总监审核人
    public static final Function<String, String> comdGMApprovers = (planProduct) -> handler(AuthRoleEnum.COMD_GM_APPROVER, planProduct);
    // 产品审核人
    public static final Function<String, String> productApprovers = (planProduct) -> handler(AuthRoleEnum.PRODUCT_APPROVER, planProduct);
    // 资源组审批人
    public static final Function<String, String> comdApprovers = (planProduct) -> handler(AuthRoleEnum.COMD_APPROVER);
    // 某个步骤的处理人也放在这里了，作为通用函数

    public static String handler(AuthRoleEnum authRoleEnum) {
        return handler(authRoleEnum, null);
    }

    public static String handler(AuthRoleEnum authRoleEnum, String planProduct) {
        String sql = "select distinct user from rrp_permission where role = ?";
        Object[] args;
        if (StringUtils.isNotBlank(planProduct)) {
            sql = sql + " and product = ?";
            args = new Object[2];
            args[1] = planProduct;
        } else {
            args = new Object[1];
        }
        args[0] = authRoleEnum.getEn();
        List<String> users = DBList.rrpDBHelper.getRaw(String.class, sql, args);
        if (ListUtils.isNotEmpty(users)) {
            return String.join(";", users);
        } else {
            return DynamicProperties.defaultApprovers();
        }
    }
}

