package cloud.demand.app.modules.cvm_cbs.entity;

import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @since 2024/8/19 19:35
 */
@Data
@Table("dws_zi_yan_cbs_stock_diff_df")
public class ZiYanCbsStockDiffDO{

    /**
     * 年月
     */
    @Column("year_month")
    private String yearMonth;

    /**
     * 日期
     */
    @Column("version")
    private String version;

    /**
     * 计费量
     */
    @Column("amount")
    private BigDecimal amount;

    /**
     * 境内外
     */
    @Column("customhouse_title")
    private String customhouseTitle;

    /**
     * 地域
     */
    @Column("region_name")
    private String regionName;

    /**
     * 可用区
     */
    @Column("zone_name")
    private String zoneName;

    /**
     * 云盘类型
     */
    @Column("volume_type")
    private String volumeType;

    /**
     * 差异量
     */
    @Column("diff_amount")
    private BigDecimal diffAmount;

    public String getKey(String version) {
        if (StringUtils.isBlank(version)) {
            return StringUtils.joinWith("@", this.yearMonth, this.version, this.customhouseTitle, this.regionName, this.zoneName, this.volumeType);
        }
        LocalDate versionDate = LocalDate.parse(version);
        String yearMonth = SoeCommonUtils.getYearMonth(versionDate.getYear(), versionDate.getMonth().getValue());
        return StringUtils.joinWith("@", yearMonth, version, this.customhouseTitle, this.regionName, this.zoneName, this.volumeType);
    }

}
