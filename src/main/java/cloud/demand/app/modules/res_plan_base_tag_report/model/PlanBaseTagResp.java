package cloud.demand.app.modules.res_plan_base_tag_report.model;

import cloud.demand.app.modules.res_plan_base_tag_report.entity.ResPlanBaseTagReportDO;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PlanBaseTagResp {

    // 年月
    @ExcelProperty(value = "年月")
    private String yearMonth;

    // 周
    @ExcelProperty(value = "周")
    private Integer week;

    // 境内外
    @ExcelProperty(value = "境内外")
    private String customhouseTitle;

    // 国家
    @ExcelProperty(value = "国家")
    private String country;

    // 项目类型
    @ExcelProperty(value = "项目类型")
    private String projectType;

    // 混合项目类型
    @ExcelProperty(value = "混合项目类型")
    private String mixProjectType;

    // 业务大类
    @ExcelProperty(value = "业务大类")
    private String bizClass;

    // 规划产品
    @ExcelProperty(value = "规划产品")
    private String planProduct;

    // 设备类型
    @ExcelProperty(value = "设备类型")
    private String deviceType;

    // 预测基准数
    @ExcelProperty(value = "预测基准数(台数)")
    private Integer tagNum;

    // W13算法剩余预测量(台数)
    @ExcelProperty(value = "W13算法剩余预测量(台数)")
    private Integer w13RemainNum;

    // W13Plus算法剩余预测量(台数)
    @ExcelProperty(value = "W13Plus算法剩余预测量(台数)")
    private Integer w13PlusRemainNum;

    // 未下单量
    @ExcelProperty(value = "未下单量(台数)")
    private Integer noExecuteNum;

    // 未执行预测，周预测内
    @ExcelProperty(value = "未执行预测，周预测内(台数)")
    private Integer noExecuteW13InnerNum;

    // 未执行预测，周预测外
    @ExcelProperty(value = "未执行预测，周预测外(台数)")
    private Integer noExecuteW13OuterNum;

    // 未执行预测，周plus预测内
    @ExcelProperty(value = "未执行预测，周plus预测内(台数)")
    private Integer noExecuteW13PlusInnerNum;

    // 未执行预测，周plus预测外
    @ExcelProperty(value = "未执行预测，周plus预测外(台数)")
    private Integer noExecuteW13PlusOuterNum;

    // 已下订单量
    @ExcelProperty(value = "已下订单量(台数)")
    private Integer executeOrderNum;

    @ExcelProperty(value = "已下订单，周预测内(台数)")
    private Integer executeOrderW13InnerNum;

    @ExcelProperty(value = "已下订单，周预测外(台数)")
    private Integer executeOrderW13OuterNum;

    @ExcelProperty(value = "已下订单，周plus预测内(台数)")
    private Integer executeOrderW13PlusInnerNum;

    @ExcelProperty(value = "已下订单，周plus预测外(台数)")
    private Integer executeOrderW13PlusOuterNum;

    // 对应核心数
    @ExcelProperty(value = "预测基准数(核数)")
    private Integer tagCore;

    @ExcelProperty(value = "W13算法剩余预测量(核数)")
    private Integer w13RemainCore;

    @ExcelProperty(value = "W13Plus算法剩余预测量(核数)")
    private Integer w13PlusRemainCore;

    @ExcelProperty(value = "未下单量(核数)")
    private Integer noExecuteCore;

    @ExcelProperty(value = "未执行预测，周预测内(核数)")
    private Integer noExecuteW13InnerCore;

    @ExcelProperty(value = "未执行预测，周预测外(核数)")
    private Integer noExecuteW13OuterCore;

    @ExcelProperty(value = "未执行预测，周plus预测内(核数)")
    private Integer noExecuteW13PlusInnerCore;

    @ExcelProperty(value = "未执行预测，周plus预测外(核数)")
    private Integer noExecuteW13PlusOuterCore;

    @ExcelProperty(value = "已下订单量(核数)")
    private Integer executeOrderCore;

    @ExcelProperty(value = "已下订单，周预测内(核数)")
    private Integer executeOrderW13InnerCore;

    @ExcelProperty(value = "已下订单，周预测外(核数)")
    private Integer executeOrderW13OuterCore;

    @ExcelProperty(value = "已下订单，周plus预测内(核数)")
    private Integer executeOrderW13PlusInnerCore;

    @ExcelProperty(value = "已下订单，周plus预测外(核数)")
    private Integer executeOrderW13PlusOuterCore;

    @NotNull
    public static PlanBaseTagResp transform(ResPlanBaseTagReportDO reportDO, PlanBaseTagReq req) {
        PlanBaseTagResp resp = new PlanBaseTagResp();
        if (req.getExecuteCaliber().equals("订单期望时间") && reportDO.getDataIndex().equals("EXECUTE_ORDER")) {
            resp.setYearMonth(reportDO.getExpectYearMonth());
            resp.setWeek(reportDO.getExpectWeek());
        } else {
            resp.setYearMonth(reportDO.getDemandYearMonth());
            resp.setWeek(reportDO.getDemandWeek());
        }
        if (reportDO.getCountry().equals("中国内地")) {
            resp.setCustomhouseTitle("境内");
        } else {
            resp.setCustomhouseTitle("境外");
        }
        resp.setCountry(reportDO.getCountry());
        resp.setProjectType(reportDO.getProjectType());
        resp.setMixProjectType(reportDO.getMixProjectType());
        resp.setBizClass(reportDO.getBizClass());
        resp.setPlanProduct(reportDO.getPlanProduct());
        resp.setDeviceType(reportDO.getDeviceType());
        if (reportDO.getDataIndex().equals("BASE_DEMAND")) {
            resp.setTagNum(reportDO.getTagNum());
            resp.setW13RemainNum(reportDO.getW13RemainNum());
            resp.setW13PlusRemainNum(reportDO.getW13PlusRemainNum());
            resp.setTagCore(reportDO.getTagCore());
            resp.setW13RemainCore(reportDO.getW13RemainCore());
            resp.setW13PlusRemainCore(reportDO.getW13PlusRemainCore());
        }

        if (reportDO.getDataIndex().equals("NO_EXECUTE_DEMAND")) {
            resp.setNoExecuteNum(reportDO.getNoExecuteNum());
            resp.setNoExecuteW13InnerNum(reportDO.getW13InnerNum());
            resp.setNoExecuteW13OuterNum(reportDO.getW13OuterNum());
            resp.setNoExecuteW13PlusInnerNum(reportDO.getW13PlusInnerNum());
            resp.setNoExecuteW13PlusOuterNum(reportDO.getW13PlusOuterNum());
            resp.setNoExecuteCore(reportDO.getNoExecuteCore());
            resp.setNoExecuteW13InnerCore(reportDO.getW13InnerCore());
            resp.setNoExecuteW13OuterCore(reportDO.getW13OuterCore());
            resp.setNoExecuteW13PlusInnerCore(reportDO.getW13PlusInnerCore());
            resp.setNoExecuteW13PlusOuterCore(reportDO.getW13PlusOuterCore());
        }
        if (reportDO.getDataIndex().equals("EXECUTE_ORDER")) {
            resp.setExecuteOrderNum(reportDO.getOrderNum());
            resp.setExecuteOrderW13InnerNum(reportDO.getW13InnerNum());
            resp.setExecuteOrderW13OuterNum(reportDO.getW13OuterNum());
            resp.setExecuteOrderW13PlusInnerNum(reportDO.getW13PlusInnerNum());
            resp.setExecuteOrderW13PlusOuterNum(reportDO.getW13PlusOuterNum());
            resp.setExecuteOrderCore(reportDO.getOrderCore());
            resp.setExecuteOrderW13InnerCore(reportDO.getW13InnerCore());
            resp.setExecuteOrderW13OuterCore(reportDO.getW13OuterCore());
            resp.setExecuteOrderW13PlusInnerCore(reportDO.getW13PlusInnerCore());
            resp.setExecuteOrderW13PlusOuterCore(reportDO.getW13PlusOuterCore());
        }
        return resp;
    }
}
