package cloud.demand.app.modules.sop_util.enums.supply;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.function.Function;

/**
 * 库存健康
 */

@Getter
@AllArgsConstructor
public enum InventoryHealthEnum {

    // 低于安全库存：周转天数小于20的库存数据
    LOW("低于安全库存", v -> v == null || v.compareTo(BigDecimal.valueOf(20)) < 0),

    // 健康：周转天数介于20～30的库存数据
    HEALTH("健康", v -> v != null && v.compareTo(BigDecimal.valueOf(20)) >= 0
            && v.compareTo(BigDecimal.valueOf(30)) <= 0),

    // 冗余：周转天数>30的库存数据
    REDUNDANCY("冗余", v -> v != null && v.compareTo(BigDecimal.valueOf(30)) > 0),

    ;

    private final String name;

    private final Function<BigDecimal, Boolean> func;

    public static String getByTurnover(BigDecimal v){
        for (InventoryHealthEnum value : values()) {
            if (value.func.apply(v)){
                return value.getName();
            }
        }
        return null;
    }
}
