package cloud.demand.app.modules.sop_util.process.store.register;

import cloud.demand.app.modules.sop.exception.SopException;
import cloud.demand.app.modules.sop_util.anno.StoreRegister;
import cloud.demand.app.modules.sop_util.anno.StoreRegisterClient;
import cloud.demand.app.modules.sop_util.domain.DimReq;
import cloud.demand.app.modules.sop_util.domain.ITimePeriodReq;
import cloud.demand.app.modules.sop_util.domain.IVersionReq;
import cloud.demand.app.modules.sop_util.domain.demand.ReportDemandReq;
import cloud.demand.app.modules.sop_util.enums.StoreNameEnum;
import cloud.demand.app.modules.sop_util.model.demand.SopDemandReturnReq;
import cloud.demand.app.modules.sop_util.model.supply.VersionInfo;
import cloud.demand.app.modules.sop_util.model.version.SopUtilVersionItem;
import cloud.demand.app.modules.sop_util.process.store.IDBData;
import cloud.demand.app.modules.sop_util.process.store.item.BudgetItem;
import cloud.demand.app.modules.sop_util.service.SopUtilCommonService;
import cloud.demand.app.modules.sop_util.utils.CommonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/** 初始化的步骤仓库 */
@StoreRegisterClient
@Component
public class InitComponent {
    @Resource
    protected SopUtilCommonService commonService;

    /**
     * 初始化版本信息
     */
    @StoreRegister(storeName = StoreNameEnum.initVersion)
    public List<IDBData> initVersion(Map<String, Object> params) {
        // 接口入参
        IVersionReq req = (IVersionReq) params.get("req");
        // 版本信息
        VersionInfo info = (VersionInfo)params.get("info");
        if (StringUtils.isBlank(req.getDeviceVersion())){
            SopUtilVersionItem version = commonService.getVersion(req.getVersion());
            if (version == null){
                throw new SopException("没有版本数据，无法查询");
            }
            info.setDeviceVersion(version.getDeviceVersion());
            info.setDeviceYearMonth(CommonUtils.toYearMonth(version.getDeviceVersionDateTime()));
        }else {
            info.setDeviceVersion(req.getDeviceVersion());
            Map<String, LocalDateTime> versionDate = commonService.getVersionDate();
            LocalDateTime dateTime = versionDate.get(info.getDeviceVersion());
            if (dateTime==null){
                throw new SopException("版本号异常，无法查询");
            }
            info.setDeviceYearMonth(CommonUtils.toYearMonth(dateTime));
        }
        // 如果req能获取时间范围，则使用req入参提供的时间范围
        if (req instanceof ITimePeriodReq){
            ITimePeriodReq timePeriodReq = (ITimePeriodReq)req;
            SopDemandReturnReq.TimePeriod dateRange = timePeriodReq.getDateRange();
            if (dateRange != null){
                info.setDateRange(dateRange);
            }
        }
        return null;
    }
}
