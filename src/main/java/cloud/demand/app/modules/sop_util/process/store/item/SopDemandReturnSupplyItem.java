package cloud.demand.app.modules.sop_util.process.store.item;

import cloud.demand.app.modules.sop_util.entity.SopDemandReturnSupplyDO;
import cloud.demand.app.modules.sop_util.process.model.IIndexData;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/** sop新增退回(设备类型-物理机和实例规格-cvm) */
@Data
public class SopDemandReturnSupplyItem extends SopDemandReturnItem implements IIndexData {

    /** 资源类型 */
    private String resType;

    /** cvm实例规格 */
    private String cvmGinsType;

    /** 物理机设备类型 */
    private String phyDeviceType;

    /** 分摊核数 */
    private BigDecimal apportionCoreNum;

    public static SopDemandReturnSupplyItem transform(SopDemandReturnSupplyDO supplyDO){
        SopDemandReturnSupplyItem ret = new SopDemandReturnSupplyItem();
        ret.setResType(supplyDO.getResType());
        ret.setCvmGinsType(supplyDO.getCvmGinsType());
        ret.setPhyDeviceType(supplyDO.getPhyDeviceType());
        ret.setUtilBusinessType(supplyDO.getUtilBusinessType());
        ret.setSopBigFamily(supplyDO.getSopBigFamily());
        ret.setYearMonth(supplyDO.getYearMonth());
        ret.setCoreNum(supplyDO.getCoreNum());
        return ret;
    }

    @Override
    public Map<String, Object> getDimMap_() {
        Map<String, Object> dimMap = super.getDimMap_();
        if (!dimMap.containsKey("resType")){
            dimMap.put("resType",resType);
            dimMap.put("cvmGinsType",cvmGinsType);
            dimMap.put("phyDeviceType",phyDeviceType);
            dimMap.put("deviceType",phyDeviceType);
        }
        return dimMap;
    }
}
