package cloud.demand.app.modules.sop_util.model.version;

import cloud.demand.app.modules.sop_util.domain.demand.ReportDemandVersionResp;
import lombok.Data;

import java.time.LocalDateTime;

/** sop版本 */
@Data
public class SopUtilVersionItem {
    /**
     * 标签
     */
    private String label;

    /**
     * cvm版本
     */
    private String cvmVersion;

    /**
     * cvm的版本时间
     */
    private LocalDateTime cvmVersionDateTime;

    /**
     * 物理机版本
     */
    private String deviceVersion;

    /**
     * 物理机版本时间
     */
    private LocalDateTime deviceVersionDateTime;

    public static SopUtilVersionItem transform(ReportDemandVersionResp.Item item){
        SopUtilVersionItem ret = new SopUtilVersionItem();
        ret.setLabel(item.getLabel());
        ret.setCvmVersion(item.getCvmVersion());
        ret.setCvmVersionDateTime(item.getCvmVersionDateTime());
        ret.setDeviceVersion(item.getDeviceVersion());
        ret.setDeviceVersionDateTime(item.getDeviceVersionDateTime());
        return ret;
    }
}
