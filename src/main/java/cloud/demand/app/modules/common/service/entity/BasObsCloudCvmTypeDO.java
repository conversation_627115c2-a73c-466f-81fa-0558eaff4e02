package cloud.demand.app.modules.common.service.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("bas_obs_cloud_cvm_type")
public class BasObsCloudCvmTypeDO {

    @Column(value = "CvmInstanceGroup")
    private String cvmInstanceGroup;

    /** 实例类型<br/>Column: [CvmInstanceType] */
    @Column(value = "CvmInstanceType")
    private String cvmInstanceType;

    /** 实例类型简称<br/>Column: [CvmInstanceTypeCode] */
    @Column(value = "CvmInstanceTypeCode")
    private String cvmInstanceTypeCode;

    /** 实例规格<br/>Column: [CvmInstanceModel] */
    @Column(value = "CvmInstanceModel")
    private String cvmInstanceModel;

    /** Cpu核数(核)<br/>Column: [CpuAmount] */
    @Column(value = "CpuAmount")
    private Integer cpuAmount;

    /** 内存量(G)<br/>Column: [RamAmount] */
    @Column(value = "RamAmount")
    private Integer ramAmount;

    /** 核心类型: 1:小核心 cpu个数 <=12 , 2:中核心 cpu个数 12<    <=36, 3:大核心 cpu个数  > 36<br/>Column: [CoreType] */
    @Column(value = "CoreType")
    private Integer coreType;

    /**
     * 厂商类型
     */
    @Column(value  ="FirmType")
    private String firmType;


    /** 生产对应标准母机机型<br/>Column: [HostDeviceClass] */
    @Column(value = "HostDeviceClass")
    private String hostDeviceClass;


    @Column(value = "GpuAmount")
    private BigDecimal gpuNum;
    @Column(value = "GpuType")
    private String gpuType;


    /**
     * 单母机可生产该规格CVM个数
     */
    @Column(value = "HostProduceNum")
    private Integer HostProduceNum;



}