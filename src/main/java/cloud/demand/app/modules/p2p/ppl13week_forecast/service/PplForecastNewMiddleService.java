package cloud.demand.app.modules.p2p.ppl13week_forecast.service;

import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitMiddleDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;

import java.util.List;

/**
 * 新腰部模型预测相关服务
 */
public interface PplForecastNewMiddleService {

    /**
     * 查询最后一次的预测任务
     * @param isIndustry true外部客户，false内部客户
     * @param isKeepUin 是否保留客户维度，如果保留uin，即不去重；如果不保留，即去重
     * @return task
     */
    PplForecastPredictTaskDO getLastIndustryPredictTask(boolean isIndustry, boolean isKeepUin);

    /**
     * 查询31个大客户+毛刺的预测任务id，最后1个任务
     */
    PplForecastPredictTaskDO getLastHeadCustomerWithSpikePredictTask(boolean isIndustry);

    /**
     * 查询31个大客户+毛刺的预测任务列表
     */
    List<PplForecastPredictTaskDO> getHeadCustomerWithSpikePredictTask(boolean isIndustry);

    /**
     * 查询最后一次的预测任务
     * @param isIndustry true外部客户，false内部客户
     * @param isKeepUin 是否保留客户维度，如果保留uin，即不去重；如果不保留，即去重
     * @return String
     */
    String getCategoryPrefix(boolean isIndustry, boolean isKeepUin);


    /**
     * 查询拆分之后的预测结果
     * @param taskId 任务id
     * @return 预测结果
     */
    List<PplForecastPredictResultSplitMiddleDO> getForecastResult(List<Long> taskId,
                                                                  String industryDept,
                                                                  String startYearMonth,
                                                                  String endYearMonth);

}
