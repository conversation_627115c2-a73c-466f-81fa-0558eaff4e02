package cloud.demand.app.modules.p2p.ppl13week.constant;

import java.util.Objects;

public enum PplInnerProcessVersionStartConsensusEnum {

    NOT_CONSENSUS(0, "未共识"),

    IN_CONSENSUS(1, "共识中"),

    OVER_CONSENSUS(2, "已结束");

    private final int code;

    private final String name;

    PplInnerProcessVersionStartConsensusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static PplInnerProcessVersionStartConsensusEnum getByCode(Integer code) {
        for (PplInnerProcessVersionStartConsensusEnum e : PplInnerProcessVersionStartConsensusEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

}
