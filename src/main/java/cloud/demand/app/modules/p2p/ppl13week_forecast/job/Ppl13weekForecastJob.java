package cloud.demand.app.modules.p2p.ppl13week_forecast.job;

import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import cloud.demand.app.modules.forecast_compute.model.ForecastAlgorithms;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskOutputVersionDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastGroupDimsEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCdbMemGroupEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCustomerScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastProductEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastResourcePoolEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekInputService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekOpsService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekPredictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekSplitService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13WeekInputServiceImpl;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Lang;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.function.BiConsumer;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_CBS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_CDB;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EKS;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastOutputVersionTypeEnum.SPLIT_EMR;

@Slf4j
@Service
public class Ppl13weekForecastJob {

    @Autowired
    private Ppl13weekPredictService forecastService;
    @Resource
    private Ppl13weekInputService ppl13weekInputService;
    @Resource
    private Ppl13WeekInputServiceImpl ppl13WeekInputService;
    @Resource
    private Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    private Ppl13weekSplitService ppl13weekSplitService;

    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private DBHelper ckcldyuntiDBHelper;

    private Ppl13weekOpsService ppl13weekOpsService;

    /**
     * 同步底层预测任务的预测结果，每分钟定时同步一次
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0/1 * * * ?")
    public void run() {
        List<Long> taskIds = forecastService.syncPredictResult();

        if (ListUtils.isEmpty(taskIds)) {
            return;
        }

        // 对于腰部的任务，自动拆分需求
        List<PplForecastPredictTaskDO> taskDOs = ppl13weekCommonDataAccess.getPplForecastPredictTaskDO(taskIds, false);
        List<PplForecastPredictTaskDO> middleTasks = ListUtils.filter(taskDOs,
                o -> Ppl13weekForecastCustomerScopeEnum.MIDDLE.getCode().equals(o.getCustomerScope())
                        && Ppl13weekForecastResourcePoolEnum.PUBLIC.getCode().equals(o.getResourcePool())
                        || isStartWiths(o.getCategory(), "方案855", "方案865", "方案875"));
        for (PplForecastPredictTaskDO taskDO : middleTasks) {
            try {
                PplForecastPredictTaskOutputVersionDO outputVersion = ppl13weekSplitService.createOutputVersion(
                        taskDO.getId(), "腰部拆分需求",
                        Ppl13weekForecastOutputVersionTypeEnum.SPLIT_FOR_MIDDLE, "拆分成行业部门");

                ppl13weekSplitService.splitCloudMiddle(taskDO.getId(), outputVersion.getId());
                ppl13weekSplitService.updateStatus(outputVersion.getId(), "SPLIT", "");
            } catch (Exception e) {
                log.error("auto split middle forecast fail, taskId:{}", taskDO.getId(), e);
            }
        }

        splitProducts(taskDOs);

        // 如果有CBS任务，尝试同步头部客户
        List<PplForecastPredictTaskDO> cbsTasks
                = ListUtils.filter(taskDOs, o -> isStartWiths(o.getCategory(), "方案540"));
        if (ListUtils.isNotEmpty(cbsTasks)) {
            ppl13weekOpsService.syncCbsHeaderCustomer();
        }
    }

    @AllArgsConstructor
    static class SplitParams {

        String categoryPrefix;
        Ppl13weekForecastOutputVersionTypeEnum productEnum;
        String versionDesc;
        BiConsumer<Long, Long> func;
    }

    final static String splitDesc = "预测完成自动拆分,下发PPL的数据来源";
    List<SplitParams> splitParams = ListUtils.of();
    @PostConstruct
    public void init() {
        splitParams = ListUtils.of(
                new SplitParams("方案510", SPLIT_EMR, splitDesc, ppl13weekSplitService::splitEmrLongTail),
                new SplitParams("方案520", SPLIT_EKS, splitDesc, ppl13weekSplitService::splitEksLongTail),
                new SplitParams("方案530", SPLIT_CDB, splitDesc, ppl13weekSplitService::splitCdbLongTail),
                new SplitParams("方案540", SPLIT_CBS, splitDesc, ppl13weekSplitService::splitCbsLongTail)
        );
    }

    private void splitProducts(List<PplForecastPredictTaskDO> taskDOs) {
        for (SplitParams splitParam : splitParams) {
            splitByProduct(taskDOs, splitParam);
        }
    }

    private void splitByProduct(List<PplForecastPredictTaskDO> taskDOs, SplitParams params) {
        List<PplForecastPredictTaskDO> filterTasks
                = ListUtils.filter(taskDOs, o -> isStartWiths(o.getCategory(), params.categoryPrefix));
        for (PplForecastPredictTaskDO oneTask : filterTasks) {
            PplForecastPredictTaskOutputVersionDO outputVersion
                    = ppl13weekSplitService.createOutputVersion(
                    oneTask.getId(), params.productEnum.getName(), params.productEnum, params.versionDesc);
            try {
                params.func.accept(oneTask.getId(), outputVersion.getId());
                ppl13weekSplitService.updateStatus(outputVersion.getId(), "SPLIT", "");
            } catch (Exception e) {
                log.error("auto split middle forecast fail, taskId:{}", oneTask.getId(), e);
                ppl13weekSplitService.updateStatus(outputVersion.getId(), "ERROR", e.getMessage());
            }
        }
    }

    private static boolean isStartWiths(String str, String... prefix) {
        if (str == null || prefix == null) {
            return false;
        }
        for (String p : prefix) {
            if (str.startsWith(p)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 行业数据看板的最新数据
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 8 * * ?")
    public void genLatestInputDetailForMrpData() {
        log.info("check  geLatestInputDetailData");
        ppl13weekInputService.genLatestInputDetailForMrpData();
    }

    /**
     * 检查日规模表最新的数据是否以及生成
     */
    private boolean checkScaleData() {
        BigDecimal curCore = ckcldStdCrpDBHelper.getRawOne(BigDecimal.class,
                "select sum(cur_bill_service_core) from dwd_txy_scale_df where stat_time=?",
                LocalDate.now().minusDays(1));
        return curCore != null && curCore.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 检查自研cvm底表数据是否有了
     *
     * @return
     */
    private boolean checkZiyanCvmData() {
        Long count = ckcldyuntiDBHelper.getRawOne(Long.class,
                "select count(*) from dwd_yunti_cvm_demand_forecast_item_df where stat_date=?",
                LocalDate.now().minusDays(1));
        return count != null && count.compareTo(0L) > 0;
    }

    /**
     * 8大产品的预测方案
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 50 8 1 * ?")
    @TaskLog(taskName = "createMonthArimaxTaskForEightProduct")
    public void createMonthArimaxTaskForEightProduct() {
        if (!checkScaleData()) {
            log.error("daily scale data not ready, end job");
            return;
        }

        {
            String category = "方案510：EMR中长尾-月切保留客户维度(机型规格)-机型收敛-包弹-全部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAXL,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            String retAlArgs = "0,1,3,0.2"; // 退回仍采用0,1,3，因为0,1,6的趋势过大
            Integer customSpikeThreshold = 2000; // 【重要】毛刺阈值
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.EMR,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, null, true, null, null,
                    retAlArgs, null, null, null, customSpikeThreshold, null);
        }

        {
            String category = "方案520：EKS中长尾-月切保留客户维度(机型规格)-机型收敛-全部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAXL,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            String retAlArgs = "0,1,3,0.2"; // 退回仍采用0,1,3，因为0,1,6的趋势过大
            Integer customSpikeThreshold = 1000; // 【重要】毛刺阈值
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.EKS,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, null, true, null, null,
                    retAlArgs, null, null, null, customSpikeThreshold, null);
        }

        {
            String category = "方案530：CDB中长尾-月切保留客户维度-内存收敛-全部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAXL,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            String retAlArgs = "0,1,3,0.2"; // 退回仍采用0,1,3，因为0,1,6的趋势过大
            Integer customSpikeThreshold = 2000; // 【重要】毛刺阈值，单位GB
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CDB,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, false, null, true, null, null,
                    retAlArgs, null, null, null, customSpikeThreshold, Ppl13weekForecastCdbMemGroupEnum.SMALL_OR_BIG);
        }

        {
            String category = "方案540：CBS中长尾-月切保留客户维度-内存收敛-全部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAXL,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            String retAlArgs = "0,1,3,0.2"; // 退回仍采用0,1,3，因为0,1,6的趋势过大
            // 特别说明，阈值由于分境内外，参数上比较灵活，所以先写在SQL里
            //          机型收敛也写在SQL里
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CBS,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, false, null, true, null, null,
                    retAlArgs, null, null, null, null, null);
        }
    }


    /**
     * 大客户+毛刺预测，用于PPL录入时，云运管可以参考，也可以一键干预
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 40 8 1 * ?")
    @TaskLog(taskName = "createMonthArimaxTaskForWholeHead")
    public void createMonthArimaxTaskForWholeHead() {
        if (!checkScaleData()) {
            log.error("daily scale data not ready, end job");
            return;
        }

        {
            String category = "方案855：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 3,
                    null, null,
                    ListUtils.of("上海自动驾驶云一区"), null, null, null);
        }

        {
            String category = "方案865：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 3,
                    null, null,
                    ListUtils.of("上海自动驾驶云一区"), null, null, null);
        }

        {
            String category = "方案875：大客户加毛刺-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 3,
                    null, null,
                    ListUtils.of("上海自动驾驶云一区"), null, null, null);
        }
    }

    /**
     * 中长尾预测范围
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 30 8 1 * ?")
    @TaskLog(taskName = "createMonthArimaxTaskForWhole")
    public void createMonthArimaxTaskForWhole() {
        if (!checkScaleData()) {
            log.error("daily scale data not ready, end job");
            return;
        }

        {
            String category = "方案705：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAXL,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            String retAlArgs = "0,1,3,0.2"; // 退回仍采用0,1,3，因为0,1,6的趋势过大
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 1,
                    retAlArgs, null, null, null, null, null);
        }

        {
            // 来自于清华的模型
            String category = "方案707：全部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度-MIXED";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.MIXED,
                    Lang.list());
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 1,
                    null, null, null, null, null, null);
        }

        {
            String category = "方案715：全部-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 1,
                    null, null, null, null, null, null);
        }

        {
            String category = "方案725：全部-月切保留客户维度(机型规格)-机型收敛-包弹-内外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY_INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudWholeForecastCompute(Ppl13weekForecastProductEnum.CVM,
                    category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true, null, 1,
                    null, null, null, null, null, null);
        }

        // 对于方案705和715，自动同步最新版执行毛刺，这个毛刺数据目前用于行业数据看板查询哪些是毛刺，注意，毛刺是在输入时就决定了，所以不用等任务完成
        ppl13WeekInputService.reinitSpikeLatestData();
    }

    /**
     * 每个月1号（规模日表7-8点准备好之后），创建ARIMA和ARIMAX的月度的input和预测任务
     * 这个用于腰部
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 20 8 1 * ?")
    @TaskLog(taskName = "createMonthArimaxTaskForMiddle")
    public void createMonthArimaxTaskForMiddle() {

        if (!checkScaleData()) {
            log.error("daily scale data not ready, end job");
            return;
        }

        // 新腰部的4个方案

        {
            String category = "方案651：新腰部-月切去重-机型收敛-包弹-外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.NO_UIN_INSTANCE_TYPE_ZONE_NAME;
            ppl13WeekInputService.createCloudNewMiddleForecastCompute(category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true);
        }

        {
            String category = "方案655：新腰部-月切保留客户维度(机型规格)-机型收敛-包弹-外部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INDUSTRY;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudNewMiddleForecastCompute(category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true);
        }

        {
            String category = "方案661：新腰部-月切去重-机型收敛-包弹-内部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.NO_UIN_INSTANCE_TYPE_ZONE_NAME;
            ppl13WeekInputService.createCloudNewMiddleForecastCompute(category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true);
        }

        {
            String category = "方案665：新腰部-月切保留客户维度(机型规格)-机型收敛-包弹-内部客户-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum = Ppl13weekForecastSourceTypeEnum.INNER;
            Ppl13weekForecastBillTypeEnum billTypeEnum = Ppl13weekForecastBillTypeEnum.ALL;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));
            PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.UIN_INSTANCE_MODEL_ZONE_NAME;
            ppl13WeekInputService.createCloudNewMiddleForecastCompute(category, startLocalDate.toString(),
                    SerialIntervalEnum.MONTH,
                    sourceTypeEnum,
                    billTypeEnum, algorithm, true, true, groupDimsEnum, true);
        }
    }

    /**
     * 每个月1号（规模日表7-8点准备好之后），创建ARIMA和ARIMAX的月度的input和预测任务
     * 这个用于腰部
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 10 8 1 * ?")
    @TaskLog(taskName = "createMonthArimaxTaskForZiyan")
    public void createMonthArimaxTaskForZiyan() {
        if (!checkZiyanCvmData()) {
            log.error("daily ziyan cvm data not ready, end job");
            return;
        }

        {
            String category = "方案400：自研-机型族-地域-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            SerialIntervalEnum serialIntervalEnum = SerialIntervalEnum.MONTH;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));

            ppl13WeekInputService.createZiyanForecastCompute(
                    category, startLocalDate.toString(), serialIntervalEnum,
                    algorithm, true, true, false, 0, 1, 6);
        }

        {
            String category = "方案406：常规项目-机型族-地域-月度ARIMAX";
            LocalDate startLocalDate = DateUtils.getFirstDayOfMonth(new Date());
            SerialIntervalEnum serialIntervalEnum = SerialIntervalEnum.MONTH;
            ForecastAlgorithms.Algorithm algorithm = new ForecastAlgorithms.Algorithm(
                    ForecastAlgorithms.AlgorithmEnum.ARIMAX,
                    Lang.list("0", "1", "3", "0", "1", "6", "0.2"));

            ppl13WeekInputService.createZiyanForecastCompute(
                    category, startLocalDate.toString(), serialIntervalEnum,
                    algorithm, true, true, false, 7, 0, 8);
        }

    }

    /**
     * 生成ppl_forecast_config_customer_defines_year_month_version表的数据，每个月一份
     * 2023-12-15 这里改成每天一份，原来的是： @Scheduled(cron = "0 0 2 1 * ?")
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 2 * * ?")
    @TaskLog(taskName = "createYearMonthCustomerDefines")
    public void createYearMonthCustomerDefines() {
        log.info("createYearMonthCustomerDefines task begin");
        ppl13weekCommonDataAccess.createYearMonthCustomerDefines(LocalDate.now());
        ppl13weekCommonDataAccess.createVersionItemYearMonthCustomerDefines(LocalDate.now());
        log.info("createYearMonthCustomerDefines task end");
    }

    @SneakyThrows
    @Scheduled(cron = "0 0 2 * * ?") // Run at 2:00 AM every day
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void scheduledSyncCbsHeaderCustomer() {
        log.info("Starting scheduled CBS header customer sync");
        try {
            ppl13weekOpsService.syncCbsHeaderCustomer();
            log.info("Completed scheduled CBS header customer sync");
        } catch (Exception e) {
            log.error("Error during scheduled CBS header customer sync", e);
        }
    }
}
