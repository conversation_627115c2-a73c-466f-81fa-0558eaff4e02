package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemRecordDO;
import lombok.Data;

/**
 * 修改item时附带的事件信息
 */
@Data
public class ChangeItemEventDTO {

    // 以下字段和ppl_item_record表一一对应

    private String operateUser;

    /**
     * @see cloud.demand.app.modules.p2p.ppl13week.enums.PplItemEventTypeEnum
     */
    private String eventType;

    private String eventName;

    private String eventContent;

    private String orderType;

    private String orderId;

    public void fill(PplItemRecordDO recordDO) {
        recordDO.setOperateUser(operateUser);
        recordDO.setEventType(eventType);
        recordDO.setEventName(eventName);
        recordDO.setEventContent(eventContent);
        recordDO.setOrderType(orderType);
        recordDO.setOrderId(orderId);
    }

}
