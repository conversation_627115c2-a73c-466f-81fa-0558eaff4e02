package cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class QuotaQueryResp {

    private Boolean success = Boolean.TRUE;

    private String errorMsg;

    private Boolean retry = Boolean.FALSE;


    @JsonAlias("quota_list")
    private List<QuotaDetail> quotaList;

    @JsonAlias("total_count")
    private Integer totalCount;

    @Data
    public static class QuotaDetail {

        String appId;

        Date createTime;

        String frozenExpireTime;

        int frozenQuota;

        int id;

        String payMode;

        int quota;

        String quotaId;

        String quotaKey;

        String quotaUnit;

        int quotaUsage;

        Date updateTime;

        String zoneId;
    }
}
