package cloud.demand.app.modules.p2p.industry_demand.dto;

import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import com.pugwoo.wooutils.collect.ListUtils;
import java.util.List;
import lombok.Data;

@Data
public class UserPermissionFilterReq {

    /**
     *  需要查询对应数据权限的角色
     */
    private List<IndustryDemandAuthRoleEnum> includeRoles;

    /**
     *  不需要查询对应数据权限的角色，优先级高于includeRoles
     */
    private List<IndustryDemandAuthRoleEnum> excludeRoles;

    /**
     *  没有数据权限时是否抛出异常。默认为true（没有数据权限时抛出异常）
     */
    private boolean nonPermissionThrowsException = true;

    /**
     *  当前没有登陆用户 时是否跳过权限查询，默认为true（当前没有登陆用户时跳过权限查询）
     */
    private boolean notCheckSystem = true;

    /**
     *  当前用户为 {@link IndustryDemandAuthRoleEnum#ADMIN} 时是否跳过权限查询，默认为true（当前用户为 admin 时跳过权限查询）
     */
    private boolean notCheckAdmin = true;

    public static UserPermissionFilterReq createByIncludeRoles(IndustryDemandAuthRoleEnum... roles)  {
        UserPermissionFilterReq req = new UserPermissionFilterReq();
        req.setIncludeRoles(ListUtils.newArrayList(roles));
        return req;
    }


}
