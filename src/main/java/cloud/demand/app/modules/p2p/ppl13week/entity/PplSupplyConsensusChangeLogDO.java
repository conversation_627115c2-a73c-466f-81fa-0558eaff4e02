package cloud.demand.app.modules.p2p.ppl13week.entity;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_supply_consensus_change_log")
public class PplSupplyConsensusChangeLogDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time", setTimeWhenInsert = true)
    private Date createTime;

    /**
     * 操作人
     */
    @Column(value = "user_name")
    private String userName;

    /**
     * ppl需求id<br/>Column: [ppl_id]
     */
    @Column(value = "ppl_id")
    private String pplId;

    /**
     * 供应方案id
     * @see PplSupplyConsensusDO#getId()
     */
    @Column(value = "consensus_id")
    private Long consensusId;

    /**
     * 备注
     */
    @Column(value = "note")
    private String note;

    /**
     *  更新的json
     */
    @Column(value = "update_json")
    private String updateJson;

    /**
     *  更新前的数据
     */
    @Column(value = "normal_json")
    private String normalJson;


}
