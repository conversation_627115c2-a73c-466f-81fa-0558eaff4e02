package cloud.demand.app.modules.p2p.ppl13week.dto.item;

import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.listener.tool.FieldComparator.ChineseName;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class PplItemChangeAllFieldDTO extends PplItemBaseDO {

    /**
     * 变更状态
     *
     * @see PplRecordChangeStatusEnum
     */
    private String changeStatus;

    /**
     * 有变化的字段列表
     */
    private List<PplItemChangeFieldDTO> changeFieldDTOList;

    /**
     * 需求来源
     *
     * @see PplOrderSourceTypeEnum
     */
    private String source;

    @ChineseName("行业部门")
    private String industryDept;

    @ChineseName("客户类型")
    private String customerType;

    @ChineseName("客户名称")
    private String customerName;

    @ChineseName("客户简称")
    private String customerShortName;

    @ChineseName("客户UIN")
    private String customerUin;

    @ChineseName("客户来源")
    private String customerSource;

    @ChineseName("中心")
    private String center;

    @ChineseName("战区")
    private String warZone;

    private String submitUser;

    // 地域类型（国内/国外） -- 查询日志时返回设置
    private String regionType;

    // 下面的四个共识字段，在变更事件为 行业拒绝引导、行业接受引导 时适用

    /** 共识需求日期, 如果是变更前的数据，则将需求日期传入, 如果是变更后的数据，则传入共识需求日期 */
    @ChineseName("共识需求日期")
    private LocalDate consensusDemandDate;

    /** 共识机型, 如果是变更前的数据，则将需求机型传入, 如果是变更后的数据，则传入共识机型 */
    @ChineseName("共识机型")
    private String consensusInstanceType;

    /** 共识可用区, 如果是变更前的数据，则将需求可用区传入, 如果是变更后的数据，则传入共识可用区 */
    @ChineseName("共识可用区")
    private String consensusZoneName;

    /** 本条共识的核心数 */
    @ChineseName("本条共识的核心数")
    private Integer consensusTotalCore;

}
