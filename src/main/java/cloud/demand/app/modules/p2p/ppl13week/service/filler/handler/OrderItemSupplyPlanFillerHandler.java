package cloud.demand.app.modules.p2p.ppl13week.service.filler.handler;

import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import cloud.demand.app.modules.order.enums.SupplyPlanVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.OrderItemSupplyPlanFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.core.FillerHandler;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import javax.annotation.Resource;
import lombok.Data;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;

@Service
public class OrderItemSupplyPlanFillerHandler implements FillerHandler<OrderItemSupplyPlanFiller> {

    @Resource
    private DBHelper demandDBHelper;

    @Override
    public void fill(List<OrderItemSupplyPlanFiller> obj) {
        BatchUtil.syncBatchExec(obj, 10000, this::fillOneBatch);
    }

    private void fillOneBatch(List<OrderItemSupplyPlanFiller> result) {
        String sql = ORMUtils.getSql("/sql/order/supply/query_supply_num_by_order_items.sql");
        List<String> items = new ArrayList<>();
        for (OrderItemSupplyPlanFiller filler : result) {
            if (filler == null || Strings.isBlank(filler.provideOrderNumberId())) {
                continue;
            }
            items.add(filler.provideOrderNumberId());
        }
        if (ListUtils.isEmpty(items)) {
            return;
        }
        List<OrderItemSupplyNumDTO> list = demandDBHelper.getRaw(OrderItemSupplyNumDTO.class, sql,
                SupplyPlanVersionStatusEnum.available.getCode(), items);
        if (ListUtils.isEmpty(list)) {
            return;
        }
        Map<String, List<OrderItemSupplyNumDTO>> map = ListUtils.toMapList(
                list, OrderItemSupplyNumDTO::getOrderNumberId, Function.identity());
        for (OrderItemSupplyPlanFiller filler : result) {
            if (filler == null || Strings.isBlank(filler.provideOrderNumberId())) {
                continue;
            }
            List<OrderItemSupplyNumDTO> datas = map.get(filler.provideOrderNumberId());
            if (ListUtils.isEmpty(datas)) {
                continue;
            }
            LocalDate earliestSupplyDate = null;
            LocalDate latestSupplyDate = null;
            BigDecimal allMatchedCore = BigDecimal.ZERO;
            for (OrderItemSupplyNumDTO data : datas) {
                if (data == null || Strings.isBlank(data.matchType)) {
                    continue;
                }
                OrderSupplyPlanMatchTypeEnum matchType = OrderSupplyPlanMatchTypeEnum.getByCode(data.matchType);
                if (matchType == null) {
                    continue;
                }
                if (data.earliestSupplyDate != null) {
                    if (earliestSupplyDate == null || earliestSupplyDate.isAfter(data.earliestSupplyDate)) {
                        earliestSupplyDate = data.earliestSupplyDate;
                    }
                }
                if (data.latestSupplyDate != null) {
                    if (latestSupplyDate == null || latestSupplyDate.isBefore(data.latestSupplyDate)) {
                        latestSupplyDate = data.latestSupplyDate;
                    }
                }
                allMatchedCore = allMatchedCore.add(data.totalMatchCore);
                switch (matchType) {
                    case SATISFY:
                        filler.fillSatisfySupplyCore(data.supplyCoreNum);
                        filler.fillSatisfySupplyInstanceNum(data.supplyInstanceNum);
                        filler.fillOrderItemSatisfyMatchedCore(data.totalMatchCore);
                        break;
                    case BUY:
                        filler.fillBuySupplyCore(data.supplyCoreNum);
                        filler.fillBuySupplyInstanceNum(data.supplyInstanceNum);
                        filler.fillOrderItemBuyMatchedCore(data.totalMatchCore);
                        break;
                    case MOVE:
                        filler.fillMoveSupplyCore(data.supplyCoreNum);
                        filler.fillMoveSupplyInstanceNum(data.supplyInstanceNum);
                        filler.fillOrderItemMoveMatchedCore(data.totalMatchCore);
                        break;
                    default:
                        break;
                }
            }
            filler.fillOrderItemTotalMatchedCore(allMatchedCore);
            filler.fillEarliestSupplyDate(earliestSupplyDate);
            filler.fillLatestSupplyDate(latestSupplyDate);
            if (filler.provideOrderItemTotalCore() != null) {
                Integer totalCore = filler.provideOrderItemTotalCore();
                BigDecimal rate = BigDecimal.ZERO;
                if (allMatchedCore.compareTo(BigDecimal.ZERO) <= 0) {
                    rate = BigDecimal.ZERO;
                } else if (totalCore != null && totalCore > 0){
                    rate = NumberUtils.divide(allMatchedCore, totalCore, 4);
                }
                rate = rate.min(BigDecimal.ONE);
                filler.fillOrderItemTotalMatchedRate(rate);
            }
        }
    }

    @Data
    public static class OrderItemSupplyNumDTO {

        @Column("orderNumberId")
        private String orderNumberId;

        @Column("matchType")
        private String matchType;

        @Column("supplyCoreNum")
        private Integer supplyCoreNum;

        @Column("supplyInstanceNum")
        private Integer supplyInstanceNum;

        // 订单满足量
        @Column("totalMatchCore")
        private BigDecimal totalMatchCore;

        /** 最早实际交付日期 */
        @Column(value = "earliestSupplyDate")
        private LocalDate earliestSupplyDate;

        /** 最晚实际交付日期 */
        @Column(value = "latestSupplyDate")
        private LocalDate latestSupplyDate;

    }
}
