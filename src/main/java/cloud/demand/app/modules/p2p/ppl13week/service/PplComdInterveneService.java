package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.modules.p2p.ppl13week.dto.version.ComdInterveneByForecastModelReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionInterveneByForecastReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionInterveneByForecastResp;

/**
 * 云运管干预相关服务
 */
public interface PplComdInterveneService {

    /**
     * 一键使用模型预测的结果替换ppl的常规项目
     */
    PplItemImportRsp comdInterveneByForecastModel(ComdInterveneByForecastModelReq req);


    PplItemImportRsp resetAllComdIntervene(Long versionGroupId);


    QueryVersionInterveneByForecastResp queryInterveneByForecastLog(QueryVersionInterveneByForecastReq req);


}
