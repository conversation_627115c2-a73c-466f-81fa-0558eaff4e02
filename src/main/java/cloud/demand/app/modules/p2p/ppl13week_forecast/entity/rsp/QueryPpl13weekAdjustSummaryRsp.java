package cloud.demand.app.modules.p2p.ppl13week_forecast.entity.rsp;


import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QueryPpl13weekAdjustSummaryRsp {

    BigDecimal totalNewPredict;
    BigDecimal totalRetPredict;

    BigDecimal totalNewAdjustPredict;
    BigDecimal totalRetAdjustPredict;

    List<Item> item;

    @Data
    public static class Item {

        String yearMonth;

        BigDecimal newPredict;
        BigDecimal retPredict;

        BigDecimal newAdjustPredict;
        BigDecimal retAdjustPredict;

    }


}
