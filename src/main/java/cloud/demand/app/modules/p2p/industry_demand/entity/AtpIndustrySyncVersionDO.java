package cloud.demand.app.modules.p2p.industry_demand.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.sop.http.service.CloudAppLabSupplyDemandHttpService.InventoryReq;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

/**
 * 行业ATP结余库存数据同步的版本信息
 */
@Data
@ToString
@Table("atp_industry_sync_version")
public class AtpIndustrySyncVersionDO extends BaseDO {

    /** 定稿版本<br/>Column: [version_code] */
    @Column(value = "version_code")
    private String versionCode;

    /** 数据同步时的查询参数<br/>Column: [sync_params] */
    @Column(value = "sync_params", isJSON = true)
    private InventoryReq syncParams;

    /** 结余库存-统计日期<br/>Column: [stat_date] */
    @Column(value = "stat_date")
    private LocalDate statDate;

}
