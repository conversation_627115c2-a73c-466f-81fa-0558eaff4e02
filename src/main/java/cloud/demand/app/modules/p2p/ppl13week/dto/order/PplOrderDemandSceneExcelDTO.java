package cloud.demand.app.modules.p2p.ppl13week.dto.order;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.PplOrderImportRsp.ErrorMessage;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryPplOrderDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigDemandSceneDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.util.StringUtils;

@Data
public class PplOrderDemandSceneExcelDTO {

    /**
     * 客户类型,存量客户,winback客户<br/>Column: [customer_type]
     */
    @ExcelProperty( value = "客户类型")
    private String customerType;

    /**
     * 需求类型<br/>Column: [demand_type]
     */
    @ExcelProperty(value = "需求类型")
    private String demandType;

    /**
     * 需求场景<br/>Column: [demand_scene]
     */
    @ExcelProperty(value = "需求场景")
    private String demandScene;

    public static PplOrderDemandSceneExcelDTO transFrom(PplConfigDemandSceneDO source) {
        PplOrderDemandSceneExcelDTO pplOrderDemandSceneExcelDTO = new PplOrderDemandSceneExcelDTO();
        pplOrderDemandSceneExcelDTO.setCustomerType(source.getCustomerType());
        pplOrderDemandSceneExcelDTO.setDemandType(source.getDemandType());
        pplOrderDemandSceneExcelDTO.setDemandScene(source.getDemandScene());
        return pplOrderDemandSceneExcelDTO;
    }

    public static List<PplOrderDemandSceneExcelDTO> transFrom(List<PplConfigDemandSceneDO> source) {
        return ListUtils.transform(source, PplOrderDemandSceneExcelDTO::transFrom);
    }

}