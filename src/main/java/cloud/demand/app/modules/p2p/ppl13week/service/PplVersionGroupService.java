package cloud.demand.app.modules.p2p.ppl13week.service;

import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ExportPplVersionItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ImportVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.InitNewVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupView;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.SaveVersionGroupItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupApproveResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupForecastResultViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupInfoResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSpikeViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSummaryResp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordWithGroupVO;
import com.pugwoo.wooutils.redis.Synchronized;
import java.util.List;
import java.util.Map;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

/**
 * ppl版本分组审批相关服务
 */
public interface PplVersionGroupService {

    /**
     * 检查当前登录用户是否有查看权限，以下用户可以看：
     * 1）当前的处理人
     * 2）参与过审批的人
     * 3）ADMIN角色
     * 4）有对应部门的PPL角色
     * 5）同OA部门的人
     */
    void checkGroupViewPermission(PplVersionGroupInfoVO groupInfoVO);

    /**
     * 用于下拉框，查询
     */
    List<String> queryVersionCode();

    /**
     * 查询ppl版本分组列表
     */
    QueryVersionGroupResp queryVersionGroup(QueryVersionGroupReq req);

    /**
     * 查询分组概要信息
     */
    VersionGroupInfoResp queryVersionGroupInfo(Long groupId);

    /**
     * 查询分组审批记录
     */
    VersionGroupApproveResp queryVersionGroupApproveLog(Long groupId);

    /**
     * 查询分组资源统计信息
     */
    VersionGroupStatResp queryVersionGroupStat(VersionGroupStatReq req);

    /**
     * 查询 ppl 聚合数据， ppl 数据
     *
     * @param req req
     * @return rsp
     */
    VersionGroupSpikeViewResp queryVersionGroupSpikeView(VersionGroupStatReq req);

    /**
     * 查询 ppl 概览页面的数据，预测数据
     *
     * @param req req
     * @return rsp
     */
    VersionGroupForecastResultViewResp queryVersionGroupForecastResultView(VersionGroupStatReq req);

    /**
     * 查询分组资源统计信息 新版
     */
    VersionGroupSummaryResp queryVersionGroupSummary(VersionGroupStatReq req);

    /**
     * 查询分组资源明细，这里不分页
     */
    VersionGroupItemResp queryVersionGroupItem(Long groupId);

    /**
     * 查询一个版本下所有分组资源明细，这里不分页
     */
    VersionGroupItemResp queryVersionGroupItemByVersionCode(String versionCode);

    /**
     * 保存明细，和审批拆分开了
     */
    void saveVersionGroupItem(SaveVersionGroupItemReq req);

    void initVersionGroupItem(InitNewVersionGroupReq req);


    /**
     * 手工下发对冲
     *
     * 把这些record的数据都转为库存对冲的状态
     *
     * 不会生成代办
     *
     * @param notInStockSupply list
     */
    void toStockSupplyStatus(List<PplVersionGroupRecordWithGroupVO> notInStockSupply);

    /**
     * 无锁
     *
     * @param req req
     * @see PplVersionGroupService approveVersionGroup
     */
    void approveVersionGroupWithOutLock(ApproveVersionGroupReq req);

    // 上升到 version， 等待 5 秒钟
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    void approveVersionGroupByAdmin(String changeStatus, PplVersionGroupDO pplVersionGroupDO);

    /**
     * 审批
     */
    void approveVersionGroup(ApproveVersionGroupReq req);

    /**
     * 关联一个组的预约数据
     *
     * @param groupId 组id
     */
    void updateYunXiaoApplyData(long groupId);

    /**
     * 查询ppl版本下item的所有记录，时间逆序
     */
    List<PplVersionGroupRecordItemVO> getVersionRecordItem(String pplId);

    /**
     * 比较变化
     */
    Map<String, String> diffChange(PplVersionGroupRecordItemDO originItem, PplVersionGroupRecordItemDO newItem);

    /**
     * import2NextRecord
     *
     * @param group
     * @param approveNote
     * @return
     */
    PplVersionGroupRecordDO import2NextRecord(PplVersionGroupInfoVO group, String approveNote, Boolean isImportData,
            String nextGroupStatus);

    Map<String, Integer> queryVersionGroupView(QueryVersionGroupView req);

    /**
     * 给一批Item 打标，只会设置 is_spike 字段
     * @param pplItems items
     */
    void setThresholds(List<PplVersionGroupRecordItemDO> pplItems);

    void getNoSuccessUin();

    //    EXISTING  yearMonth = 202210
    PplItemImportRsp uploadPplItemDetailExcel(MultipartFile file, YearMonth start, YearMonth end, String product);

    FileNameAndBytesDTO exportPplVersionItemExcel(ExportPplVersionItemReq req);

    void importPplVersionItem(ImportVersionGroupReq req);

    /**
     * 导出CVM 的数据
     *
     * @param req req
     * @return byte
     */
    FileNameAndBytesDTO exportPplVersionItemExcelWithStockSupply(ExportPplVersionItemReq req);

    /**
     * 查询指定版本下的所有分组
     *
     * @param versionCode
     * @param product 如果有值，则只查询这个产品
     */
    List<PplVersionGroupDO> queryGroupByVersionCode(String versionCode, String product);

    void applyToItem(PplVersionGroupInfoVO group);

    void snapshotItemStatus(List<PplVersionGroupRecordItemDO> items);

    /**
     * 查询group最新的record的资源明细，这里不分页
     */
    List<PplVersionGroupItemVO> queryPplVersionGroupItemVO(Long groupId);

    /**
     * 查询指定分组的最后一个record
     */
    PplVersionGroupRecordDO queryLatestRecord(Long groupId);

    /**
     * 查询分组信息
     */
    PplVersionGroupDO queryPplVersionGroup(Long groupId);

    List<VersionGroupItemResp.GroupItemDTO> queryGroupItemDtoWithComd(Long groupId);

    void checkVersionDeadline();

    void forecastApplyMatch(Long versionGroupInfoId);

    void sendInSubmitMail();

    void removeGroup(List<PplVersionGroupRecordWithGroupVO> list);

    /**
     * 根据 ppl版本编码查询出 版本内所有pplIdList 的最新ppl版本Item记录
     *
     * @param versionCode ppl版本编码 不能为空
     * @param pplIdList 需要查询的 pplId 集合，如果为空，则默认查询所有
     */
    List<PplVersionGroupRecordItemDO> queryLatestPplVersionItem(String versionCode, List<String> pplIdList);

    /**
     * 根据 versionGroupId 查询出 版本内所有pplIdList 的最新ppl版本Item记录
     *
     * @param versionGroupId 不能为空
     * @param pplIdList 需要查询的 pplId 集合，如果为空，则默认查询所有
     */
    List<PplVersionGroupRecordItemDO> queryLatestPplVersionItemByVersionGroupId(Long versionGroupId,
            List<String> pplIdList);

    String refreshGpuGap(String versionCode, String industryDept, String product);

    void syncYunTiPpl(String versionCode);

    void refreshSpike();

    /**
     * 给pplItems打上cbs项目标签
     * @param pplItems
     */
    void setThresholdsForCBS(List<PplVersionGroupRecordItemDO> pplItems);


    void refreshCbsSpike(String versionCode);

}
