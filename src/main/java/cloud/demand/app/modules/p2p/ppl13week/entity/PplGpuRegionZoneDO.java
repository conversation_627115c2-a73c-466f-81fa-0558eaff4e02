package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_gpu_region_zone")
public class PplGpuRegionZoneDO {

    /**
     * id<br/>Column: [id]
     */
    @Column(value = "id", isKey = true, isAutoIncrement = true)
    private Long id;

    /**
     * 删除标记<br/>Column: [deleted]
     */
    @Column(value = "deleted", softDelete = {"0", "1"})
    private Boolean deleted;

    /**
     * 创建时间<br/>Column: [create_time]
     */
    @Column(value = "create_time")
    private Date createTime;

    /**
     * 更新时间<br/>Column: [update_time]
     */
    @Column(value = "update_time")
    private Date updateTime;

    @Column(value = "region")
    private String region;

    @Column(value = "region_name")
    private String regionName;

    @Column(value = "zone")
    private String zone;

    @Column(value = "zone_name")
    private String zoneName;

    @Column(value = "instance_type")
    private String instanceType;

    @Column(value = "instance_model")
    private String instanceModel;

    /**
     * 原始卡型 带- <br/>Column: [gpu_type]
     */
    @Column(value = "original_gpu_type")
    private String originalGpuType;

    /**
     * 卡型 不带- <br/>Column: [gpu_type]
     */
    @Column(value = "gpu_type")
    private String gpuType;

    /**
     * 卡类型 消费、训练、推理 <br/>Column: [card_type]
     */
    @Column(value = "card_type")
    private String cardType;

    /**
     * 产品形态<br/>Column: [gpu_product_type]
     */
    @Column(value = "gpu_product_type")
    private String gpuProductType;

    @Column(value = "cpu_num")
    private Integer cpuNum;

    @Column(value = "gpu_num")
    private Integer gpuNum;

    @Column(value = "gpu_count")
    private BigDecimal gpuCount;

    @Column(value = "memory")
    private Integer memory;
    
    @Column(value = "is_white")
    private Boolean isWhite;

    /**
     *   是否不展示，true 表示不展示，  false 表示展示
     */
    @Column(value = "not_show")
    private Boolean notShow;

}
