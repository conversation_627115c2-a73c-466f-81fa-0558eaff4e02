package cloud.demand.app.modules.p2p.ppl13week.job;

import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionSlaDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderAuditRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDraftDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderDraftStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.inner_process.PplInnerProcessVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDraftService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplIndustryProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerProcessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplInnerVersionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.sun.jna.platform.win32.WinDef.LONG;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.nutz.lang.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import yunti.boot.config.DynamicProperty;

@Slf4j
@Service
public class InnerProcessRelevantTask {

    @Autowired
    private PplInnerProcessService innerProcessService;
    @Autowired
    private PplDraftService pplDraftService;
    @Resource
    private PplInnerVersionService innerVersionService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private PplConsensusService pplConsensusService;

    @Resource
    private PplIndustryProcessService pplIndustryProcessService;

    @Resource
    Alert alert;


    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");


    /**
     * 同步已审批完的行业PPL 每2分钟触发一次
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */2 * * * * ")
    public void SyncIndustryItemTask() throws InterruptedException {
        Thread.sleep(1000);
        log.info("begin SyncIndustryItemTask");
        innerProcessService.syncIndustryItem();
        log.info("end SyncIndustryItemTask");
    }


    /**
     * 给需要内部流程审批的人发送邮件,每天早上10点发送
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0,0 0,0 10 * * * ")
    public void sendAuditorMail() throws InterruptedException {
        //        本地环境关掉
        if (Strings.isNotBlank(testEnv.get())) {
            return;
        }
        Thread.sleep(1000);
        log.info("begin sendAuditorMail");
        innerProcessService.sendAuditorMail();
        log.info("end sendAuditorMail");
    }

    /**
     * 给架构师推送邮件提醒，处理本周期的过期需求，每天一次
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0,0 0,0 10 * * * ")
    public void sendExpiredMail() throws InterruptedException {
        Thread.sleep(1000);
        log.info("begin sendExpiredMail");
        innerProcessService.sendExpiredMail();
        log.info("end sendExpiredMail");
    }


    /**
     * 判断当前是否进行中的行业流程是否已到截止录制时间。
     * 如果到了的话，将预提交的全部转成流程中。
     * 每1分钟触发一次
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */1 * * * * ")
    public void syncPreSubmitPplOrder() throws InterruptedException {
        Thread.sleep(1000);
        List<PplInnerProcessVersionDO> all = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
                "where status = ?", PplInnerProcessVersionStatusEnum.PROCESSING.getCode());

        for (PplInnerProcessVersionDO versionDOS : all) {
            if (versionDOS == null) {
                continue;
            }
            PplInnerProcessVersionSlaDO one = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
                    "where version_id = ? and deadline_type = 'ENTER'", versionDOS.getId());
            if (one == null) {
                continue;
            }
            if (one.getDeadlineTime() != null && one.getDeadlineTime().before(new Date())) {
                try {
                    syncPreSubmitData(versionDOS);
                } catch (Exception e) {
                    String msg = "【同步预提交单据 -- 失败】versionId： " + versionDOS.getId() + " exception: " + e;
                    AlarmRobotUtil.doAlarm("syncPreSubmitPplOrder", msg, null, false);
                }
            }
        }

    }

    /**
     * 每个版本到达截止时间后具体该做什么事 报错需抛到上层告警
     * 粒度： 版本
     *
     * @param versionDO
     */
    @Transactional("demandTransactionManager")
    void syncPreSubmitData(PplInnerProcessVersionDO versionDO) {
        List<PplOrderDraftDO> pplOrderDraftDOS = demandDBHelper.getAll(PplOrderDraftDO.class,
                "where industry_dept = ? and draft_status = ? and ppl_order in "
                        + "(select ppl_order from ppl_item_draft where deleted = 0"
                        + " and draft_status = ? and product in (?))",
                versionDO.getIndustryDept(),
                PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
                Arrays.asList(versionDO.getProduct().split(";")));
        if (CollectionUtils.isEmpty(pplOrderDraftDOS)) {
            // 判断是否需系统自动开启下一版本
            isAutoNextVersion(versionDO);
            return;
        }

        List<String> pplOrders = ListUtils.transform(pplOrderDraftDOS, PplOrderDraftDO::getPplOrder);

        // 1、系统自动重匹配预测预约
        // （目前只针对一部和战略部）
        List<String> industryList = Arrays.asList("智慧行业一部", "战略客户部");
        if (industryList.contains(versionDO.getIndustryDept())) {
            List<String> deletedPeOrders = pplDraftService.matchRepeatAppliedDraft(versionDO.getIndustryDept(),
                    Arrays.asList(versionDO.getProduct().split(";")));
            pplOrders.removeAll(deletedPeOrders);
        }

        // 2、提交草稿单到真正的流程中
        pplDraftService.submitDraft(versionDO.getId(), pplOrders);

    }

    /**
     * 是否需系统自动开启下一版本
     * （目的：防止当前版本无审批数据时，流程卡在当前版本，行业无法在版本中正常录入需求）
     * （两种解决方案：一：云运管将当前版本的审批节点结束时间调后；二：系统自动开下一版本）
     * （系统自动开下一版本，需注意一个场景：下一版本的需求沟通结束时间已经过期，当前版本审批通过开启下一版本后，下一版本直接进入审批流，当前版本的mq消息还未消费将数据继承到下一版本的审批流，定时任务已经在扫描）
     *
     * @param versionDO 当前版本
     */
    private void isAutoNextVersion(PplInnerProcessVersionDO versionDO) {
        if ("exist".equals(redisHelper.getString("existAuditVersion:" + versionDO.getId()))) {
            return;
        }
        int DAY_SECOND = 86400;

        // 判断当前版本的审批流有无数据
        PplOrderAuditRecordDO auditRecordDO = demandDBHelper.getOne(PplOrderAuditRecordDO.class,
                "where version_id = ? ", versionDO.getId());
        if (auditRecordDO != null) {
            redisHelper.setStringIfNotExist("existAuditVersion:" + versionDO.getId(), DAY_SECOND * 10,
                    "exist");
            return;
        }

        // 当前版本审批流无数据，判断上一版本的审批流有无数据
        PplInnerProcessVersionDO lastVersionDO = demandDBHelper.getOne(PplInnerProcessVersionDO.class,
                "WHERE id = ( "
                        + "SELECT MAX(id) FROM ppl_inner_process_version "
                        + "WHERE process_id = ? AND status = ? AND deleted = 0)", versionDO.getProcessId(),
                PplInnerProcessVersionStatusEnum.DONE.getCode());
        if (lastVersionDO != null) {
            if ("exist".equals(redisHelper.getString("existAuditLastVersion:" + lastVersionDO.getId()))) {
                return;
            }

            PplOrderAuditRecordDO lastAuditRecordDO = demandDBHelper.getOne(PplOrderAuditRecordDO.class,
                    "where version_id = ? ", lastVersionDO.getId());
            // 上一版本有审批数据，说明会发送mq消息并进行消费
            if (lastAuditRecordDO != null) {
                // 当前版本有数据继承，说明现在定时任务扫描到的当前版本的审批流是暂时没有数据，即定时任务执行时mq还未消费完
                List<PplItemDO> versionValidPplItem = innerProcessService.getVersionValidPplItem(versionDO);
                if (!CollectionUtils.isEmpty(versionValidPplItem)) {
                    redisHelper.setStringIfNotExist("existAuditLastVersion:" + lastVersionDO.getId(), DAY_SECOND * 10,
                            "exist");
                    return;
                } else if (lastVersionDO.getConsumeStatus() != 1) {
                    // 判断mq是否处理完上一版版本的数据，若未处理完，则不进入自动开下一版的逻辑，若处理完，则系统可自动开下一版
                    // 即需限制 确保上一版数据处理完后，再进行是否关闭当前版本自动开下一版的逻辑处理
                    return;
                }
            }
        }

        //若当前版本or上一版本的审批流中无审批单据 / 当前版本无数据继承(mq消费完的前提下)，则直接完成当前版本，开启新版本，供行业录入需求
        Long nextVersionId = innerVersionService.autoInitNewVersion(versionDO.getIndustryDept(),
                versionDO.getId());
        // 将新版本需求年月范围已生效数据移至新版本的需求沟通中
        innerProcessService.syncNewVersionPpl(nextVersionId);
    }

    /**
     * 每分钟检查一次
     * 定时器检查是否到达审批时间,确定是否过单
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */1 * * * * ")
    public void checkAutoPass() throws InterruptedException {
        Thread.sleep(1000);
        log.info("begin checkAutoPass");
        innerProcessService.checkAutoPass();
        log.info("end checkAutoPass");
    }

    /**
     * 每20s检查一次
     * 确定待办中的数据是否已完成，如果已完成的话，完成待办
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "20,40,0 * * * * ? ")
    public void finishIndustryTodo() throws InterruptedException {
        Thread.sleep(1000);
        log.info("begin checkAutoPass");
        innerProcessService.finishIndustryTodo();
        log.info("end checkAutoPass");
    }

    /**
     * 定时同步在需求沟通时间内，来自云霄的预约单数据到草稿单中
     * 每5min同步一次
     */
//    @Synchronized(waitLockMillisecond = 100)
//    @Scheduled(cron = "0 */5 * * * * ")
//    @TaskLog(taskName = "syncAppliedToDraft")
//    public void syncAppliedToDraftTask() throws InterruptedException {
//        Thread.sleep(1000);
//        log.info("begin syncAppliedToDraftTask");
//
//        innerProcessService.syncAppliedToDraft();
//
//        log.info("end syncAppliedToDraftTask");
//
//    }
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */20 * * * *")
    public void checkInnerProcessData() {
        Map<String, Long> repeatDraftMap = demandDBHelper.getRaw(Map.class,
                        "select ppl_id, count(ppl_id) as num from ppl_item_draft "
                                + "where draft_status = 'PRE_SUBMIT' and deleted = 0 "
                                + "group by ppl_id "
                                + "having count(ppl_id) > 1 ").stream()
                .collect(Collectors.toMap(e -> (String) e.get("ppl_id"), e -> (Long) e.get("num")));
        if (!CollectionUtils.isEmpty(repeatDraftMap)) {
            AlarmRobotUtil.doAlarm("checkInnerProcessData",
                    "草稿箱中存在重复ppl单 :" + repeatDraftMap.keySet(),
                    null, false);
        }

        Map<String, Long> errorSourceMap = demandDBHelper.getRaw(Map.class,
                        "select id, ppl_order from ppl_order "
                                + " where ppl_order like 'PE%' and source in('IMPORT') and deleted = 0").stream()
                .collect(Collectors.toMap(e -> (String) e.get("id"), e -> (Long) e.get("ppl_order")));
        if (!CollectionUtils.isEmpty(errorSourceMap)) {
            AlarmRobotUtil.doAlarm("checkInnerProcessData",
                    "存在IMPORT来源的PE单 :" + errorSourceMap.values(),
                    null, false);
        }

        Map<String, Long> repeatAppliedMap = demandDBHelper.getRaw(Map.class,
                        "select yunxiao_order_id, count(yunxiao_detail_id) as num from ppl_item_applied "
                                + "where deleted = 0 "
                                + "group by yunxiao_order_id, yunxiao_detail_id "
                                + "having count(yunxiao_detail_id) > 1 ").stream()
                .collect(Collectors.toMap(e -> (String) e.get("yunxiao_order_id"), e -> (Long) e.get("num")));
        if (!CollectionUtils.isEmpty(repeatAppliedMap)) {
            AlarmRobotUtil.doAlarm("checkInnerProcessData",
                    "存在重复预约明细的预约单列表 :" + repeatAppliedMap.keySet(),
                    null, false);
        }

//        Map<String, Long> repeatUuidMap = demandDBHelper.getRaw(Map.class,
//                        " select bizId, count(Id) as num from ( "
//                                + " select biz_id as bizId, id as Id from ppl_item "
//                                + " where biz_id is not null and deleted = 0 and instance_num > 0 "
//                                + " group by ppl_order "
//                                + " ) tmp "
//                                + " group by bizId "
//                                + " having count(Id) > 1 ").stream()
//                .collect(Collectors.toMap(e -> (String) e.get("bizId"), e -> (Long) e.get("num")));
//        if (!CollectionUtils.isEmpty(repeatUuidMap)) {
//            AlarmRobotUtil.doAlarm("checkInnerProcessData",
//                    "战略客户部，同一uuid下存在多个明细（非拆单相关）:" + repeatUuidMap.keySet(),
//                    null, false);
//        }
    }

    /**
     * 定时同步：
     * 需求沟通阶段：同步 在需求沟通时间内，来自云霄的预约单数据到草稿单中（同步PN单 - 拆单 & 预约状态、预约单信息、前后预约量）
     * 审批阶段：同步 已预约明细 的预约资源（同步PN单 - 预约状态、预约单信息、前后预约量）
     * 每5min同步一次
     */
    // 2024-07-12 去除同步
//    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
//    @Scheduled(cron = "0 */5 * * * *")
//    @TaskLog(taskName = "syncAppliedToVersionLine")
//    public void syncAppliedToVersionLine() {
//        log.info("begin syncAppliedToVersionLine");
//
//        List<PplInnerProcessVersionDO> all = demandDBHelper.getAll(PplInnerProcessVersionDO.class,
//                "where status = ?", PplInnerProcessVersionStatusEnum.PROCESSING.getCode());
//
//        for (PplInnerProcessVersionDO versionDO : all) {
//            if (versionDO == null) {
//                continue;
//            }
//            PplInnerProcessVersionSlaDO one = demandDBHelper.getOne(PplInnerProcessVersionSlaDO.class,
//                    "where version_id = ? and deadline_type = 'ENTER'", versionDO.getId());
//            if (one == null) {
//                continue;
//            }
//
//            CompletableFuture.runAsync(() -> {
//                // 需求沟通阶段的 预提交 阶段，如果是预约明细，则将  草稿单中的 预约信息 与 ppl-item 保持一致
//                //      如果有生效数据，且非预约明细，则将  草稿单 中的 预约相关信息 与 ppl-item 保持一致（预约状态-已预约状态恢复回未预约、预约单信息-置空、前后预约量）
//                //      如果有生效数据，且是预约明细，则将  草稿单 中的 预约相关信息 与 ppl-item 保持一致（预约状态、预约单信息、前后预约量）
//                //                      如果被拆单，则草稿单也同步进行拆单
//                if (one.getDeadlineTime() == null || one.getDeadlineTime().after(new Date())) {
//                    List<PplItemDraftDO> pplItemDraftDOS = demandDBHelper.getAll(PplItemDraftDO.class,
//                            "where deleted = 0 and draft_status = ? and product in (?) and ppl_order in "
//                                    + "(select ppl_order from ppl_order_draft where deleted = 0 and draft_status = ? and industry_dept = ? )",
//                            PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(),
//                            Arrays.asList(versionDO.getProduct().split(";")),
//                            PplOrderDraftStatusEnum.PRE_SUBMIT.getCode(), versionDO.getIndustryDept());
//
//                    innerProcessService.syncAppliedDraftData(versionDO, pplItemDraftDOS);
//
//                    return;
//                }
//
//                // 审批阶段，如果是预约明细，则将 审批记录中的 预约信息 与 ppl-item 保持一致
//                //      如果有生效数据，且非预约明细，则将 审批记录 中的 预约相关信息 与 ppl-item 保持一致（预约状态-已预约状态恢复回未预约、预约单信息-置空、前后预约量）
//                //      如果有生效数据，且是预约明细，则将 审批记录 中的 预约相关信息 与 ppl-item 保持一致（预约状态、预约单信息、前后预约量）
//                List<PplOrderAuditRecordItemDO> maxAuditRecordItemDOs = demandDBHelper.getRaw(
//                        PplOrderAuditRecordItemDO.class,
//                        "select * from ppl_order_audit_record_item where deleted = 0 and audit_record_id in "
//                                + "(select max(id) from ppl_order_audit_record where deleted = 0 and version_id = ? group by ppl_order)",
//                        versionDO.getId());
//                innerProcessService.syncAppliedAuditData(maxAuditRecordItemDOs);
//                List<PplOrderAuditRecordItemDO> minAuditRecordItemDOs = demandDBHelper.getRaw(
//                        PplOrderAuditRecordItemDO.class,
//                        "select * from ppl_order_audit_record_item where deleted = 0 and audit_record_id in "
//                                + "(select min(id) from ppl_order_audit_record where deleted = 0 and version_id = ? group by ppl_order)",
//                        versionDO.getId());
//                innerProcessService.syncAppliedAuditData(minAuditRecordItemDOs);
//            });
//        }
//
//        log.info("end syncAppliedToVersionLine");
//    }


    /**
     * 定时任务扫描一部战区为 其他的PPL 看看是否为出海战区。
     *
     * @throws InterruptedException
     */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 */5 * * * * ")
    public void checkWarZoneAndUpdate() throws InterruptedException {
        Thread.sleep(1000);
        log.info("begin checkWarZoneAndUpdate");
        pplIndustryProcessService.checkWarZoneAndUpdate();
        log.info("end checkWarZoneAndUpdate");
    }


    /**
     * 定时任务 每周五凌晨3点触发 开行业版本
     */
    @Synchronized(waitLockMillisecond = 0, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 3 ? * 5")
    public void autoCreateVersionTask() throws InterruptedException {

        Thread.sleep(1000);
        log.info("begin autoCreateVersionTask");
        innerVersionService.autoCreateVersionTask();
        log.info("end autoCreateVersionTask");

    }

}
