package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.WorkWrapWithAuthentication;
import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AmountUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SecurityContextUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.industry_report.service.IndustryReportDictService;
import cloud.demand.app.modules.industry_report.service.impl.IndustryReportDictServiceImpl;
import cloud.demand.app.modules.order.entity.OrderConsensusDemandDetailDO.DiskItem;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplExportWriteHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryVersionInfoRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.QueryWaveReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.InstanceTypeRelateDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryIndustryDeptCustomerRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.QueryWaveDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.WaveValueDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.WaveValueDTO.Detail;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.excel.WaveDetailExportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewDetailRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewGroupRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewGroupRsp.ResultDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.overview.QueryWaveOverviewRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.AdsMckForecastSummaryDfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplItemCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwdCrpPplJoinOrderVersionCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersion532NewMifDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionBaseCfDo;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplItemVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.DwsCrpPplJoinOrderVersionNewestCfDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplJoinOrderVersionItemDataSource;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveProductCategoryEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveProjectTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplWaveVolumeTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.StatisticalScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.WaveTargetNameEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplWaveAnalysisService;
import cloud.demand.app.modules.p2p.ppl13week.thread.ThreadPoolUtils;
import cloud.demand.app.modules.p2p.ppl13week.vo.NewPplVersionGroupRecordItemWithOrderSumVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplComdVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemWithOrderSumVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwdCrpPplItemForWaveViewVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwdCrpPplItemVersionForWaveAnalysisVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.DwdCrpPplLatestItemForWaveViewVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.std_table.WaveAnalysisPplVersion532NewVO;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.SortingField;
import com.pugwoo.wooutils.collect.SortingOrderEnum;
import com.pugwoo.wooutils.collect.SortingUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.string.StringTools;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import io.vavr.Tuple2;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.nutz.lang.Lang;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import yunti.boot.exception.BizException;

@Service
@Slf4j
public class PplWaveAnalysisServiceImpl implements PplWaveAnalysisService {

    private final static String yearMonthPattern = "yyyy-MM";

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private IndustryReportDictService industryReportDictService;

    @Resource
    private PplDictService pplDictService;

    private final ExecutorService threadPool = Executors.newFixedThreadPool(100);

    private static final List<String> sourceOfIndustry = Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
            PplOrderSourceTypeEnum.FORECAST.getCode(),
            PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode());
    private static final List<String> sourceOfYun = Arrays.asList(PplOrderSourceTypeEnum.IMPORT.getCode(),
            PplOrderSourceTypeEnum.FORECAST.getCode(),
            PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode(),
            PplOrderSourceTypeEnum.COMD_INTERVENE.getCode());

    @Override
    public QueryWaveOverviewRsp compareAnalysis(QueryWaveReq req) {
        //  获取版本列表中最新版本
        PplVersionDO pplVersionDO = getNewestVersion();
        if (pplVersionDO == null) {
            throw new BizException("版本号列表为空");
        }
        String newestVersion = pplVersionDO.getVersionCode();
        //  1、获取本期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> newestVersionData =
                getNewestVersionData(newestVersion, req);
        //  2、获取上期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersionData =
                getLastVersionData(req.getBeginYearMonth(), req.getEndYearMonth(), newestVersion, req);
        //  3、两期进行比较
        //  这个接口的页面呈现【产品】是单选，因此直接get(0)即可
        return compare(req.getProduct().get(0), lastVersionData, newestVersionData);
    }

    @Override
    public QueryWaveOverviewGroupRsp waveGroupOverview(QueryWaveReq req) {
        //  获取版本列表中最新版本
        PplVersionDO pplVersionDO = getNewestVersion();
        if (pplVersionDO == null) {
            throw new BizException("版本号列表为空");
        }
        String newestVersion = pplVersionDO.getVersionCode();
        //  1、获取本期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> newestVersionData =
                getNewestVersionData(newestVersion, req);
        //  2、获取上期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersionData =
                getLastVersionData(req.getBeginYearMonth(), req.getEndYearMonth(), newestVersion, req);
        //  3、分维度对比
        return compareByFields(req.getProduct(), lastVersionData, newestVersionData, req.getBeginYearMonth(),
                req.getEndYearMonth());
    }

    @Override
    public QueryWaveOverviewDetailRsp waveGroupDetail(QueryWaveReq req) {
        //  获取版本列表中最新版本
        PplVersionDO pplVersionDO = getNewestVersion();
        if (pplVersionDO == null) {
            throw new BizException("版本号列表为空");
        }
        String newestVersion = pplVersionDO.getVersionCode();
        //  1、获取本期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> newestVersionData =
                getNewestVersionData(newestVersion, req);
        //  2、获取上期数据
        List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersionData =
                getLastVersionData(req.getBeginYearMonth(), req.getEndYearMonth(), newestVersion, req);
        //  3、对两个版本的数据根据同一个K进行分组
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> curVerMap =
                ListUtils.groupBy(newestVersionData, DwdCrpPplItemVersionForWaveAnalysisVO::getGroupK);
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> lastVerMap =
                ListUtils.groupBy(lastVersionData, DwdCrpPplItemVersionForWaveAnalysisVO::getGroupK);

        QueryWaveOverviewDetailRsp rsp = new QueryWaveOverviewDetailRsp();
        List<QueryWaveOverviewDetailRsp.ItemData> list = Lang.list();
        rsp.setData(list);

        Set<String> allKey = Lang.set();
        allKey.addAll(curVerMap.keySet());
        allKey.addAll(lastVerMap.keySet());

        for (String k : allKey) {
            List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVerList = lastVerMap.get(k);
            List<DwdCrpPplItemVersionForWaveAnalysisVO> curVerList = curVerMap.get(k);

            // 上期没有这个k，则根据本期中这个k的任意一个对象构建出一个值为0的加进去
            if (ListUtils.isEmpty(lastVerList)) {
                lastVerList = Lang.list();
                DwdCrpPplItemVersionForWaveAnalysisVO one = curVerList.get(0);
                DwdCrpPplItemVersionForWaveAnalysisVO copyOne = DwdCrpPplItemVersionForWaveAnalysisVO.copyOne(one);
                lastVerList.add(copyOne);
            }

            // 本期没有这个k，则根据上期中这个k的任意一个对象构建出一个值为0的加进去
            if (ListUtils.isEmpty(curVerList)) {
                curVerList = Lang.list();
                DwdCrpPplItemVersionForWaveAnalysisVO one = lastVerList.get(0);
                DwdCrpPplItemVersionForWaveAnalysisVO copyOne = DwdCrpPplItemVersionForWaveAnalysisVO.copyOne(one);
                curVerList.add(copyOne);
            }

            //  到这里，同组K的每个版本中都至少有一条了，因此传入任一VO即可
            //  生成并填充基础属性
            QueryWaveOverviewDetailRsp.ItemData one = QueryWaveOverviewDetailRsp.ItemData.getOne(lastVerList.get(0));
            //  设置行业分类
            one.setCategory(industryReportDictService.getCategoryByDept(lastVerList.get(0).getIndustryDept()));
            //  填充带值的属性
            QueryWaveOverviewDetailRsp.NumDTO lastVerDTO = new QueryWaveOverviewDetailRsp.NumDTO();
            lastVerDTO.setCoreNum(NumberUtils.sum(lastVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
            lastVerDTO.setInstanceNum(
                    NumberUtils.sum(lastVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getInstanceNum));
            lastVerDTO.setDiskNum(NumberUtils.sum(lastVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalDisk));
            lastVerDTO.setGpuCardNum(
                    NumberUtils.sum(lastVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum));

            QueryWaveOverviewDetailRsp.NumDTO curVerDTO = new QueryWaveOverviewDetailRsp.NumDTO();
            curVerDTO.setCoreNum(NumberUtils.sum(curVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
            curVerDTO.setInstanceNum(
                    NumberUtils.sum(curVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getInstanceNum));
            curVerDTO.setDiskNum(NumberUtils.sum(curVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalDisk));
            curVerDTO.setGpuCardNum(NumberUtils.sum(curVerList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum));

            QueryWaveOverviewDetailRsp.NumDTO diffDTO = new QueryWaveOverviewDetailRsp.NumDTO();
            diffDTO.setCoreNum(add(curVerDTO.getCoreNum(), negativeNum(lastVerDTO.getCoreNum())));
            diffDTO.setInstanceNum(add(curVerDTO.getInstanceNum(), negativeNum(lastVerDTO.getInstanceNum())));
            diffDTO.setDiskNum(add(curVerDTO.getDiskNum(), negativeNum(lastVerDTO.getDiskNum())));
            diffDTO.setGpuCardNum(add(curVerDTO.getGpuCardNum(), negativeNum(lastVerDTO.getGpuCardNum())));

            QueryWaveOverviewDetailRsp.NumDTO ratioDTO = new QueryWaveOverviewDetailRsp.NumDTO();
            ratioDTO.setCoreNum(AmountUtils.divideScale6(diffDTO.getCoreNum(), lastVerDTO.getCoreNum()));
            ratioDTO.setInstanceNum(AmountUtils.divideScale6(diffDTO.getInstanceNum(), lastVerDTO.getInstanceNum()));
            ratioDTO.setDiskNum(AmountUtils.divideScale6(diffDTO.getDiskNum(), lastVerDTO.getDiskNum()));
            ratioDTO.setGpuCardNum(AmountUtils.divideScale6(diffDTO.getGpuCardNum(), lastVerDTO.getGpuCardNum()));

            one.setLastVersion(lastVerDTO);
            one.setCurVersion(curVerDTO);
            one.setDiff(diffDTO);
            one.setRatio(ratioDTO);
            list.add(one);
        }
        return rsp;
    }

    @Override
    @SneakyThrows
    public QueryWaveDetailRsp waveDetail(QueryWaveReq req) {
        //  请求体中的筛选条件
        ORMUtils.WhereContent content = req.getWaveDetailContent();
        req.setSourceContent(content);
        appendAuthWhere(content, req.getProduct());
        if (req.getProductCategory().equals("CBS")) {
            if (ListUtils.isEmpty(req.getVolumeType())) {
                req.setVolumeType(PplWaveVolumeTypeEnum.getAllName());
            }
            if (ListUtils.isEmpty(req.getDiskType())) {
                req.setDiskType(PplWaveDiskTypeEnum.getAllName());
            }
        }

        boolean calcNetGrowth = req.calcNetGrowth();

        //  获取选取的初末时间内的所有年月列表
        List<Tuple2<Integer, Integer>> yearMonthList = getYearMonthList(req.getBeginYearMonth(), req.getEndYearMonth());

        List<CompletableFuture<List<Map<String, Object>>>> futureList = new ArrayList<>();
        // 2.2、532新版预测量（按行业数据看板532规则，对上面的所有版本数据进行计算）
        if (!req.getProductCategory().equals("CBS")) {
            futureList.add(CompletableFuture.supplyAsync(() ->
                            getVersion532NewPplItemSum(yearMonthList, content, calcNetGrowth, req),
                    ThreadPoolUtils.getWaveDetailPool()));
        }

        // 2.3、基准版
        futureList.add(CompletableFuture.supplyAsync(() ->
                        getVersionBasePplItemSum(yearMonthList, content, calcNetGrowth, req),
                ThreadPoolUtils.getWaveDetailPool()));

        // 2.4、最新版预测量（取每个月的最新运管版本的版本数据，注意月份需在运管版本的配置范围）
        futureList.add(CompletableFuture.supplyAsync(() ->
                        getNewestVersionByMonthSum(yearMonthList, content, calcNetGrowth, req),
                ThreadPoolUtils.getWaveDetailPool()));

        // 2.5、最新预测量（最新预测量、行业报备、系统补充；未转单量）
//        25/2/7 不在展示最新预测量、未转单量
//        futureList.add(CompletableFuture.supplyAsync(() -> getNewestPplItemSum(yearMonthList, content, calcNetGrowth),
//                ThreadPoolUtils.getWaveDetailPool()));

        // 2.6、预约量（实时预约单数据 - 预约量、有预测预约、无预测预约、中长尾预约、未分类）
//        futureList.add(CompletableFuture.supplyAsync(() -> getAppliedPplItemSum(yearMonthList, req),
//                ThreadPoolUtils.getWaveDetailPool()));
        // 2.6、订单量（汇总 - 订单量、PPL转单、紧急订单）
//        25/2/7 不在展示订单量
//        futureList.add(CompletableFuture.supplyAsync(() -> getNewAppliedPplItemSum(yearMonthList, req),
//                ThreadPoolUtils.getWaveDetailPool()));

        // 2.7、 0最新版PPL拼订单 （1行业PPL、1.1已转单PPL、1.2未转单PPL、  2行业订单、2.1PPL转单、2.2紧急订单）
        futureList.add(CompletableFuture.supplyAsync(
                SecurityContextUtils.wrapSupplier(() ->
                        getPplJoinOrderItemSum(yearMonthList, req, calcNetGrowth)
                ),
                ThreadPoolUtils.getWaveDetailPool()
        ));


        String versionItemSql = ORMUtils.getSql("/sql/ppl13week/wave_analysis/ppl_version_item_summary.sql");
        versionItemSql = versionItemSql.replace("${FILTER}", content.getSql());
        List<NewPplVersionGroupRecordItemWithOrderSumVO> allList = DBList.ckcldStdCrpDBHelper.getRaw(
                NewPplVersionGroupRecordItemWithOrderSumVO.class, versionItemSql,
                content.getParams());

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            allList = allList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        List<NewPplVersionGroupRecordItemWithOrderSumVO> allListByIndustry = allList.stream()
                .filter(o -> sourceOfIndustry.contains(o.getSource())).collect(
                        Collectors.toList());
        List<NewPplVersionGroupRecordItemWithOrderSumVO> allListByYun = allList.stream()
                .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1).collect(
                        Collectors.toList());

        //  groupby 年月、行业部门、版本号
        Map<String, String> industryCategoryMap = SpringUtil.getBean(IndustryReportDictServiceImpl.class)
                .queryCategory();
        Map<String, List<NewPplVersionGroupRecordItemWithOrderSumVO>> allMapByYun;
        Map<String, List<NewPplVersionGroupRecordItemWithOrderSumVO>> allMapByIndustry;
        if (req.getProductCategory().equals("CBS")) {
            allMapByYun =
                    ListUtils.groupBy(allListByYun, o -> join(o.getYearMonth(),
                            industryReportDictService.getCategoryByDept(o.getIndustryDept(), industryCategoryMap),
                            o.getVersionCode(), o.getDataDiskType(), o.getSystemDiskType()));
            allMapByIndustry =
                    ListUtils.groupBy(allListByIndustry, o -> join(o.getYearMonth(),
                            industryReportDictService.getCategoryByDept(o.getIndustryDept(), industryCategoryMap),
                            o.getVersionCode(),o.getDataDiskType(), o.getSystemDiskType()));
        }else {
            allMapByYun =
                    ListUtils.groupBy(allListByYun, o -> join(o.getYearMonth(),
                            industryReportDictService.getCategoryByDept(o.getIndustryDept(), industryCategoryMap),
                            o.getVersionCode()));
            allMapByIndustry =
                    ListUtils.groupBy(allListByIndustry, o -> join(o.getYearMonth(),
                            industryReportDictService.getCategoryByDept(o.getIndustryDept(), industryCategoryMap),
                            o.getVersionCode()));
        }

        List<NewPplVersionGroupRecordItemWithOrderSumVO> allByYun = newGetSumRecordItem(allMapByYun, calcNetGrowth);
        List<NewPplVersionGroupRecordItemWithOrderSumVO> allByIndustry = newGetSumRecordItem(allMapByIndustry,
                calcNetGrowth);

        List<String> versions = allList.stream()
                .sorted(Comparator.comparing(NewPplVersionGroupRecordItemWithOrderSumVO::getVersionCode))
                .map(NewPplVersionGroupRecordItemWithOrderSumVO::getVersionCode)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());

        Map<String, PplComdVersionVO> versionVOMap = allList.stream()
                .filter(e -> !StringUtils.isEmpty(e.getVersionCode()))
                .collect(Collectors.toMap(NewPplVersionGroupRecordItemWithOrderSumVO::getVersionCode, e -> {
                    PplComdVersionVO versionDO = new PplComdVersionVO();
                    versionDO.setVersionCode(e.getVersionCode());
                    versionDO.setVersionBeginYear(e.getVersionBeginYear());
                    versionDO.setVersionBeginMonth(e.getVersionBeginMonth());
                    versionDO.setVersionEndYear(e.getVersionEndYear());
                    versionDO.setVersionEndMonth(e.getVersionEndMonth());
                    return versionDO;
                }, (v1, v2) -> v1));

        // valueMapByYun: <YearMonth IndustryDept VersionCode, sumGpuNum/sumCoreNum>
        Map<String, BigDecimal> valueMapByYun = new HashMap<>();
        Map<String, BigDecimal> valueMapByIndustry = new HashMap<>();
        if (req.getProductCategory().equals("CBS")) {
            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByYunSystem = allByYun.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByYunSystem = allByYunSystem.stream().collect(Collectors.toMap(o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumSystemCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByYunData = allByYun.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByYunData = allByYunData.stream().collect(Collectors.toMap(o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumDataCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByIndustrySystem = allByIndustry.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByIndustrySystem = allByIndustrySystem.stream().collect(Collectors.toMap(o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumSystemCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByIndustryData = allByIndustry.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByIndustryData = allByIndustryData.stream().collect(Collectors.toMap(o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumDataCapacity, BigDecimal::add));

            if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                valueMapByYun = valueMapByYunSystem;
                valueMapByIndustry = valueMapByIndustrySystem;
            }else if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                valueMapByYun = valueMapByYunData;
                valueMapByIndustry = valueMapByIndustryData;
            }else {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(valueMapByIndustrySystem.keySet())) {
                    industryKey.addAll(valueMapByIndustrySystem.keySet());
                }
                if (ListUtils.isNotEmpty(valueMapByIndustryData.keySet())) {
                    industryKey.addAll(valueMapByIndustryData.keySet());
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(valueMapByYunSystem.keySet())) {
                    yunKey.addAll(valueMapByYunSystem.keySet());
                }
                if (ListUtils.isNotEmpty(valueMapByYunData.keySet())) {
                    yunKey.addAll(valueMapByYunData.keySet());
                }
                for (String key : industryKey) {
                    valueMapByIndustry.put(key, valueMapByIndustrySystem.getOrDefault(key, BigDecimal.ZERO).add(valueMapByIndustryData.getOrDefault(key, BigDecimal.ZERO)));
                }
                for (String key : yunKey) {
                    valueMapByYun.put(key, valueMapByYunSystem.getOrDefault(key, BigDecimal.ZERO).add(valueMapByYunData.getOrDefault(key, BigDecimal.ZERO)));
                }
            }
        }else {
            valueMapByYun = ListUtils.toMap(allByYun,
                    o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    o -> ListUtils.isNotEmpty(req.getProduct()) && req.getProduct().contains("GPU(裸金属&CVM)")
                            ? BigDecimal.valueOf(o.getSumDemandGpuNum()) : BigDecimal.valueOf(o.getSumDemandCoreNum()));
            // valueMapByIndustry: <YearMonth IndustryDept VersionCode, sumGpuNum/sumCoreNum>
            valueMapByIndustry = ListUtils.toMap(allByIndustry,
                    o -> join(o.getYearMonth(), o.getIndustryDept(), o.getVersionCode()),
                    o -> ListUtils.isNotEmpty(req.getProduct()) && req.getProduct().contains("GPU(裸金属&CVM)")
                            ? BigDecimal.valueOf(o.getSumDemandGpuNum()) : BigDecimal.valueOf(o.getSumDemandCoreNum()));
        }

        //  <K,V> -> <版本号, 行业部门集合>
        Map<String, Set<String>> industrysByVersionMap = allByYun.stream()
                .reduce(new HashMap<>(), (r, i) -> {
                    Set<String> set = r.get(i.getVersionCode());
                    if (set == null) {
                        set = new HashSet<>();
                    }
                    set.add(i.getIndustryDept());
                    r.put(i.getVersionCode(), set);
                    return r;
                }, (e1, e2) -> e1);
        industrysByVersionMap.putAll(allByIndustry.stream()
                .reduce(new HashMap<>(), (r, i) -> {
                    Set<String> set = r.get(i.getVersionCode());
                    if (set == null) {
                        set = new HashSet<>();
                    }
                    set.add(i.getIndustryDept());
                    r.put(i.getVersionCode(), set);
                    return r;
                }, (e1, e2) -> e1));

        //  valueYearMapByYun:   <VersionCode YearMonth, 核心数总和>
        Map<String, BigDecimal> valueYearMapByYun = new HashMap<>();
        Map<String, BigDecimal> valueYearMapByIndustry = new HashMap<>();
        if (req.getProductCategory().equals("CBS")) {
            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByYunSystem = allByYun.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByYunSystem = allByYunSystem.stream().collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getYearMonth()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumSystemCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByYunData = allByYun.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByYunData = allByYunData.stream().collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getYearMonth()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumDataCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByIndustrySystem = allByIndustry.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByIndustrySystem = allByIndustrySystem.stream().collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getYearMonth()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumSystemCapacity, BigDecimal::add));

            List<NewPplVersionGroupRecordItemWithOrderSumVO> allByIndustryData = allByIndustry.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> valueMapByIndustryData = allByIndustryData.stream().collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getYearMonth()),
                    NewPplVersionGroupRecordItemWithOrderSumVO::getSumDataCapacity, BigDecimal::add));;

            if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                valueYearMapByYun = valueMapByYunSystem;
                valueYearMapByIndustry = valueMapByIndustrySystem;
            }else if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                valueYearMapByYun = valueMapByYunData;
                valueYearMapByIndustry = valueMapByIndustryData;
            }else {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(valueMapByIndustrySystem.keySet())) {
                    industryKey.addAll(valueMapByIndustrySystem.keySet());
                }
                if (ListUtils.isNotEmpty(valueMapByIndustryData.keySet())) {
                    industryKey.addAll(valueMapByIndustryData.keySet());
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(valueMapByYunSystem.keySet())) {
                    yunKey.addAll(valueMapByYunSystem.keySet());
                }
                if (ListUtils.isNotEmpty(valueMapByYunData.keySet())) {
                    yunKey.addAll(valueMapByYunData.keySet());
                }
                for (String key : industryKey) {
                    valueYearMapByIndustry.put(key, valueMapByIndustrySystem.getOrDefault(key, BigDecimal.ZERO).add(valueMapByIndustryData.getOrDefault(key, BigDecimal.ZERO)));
                }
                for (String key : yunKey) {
                    valueYearMapByYun.put(key, valueMapByYunSystem.getOrDefault(key, BigDecimal.ZERO).add(valueMapByYunData.getOrDefault(key, BigDecimal.ZERO)));
                }
            }
        }else {
            valueYearMapByYun = allByYun.stream()
                    .collect(Collectors.toMap(
                            o -> join(o.getVersionCode(), o.getYearMonth()),
                            o -> ListUtils.isNotEmpty(req.getProduct()) && req.getProduct().contains("GPU(裸金属&CVM)")
                                    ? BigDecimal.valueOf(o.getSumDemandGpuNum()) : BigDecimal.valueOf(o.getSumDemandCoreNum()),
                            BigDecimal::add));
            //  valueYearMapByIndustry:   <VersionCode YearMonth, 核心数总和>
            valueYearMapByIndustry = allByIndustry.stream()
                    .collect(Collectors.toMap(
                            o -> join(o.getVersionCode(), o.getYearMonth()),
                            o -> ListUtils.isNotEmpty(req.getProduct()) && req.getProduct().contains("GPU(裸金属&CVM)")
                                    ? BigDecimal.valueOf(o.getSumDemandGpuNum()) : BigDecimal.valueOf(o.getSumDemandCoreNum()),
                            BigDecimal::add));
        }

        QueryWaveDetailRsp rsp = new QueryWaveDetailRsp();
        List<QueryWaveDetailRsp.ColumnName> tableTitle = Lang.list();
        rsp.setTableTitle(tableTitle);

        List<Map<String, Object>> data = Lang.list();
        rsp.setData(data);

        // 1、构造标题
        tableTitle.add(new QueryWaveDetailRsp.ColumnName("版本号", "version"));
        for (Tuple2<Integer, Integer> tuple2 : yearMonthList) {
            tableTitle.add(new QueryWaveDetailRsp.ColumnName(toYearMonth(tuple2), "y" + tuple2._1 + "m" + tuple2._2));
        }

        // 增加合计标题
        tableTitle.add(new QueryWaveDetailRsp.ColumnName("合计", "total"));

        //  2、构造数据
        // 2.1、版本数据
        List<Map<String, List<WaveValueDTO>>> recordDataList = new ArrayList<>();
        for (int index = 0; index < versions.size(); index++) {
            String version = versions.get(index);
            PplComdVersionVO pplComdVersionVO = versionVOMap.get(version);
            Map<String, Object> row = new LinkedHashMap<>();
            data.add(row);
            row.put("version", version);

            LocalDate versionBeginDate = null;
            LocalDate versionEndDate = null;
            if (pplComdVersionVO != null) {
                String versionBeginDateStr = toYearMonth(pplComdVersionVO.getVersionBeginYear(),
                        pplComdVersionVO.getVersionBeginMonth());
                String versionEndDateStr = toYearMonth(pplComdVersionVO.getVersionEndYear(),
                        pplComdVersionVO.getVersionEndMonth());
                row.put("versionBeginDate", versionBeginDateStr);
                row.put("versionEndDate", versionEndDateStr);
                versionBeginDate = YearMonth.parse(versionBeginDateStr).atDay(1);
                versionEndDate = YearMonth.parse(versionEndDateStr).atEndOfMonth();
            }

            Map<String, List<WaveValueDTO>> recordRowMap = new LinkedHashMap<>();
            recordDataList.add(recordRowMap);
            for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
                String yearMonthStr = toYearMonth(yearMonth);
                LocalDate currentDate = YearMonth.parse(yearMonthStr).atDay(1);
                String hoverInfo = null;
                if (versionBeginDate != null && versionEndDate != null) {
                    // 行业审批版本和13周版本之间的时差问题会导致13周版本存在配置范围外的数据，这部分数据在13周版本中不展示，在波动明细中进行hover提示
                    hoverInfo =
                            currentDate.isBefore(versionBeginDate) || currentDate.isAfter(versionEndDate) ? "非版本周期内数据"
                                    : null;
                }

                BigDecimal sumByYun = valueYearMapByYun.get(join(version, yearMonthStr));
                String valueByYun = sumByYun == null ? "" : sumByYun.toString();
                BigDecimal sumByIndustry = valueYearMapByIndustry.get(join(version, yearMonthStr));
                String valueByIndustry = sumByIndustry == null ? "" : sumByIndustry.toString();

                String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
                List<WaveValueDTO> valueDTOs = new ArrayList<>();
                WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), valueByYun, null,
                        StringUtils.isEmpty(valueByYun) ? null : hoverInfo);
                WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                        valueByIndustry, null, StringUtils.isEmpty(valueByIndustry) ? null : hoverInfo);
                valueDTOs.add(valueDTOByYun);
                valueDTOs.add(valueDTOByIndustry);
                row.put(dataIndex, valueDTOs);
                recordRowMap.put(dataIndex, valueDTOs);

                // 如果没有上一版本（上一版本是上一个index），或当前版本无数据情况（""），或上一版本无数据，info保持为null
                if ((StringTools.isBlank(valueByYun) && StringTools.isBlank(valueByIndustry)) || index == 0) {
                    continue;
                }

                Map<String, List<WaveValueDTO>> lastRow = recordDataList.get(index - 1);
                List<WaveValueDTO> lastValueDTOs = lastRow.get(dataIndex);
                Map<String, String> lastValueDTOMap = lastValueDTOs.stream()
                        .collect(Collectors.toMap(WaveValueDTO::getDataColumn, WaveValueDTO::getValue, (v1, v2) -> v2));

                for (WaveValueDTO valueDTO : valueDTOs) {
                    String lastValue = lastValueDTOMap.get(valueDTO.getDataColumn());
                    if (StringTools.isBlank(lastValue)) {
                        continue;
                    }

                    String lastVersion = versions.get(index - 1);
                    String diffNum = diffNum(valueDTO.getValue(), lastValue);
                    WaveValueDTO.Info info =
                            new WaveValueDTO.Info(lastVersion, diffNum, diffPercentage(diffNum, lastValue),
                                    null);
                    valueDTO.setInfo(info);

                    //  // 如果是"腾讯云"层级，展示变化明细
                    if (ListUtils.isEmpty(req.getIndustryDept()) && ListUtils.isEmpty(req.getInstanceType())) {
                        Set<String> industryDepts = industrysByVersionMap.get(version);
                        industryDepts.addAll(industrysByVersionMap.getOrDefault(lastVersion, new HashSet<>()));
                        List<WaveValueDTO.Detail> details = Lang.list();
                        for (String industryDept : industryDepts) {
                            BigDecimal isum = StatisticalScopeEnum.YUN_PPL.getName().equals(valueDTO.getDataColumn())
                                    ? valueMapByYun.get(
                                    join(toYearMonth(yearMonth), industryDept, version)) : valueMapByIndustry.get(
                                    join(toYearMonth(yearMonth), industryDept, version));
                            String ivalue = isum == null ? "" : isum.toString();

                            BigDecimal lastIsum = StatisticalScopeEnum.YUN_PPL.getName().equals(valueDTO.getDataColumn())
                                    ? valueMapByYun.get(
                                    join(toYearMonth(yearMonth), industryDept, lastVersion)) : valueMapByIndustry.get(
                                    join(toYearMonth(yearMonth), industryDept, lastVersion));
                            String lastIValue = lastIsum == null ? "" : lastIsum.toString();

                            String detailDiffNum = diffNum(ivalue, lastIValue);
                            if (!Objects.equals("0", detailDiffNum)) {
                                details.add(new WaveValueDTO.Detail(
                                        industryDept, detailDiffNum,
                                        diffPercentage(detailDiffNum, lastIValue)));
                            }
                        }
                        ListUtils.sortDescNullLast(details, Detail::getPercentage);
                        info.setDetails(details);
                    }

                }


            }

            // 计算合计
            List<WaveValueDTO> flatList = recordRowMap.values().stream().flatMap(List::stream)
                    .collect(Collectors.toList());
            Integer industry = NumberUtils.sum(
                    flatList.stream().filter(o -> o.getDataColumn().equals(StatisticalScopeEnum.INDUSTRY_PPL.getName()))
                            .collect(
                                    Collectors.toList()), WaveValueDTO::getValue).setScale(0, RoundingMode.HALF_UP).intValue();
            Integer yun = NumberUtils.sum(
                    flatList.stream().filter(o -> o.getDataColumn().equals(StatisticalScopeEnum.YUN_PPL.getName()))
                            .collect(
                                    Collectors.toList()), WaveValueDTO::getValue).setScale(0, RoundingMode.HALF_UP).intValue();
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), yun.toString(), null,
                    null);
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(), industry.toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            row.put("total",valueDTOs);

        }

        // 按版本逆序排序
//        Map<String, String> versionMap = ListUtils.toMap(allList,
//                NewPplVersionGroupRecordItemWithOrderSumVO::getVersionCode,
//                NewPplVersionGroupRecordItemWithOrderSumVO::getVersionCode);
        ListUtils.sortDescNullLast(data, o -> (String) o.get("version"));

        // 3、整合数据
        for (CompletableFuture<List<Map<String, Object>>> future : futureList) {
            data.addAll(future.get(5, TimeUnit.SECONDS));
        }

        return rsp;
    }

    public List<String> queryRelevantVersion(cloud.demand.app.common.utils.DateUtils.YearMonth start,
            cloud.demand.app.common.utils.DateUtils.YearMonth end) {
        List<String> versionCodeList = demandDBHelper
                .getRaw(String.class,
                        "select version_code,demand_begin_year,demand_begin_month from ppl_version where deleted = 0\n"
                                + " and (demand_begin_year > ? or (demand_begin_year = ? and demand_begin_month >= ?))\n"
                                + "and (demand_begin_year < ? or (demand_begin_year = ? and demand_begin_month <= ?))",
                        start.getYear(), start.getYear(), start.getMonth(), end.getYear(), end.getYear(),
                        end.getMonth());
        return ListUtils.isEmpty(versionCodeList) ? new ArrayList<>() : versionCodeList;
    }

    @Override
    @SneakyThrows
    public DownloadBean exportWaveDetail(QueryWaveReq req) {
        //  请求体中的筛选条件
        ORMUtils.WhereContent content = req.getWaveDetailContent();
        req.setSourceContent(content);
        appendAuthWhere(content, req.getProduct());

        if (ListUtils.isEmpty(req.getStatisticalScope())) {
            req.setStatisticalScope(
                    Arrays.asList(StatisticalScopeEnum.INDUSTRY_PPL.getCode(), StatisticalScopeEnum.YUN_PPL.getCode()));
        }
        if (req.getProductCategory().equals("CBS")) {
            if (ListUtils.isEmpty(req.getVolumeType())) {
                req.setVolumeType(PplWaveVolumeTypeEnum.getAllName());
            }
            if (ListUtils.isEmpty(req.getDiskType())) {
                req.setDiskType(PplWaveDiskTypeEnum.getAllName());
            }
        }

        // 查询版本数据
        List<PplVersionGroupRecordItemWithOrderSumVO> allList = DBList.ckcldStdCrpDBHelper
                .getAll(PplVersionGroupRecordItemWithOrderSumVO.class, content.getSql(), content.getParams());

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            allList = allList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        // 获取 通用实例类型 配置关系
        Pair<List<InstanceTypeRelateDTO>, Map<String, String>> pair = pplDictService.queryInstanceTypeRelate();
        Map<String, String> instanceConfigMap = pair.getValue();

        // 获取13周版本时间范围配置 Map<versionCode, versionDO>
        Map<String, PplVersionDO> pplVersionConfigMap = demandDBHelper.getAll(PplVersionDO.class).stream()
                .collect(Collectors.toMap(PplVersionDO::getVersionCode, e -> e, (v1, v2) -> v2));

        // 1、开始构建明细excel数据
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();
        List<CompletableFuture<List<WaveDetailExportExcelDTO>>> futureList = new ArrayList<>();
        boolean calcNetGrowth = req.calcNetGrowth();
        // 1.1、版本数据
        List<PplVersionGroupRecordItemWithOrderSumVO> finalAllList = allList;
        futureList.add(CompletableFuture.supplyAsync(
                () -> buildVersionItemExcelData(finalAllList, instanceConfigMap, pplVersionConfigMap,
                        req.getStatisticalScope(), calcNetGrowth, req),
                ThreadPoolUtils.getWaveDetailPool()));
        // 1.2、532新版预测量
        if (!req.getProductCategory().equals("CBS")) {
            futureList.add(CompletableFuture.supplyAsync(
                    () -> build532NewVersionItemExcelData(content, instanceConfigMap, pplVersionConfigMap,
                            req.getStatisticalScope(),
                            calcNetGrowth, req),
                    ThreadPoolUtils.getWaveDetailPool()));
        }
        // 1.3、基准版
        futureList.add(CompletableFuture.supplyAsync(
                () -> buildVersionBaseItemExcelData(content, instanceConfigMap, pplVersionConfigMap,
                        req.getStatisticalScope(),
                        calcNetGrowth, req),
                ThreadPoolUtils.getWaveDetailPool()));
        // 1.4、最新版预测量（取每个月的最新运管版本的版本数据，注意月份需在运管版本的配置范围）
        futureList.add(CompletableFuture.supplyAsync(
                () -> buildNewestVersionItemExcelData(content, instanceConfigMap, pplVersionConfigMap,
                        req.getStatisticalScope(),
                        calcNetGrowth, req),
                ThreadPoolUtils.getWaveDetailPool()));
        // 1.5、最新预测量（最新预测量、行业报备、系统补充；未转单量）  25/2/7 废弃该指标
//        futureList.add(CompletableFuture.supplyAsync(
//                () -> buildNewestItemExcelData(content, instanceConfigMap, req.getStatisticalScope(), calcNetGrowth),
//                ThreadPoolUtils.getWaveDetailPool()));
        // 1.6、预约量（实时预约单数据 - 有预测预约、无预测预约、中长尾预约、未分类）
//        futureList.add(CompletableFuture.supplyAsync(() -> buildAppliedItemExcelData(req, instanceConfigMap),
//                ThreadPoolUtils.getWaveDetailPool()));
        // 2.6、订单量（汇总 - 订单量、PPL转单、紧急订单） 25/2/7 废弃该指标
//        futureList.add(CompletableFuture.supplyAsync(() -> buildNewAppliedItemExcelData(req, instanceConfigMap),
//                ThreadPoolUtils.getWaveDetailPool()));

        // 1.5 版本拼接宽表相关指标
        futureList.add(CompletableFuture.supplyAsync(() -> buildPplJoinOrderVersionItemExcelData(req),
                ThreadPoolUtils.getWaveDetailPool()));

        // 整合数据
        for (Future<List<WaveDetailExportExcelDTO>> future : futureList) {
            // 导出超时放久一点
            data.addAll(future.get(10, TimeUnit.SECONDS));
        }

        for (WaveDetailExportExcelDTO datum : data) {
            if (datum.getDemandType().equals(PplDemandTypeEnum.RETURN.getName())){
                // 退回用负数表示
                datum.setTargetValue(-datum.getTargetValue());
            }
        }

        // 2、开始构建excel文件
        InputStream templateIn = !req.getProductCategory().equals("CBS") ? IOUtils.readClasspathResourceInputStream(
                "excel/wave_detail/default_wave_detail_export.xlsx") : IOUtils.readClasspathResourceInputStream(
                "excel/wave_detail/default_wave_detail_export_cbs.xlsx");

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new PplExportWriteHandler(new HashMap<>()))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(new FillWrapper("item", data), writeSheet).finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                "需求变化波动分析-波动明细数据导出" + com.pugwoo.wooutils.lang.DateUtils.format(new Date(),
                        "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return new DownloadBean(fileNameAndBytesDTO.getFileName(), fileNameAndBytesDTO.getBytes());
    }

    @Override
    public QueryVersionInfoRsp queryNewestVersionInfo() {
        String sql = "select * from ppl_version " +
                "where deleted = 0 order by id desc limit 2";
        //  取最新的两个版本
        List<PplVersionDO> versions = demandDBHelper.getRaw(PplVersionDO.class, sql);
        if (ListUtils.isEmpty(versions)) {
            throw new BizException("版本查询失败");
        }
        if (versions.size() < 2) {
            throw new BizException("版本数据缺失,版本数只有1个");
        }
        PplVersionDO newestVersionInfo = versions.get(0);
        String beginYearMonth = toYearMonth(newestVersionInfo.getDemandBeginYear(),
                newestVersionInfo.getDemandBeginMonth());
        String endYearMonth = toYearMonth(newestVersionInfo.getDemandEndYear(), newestVersionInfo.getDemandEndMonth());

        PplVersionDO nextNewestVersionInfo = versions.get(1);
        String lastBeginYearMonth = toYearMonth(nextNewestVersionInfo.getDemandBeginYear(),
                nextNewestVersionInfo.getDemandBeginMonth());
        String lastEndYearMonth = toYearMonth(nextNewestVersionInfo.getDemandEndYear(),
                nextNewestVersionInfo.getDemandEndMonth());

        QueryVersionInfoRsp rsp = new QueryVersionInfoRsp();
        rsp.setVersionCode(newestVersionInfo.getVersionCode());
        rsp.setBeginYearMonth(beginYearMonth);
        rsp.setEndYearMonth(endYearMonth);
        Map<String, Object> map = new HashMap<>();
        rsp.setPreVersion(map);
        map.put("versionCode", nextNewestVersionInfo.getVersionCode());
        map.put("beginYearMonth", lastBeginYearMonth);
        map.put("endYearMonth", lastEndYearMonth);
        return rsp;
    }

    @Override
    public List<Map<String, Object>> queryAllIndustryDeptForWaveDetail() {
        List<Map<String, Object>> industryOfCategoryList = new ArrayList<>();

        //  1、获取全量映射表
        String sql = "select distinct industry_dept from ppl_order where deleted = 0 and industry_dept is not null and industry_dept != '' and industry_dept != '未知' order by industry_dept";
        List<String> industryDepts = demandDBHelper.getRaw(String.class, sql);

        // 2、获取 行业部门-分类 配置关系
        Map<String, String> categoryMap = industryReportDictService.queryCategory();

        // 3、开始整合返回
        for (String industryDept : industryDepts) {
            Map<String, Object> map = new HashMap<>();
            map.put("industryDept", industryDept);
            map.put("category", categoryMap.get(industryDept));
            industryOfCategoryList.add(map);
        }

        return industryOfCategoryList;
    }

    @Override
    public QueryIndustryDeptCustomerRsp queryIndustryDeptCustomerTree(QueryWaveReq req) {
        // 1、获取版本宽表中，行业-实例类型 分组信息
        String sql = ORMUtils.getSql("/sql/ppl13week/wave_analysis/all_industry_dept_instance_type.sql");
        List<IndustryDeptInstanceTypeDTO> dtos = DBList.ckcldStdCrpDBHelper.getRaw(IndustryDeptInstanceTypeDTO.class,
                sql);
        List<QueryIndustryDeptCustomerRsp.Node> nodes = Lang.list();

        // 2、获取 行业部门-分类 配置关系
        Map<String, String> map = industryReportDictService.queryCategory();

        // 行业结构树进行权限控制
        Set<String> authIndustrySet = authIndustryFilter(map.keySet(), req.getProduct());

        // 3、获取 通用实例类型 配置关系
        Pair<List<InstanceTypeRelateDTO>, Map<String, String>> pair = pplDictService.queryInstanceTypeRelate();
        Map<String, String> instanceConfigMap = pair.getValue();

        // 4、开始整合返回 Map<Industry, List<InstanceType>>
        Map<String, List<IndustryDeptInstanceTypeDTO>> normalMap = ListUtils.groupBy(dtos,
                o -> map.get(o.getIndustryDept()));
        for (Map.Entry<String, List<IndustryDeptInstanceTypeDTO>> entry : normalMap.entrySet()) {
            if (!authIndustrySet.contains(entry.getKey())) {
                continue;
            }

            // <CommonInstanceType, List<InstanceType>>
            Map<String, List<String>> instanceTypesByCommonMap = entry.getValue().stream().collect(
                    Collectors.groupingBy(o -> instanceConfigMap.getOrDefault(o.getInstanceType(), "无通用实例类型"),
                            Collectors.mapping(IndustryDeptInstanceTypeDTO::getInstanceType, Collectors.toList())));
            List<QueryIndustryDeptCustomerRsp.Node> children = new ArrayList<>();
            for (Map.Entry<String, List<String>> commonEntry : instanceTypesByCommonMap.entrySet()) {
                QueryIndustryDeptCustomerRsp.Node node = new QueryIndustryDeptCustomerRsp.Node();
                node.setType("commonInstanceType");
                node.setName(commonEntry.getKey());
                node.setInstanceTypes(commonEntry.getValue());
                children.add(node);
            }
            children = children.stream().distinct().collect(Collectors.toList());

            QueryIndustryDeptCustomerRsp.Node node = new QueryIndustryDeptCustomerRsp.Node();
            node.setType("category");
            node.setName(entry.getKey());
            node.setChildren(children);
            nodes.add(node);
        }

        //  最外层套一层腾讯云，写死的
        QueryIndustryDeptCustomerRsp.Node root = new QueryIndustryDeptCustomerRsp.Node();
        root.setType("");
        root.setName("腾讯云");
        root.setChildren(nodes);

        QueryIndustryDeptCustomerRsp rsp = new QueryIndustryDeptCustomerRsp();
        rsp.setNodes(Lang.list(root));
        return rsp;
    }

    @Override
    public Map<String, List<String>> queryWaveProductCategory() {
        Map<String, List<String>> result = PplWaveProductCategoryEnum.getProductCategoryExpectDataBase();
        return result;
    }

    @Data
    public static class IndustryDeptInstanceTypeDTO {

        @Column("industry_dept")
        private String industryDept;

        @Column("instance_type")
        private String instanceType;
    }

    /**
     * 计算差异核心数
     */
    private String diffNum(String originNumStr, String newNumStr) {
        int originNum;
        int newNum;
        try {
            originNum = Integer.parseInt(originNumStr);
        } catch (Exception e) {
            originNum = 0;
        }
        try {
            newNum = Integer.parseInt(newNumStr);
        } catch (Exception e) {
            newNum = 0;
        }
        return String.valueOf(originNum - newNum);
    }

    /**
     * 计算差异百分比
     */
    private Double diffPercentage(String diffNumStr, String originNumStr) {
        int diffNum;
        int originNum;
        try {
            diffNum = Integer.parseInt(diffNumStr);
        } catch (Exception e) {
            diffNum = 0;
        }
        try {
            originNum = Integer.parseInt(originNumStr);
        } catch (Exception e) {
            originNum = 0;
        }
        if (originNum == 0) {
            return diffNum * 100.0;
        } else {
            return ((double) diffNum / originNum) * 100;
        }
    }

    /**
     * 转换为yyyy-MM的格式
     */
    private String toYearMonth(Tuple2<Integer, Integer> tuple) {
        return String.join("-", String.valueOf(tuple._1),
                tuple._2 >= 10 ? String.valueOf(tuple._2) : "0" + tuple._2);
    }

    /**
     * 转换为yyyy-MM的格式
     */
    private String toYearMonth(int year, int month) {
        return String.join("-", String.valueOf(year),
                month >= 10 ? String.valueOf(month) : "0" + month);
    }

    private String join(Object... objs) {
        return StringTools.join(":", objs);
    }

    /**
     * 生成BY维度的波动分析对比Rsp
     */
    private QueryWaveOverviewGroupRsp compareByFields(List<String> product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersion,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> curVersion,
            Date start, Date end) {
        QueryWaveOverviewGroupRsp rsp = new QueryWaveOverviewGroupRsp();
        rsp.setLastVersion(build(product, lastVersion, start, end));
        rsp.setCurVersion(build(product, curVersion, start, end));
        return rsp;
    }

    /**
     * 构建ItemData
     */
    private QueryWaveOverviewGroupRsp.ItemData build(List<String> product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> data,
            Date start, Date end) {
        QueryWaveOverviewGroupRsp.ItemData itemData = new QueryWaveOverviewGroupRsp.ItemData();
        itemData.setYearMonthNum(genTotalByMonth(product, data, start, end));
        itemData.setRegion(genRatioList(product, data, "region"));
        itemData.setCustomer(genRatioList(product, data, "customer"));
        itemData.setWarZone(genRatioList(product, data, "warZone"));
        itemData.setInstanceType(genLogicNumList(product, data, "instanceType"));
        itemData.setInstanceModel(genLogicNumList(product, data, "instanceModel"));
        return itemData;
    }

    /**
     * 生成By年月的柱状概览图
     */
    private List<QueryWaveOverviewGroupRsp.TotalNumDTO> genTotalByMonth(List<String> product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> data,
            Date start, Date end) {
        List<QueryWaveOverviewGroupRsp.TotalNumDTO> result = Lang.list();
        //  获取start到end间的所有年月
        List<Tuple2<Integer, Integer>> yearMonthList = getYearMonthList(start, end);
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> yearMonthMap =
                ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getYearMonth);
        //  填充每个年月的柱状图
        for (Tuple2<Integer, Integer> tuple2 : yearMonthList) {
            Integer year = tuple2._1;
            Integer month = tuple2._2;
            String yearMonth = year + "-" + (month >= 10 ? month : "0" + month);
            List<DwdCrpPplItemVersionForWaveAnalysisVO> list = yearMonthMap.get(yearMonth);
            QueryWaveOverviewGroupRsp.TotalNumDTO dto = new QueryWaveOverviewGroupRsp.TotalNumDTO();
            dto.setYearMonth(yearMonth);
            dto.setTotalCore(NumberUtils.sum(list, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore).intValue());
            dto.setTotalDisk(NumberUtils.sum(list, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalDisk).intValue());
            dto.setTotalGpuCard(NumberUtils.sum(list, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum));
            result.add(dto);
        }
        return result;
    }

    /**
     * 生成占比类型维度的List
     */
    private List<QueryWaveOverviewGroupRsp.ResultDTO> genRatioList(List<String> product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> data,
            String groupBy) {
        List<QueryWaveOverviewGroupRsp.ResultDTO> result = Lang.list();
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> groupMap = new HashMap<>();
        if (Objects.equals("region", groupBy)) {
            groupMap = ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getRegionName);
        } else if (Objects.equals("customer", groupBy)) {
            groupMap = ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getCustomerShortName);
        } else if (Objects.equals("warZone", groupBy)) {
            groupMap = ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getWarZone);
        }

        BigDecimal totalSum = product.contains("GPU(裸金属&CVM)")
                ? NumberUtils.sum(data, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                : NumberUtils.sum(data, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore);
        for (Map.Entry<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> entry : groupMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal curSum = product.contains("GPU(裸金属&CVM)")
                    ? NumberUtils.sum(entry.getValue(), DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                    : NumberUtils.sum(entry.getValue(), DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore);
            QueryWaveOverviewGroupRsp.ResultDTO dto = new QueryWaveOverviewGroupRsp.ResultDTO();
            dto.setName(key);
            dto.setLogicNum(curSum.intValue());
            dto.setRatio(AmountUtils.divideScale6(curSum, totalSum));
            result.add(dto);
        }
        ListUtils.sortDescNullLast(result, ResultDTO::getLogicNum);
        return ListUtils.filter(result, o -> o.getRatio().compareTo(BigDecimal.ZERO) > 0);
    }

    /**
     * 生成值类型维度的List
     */
    private List<QueryWaveOverviewGroupRsp.ResultDTO> genLogicNumList(List<String> product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> data,
            String groupBy) {

        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> groupMap = new HashMap<>();
        if (Objects.equals("instanceType", groupBy)) {
            groupMap = ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getInstanceType);
        } else if (Objects.equals("instanceModel", groupBy)) {
            groupMap = ListUtils.groupBy(data, DwdCrpPplItemVersionForWaveAnalysisVO::getInstanceModel);
        }

        List<QueryWaveOverviewGroupRsp.ResultDTO> all = Lang.list();
        for (Map.Entry<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> entry : groupMap.entrySet()) {
            String key = entry.getKey();
            BigDecimal value = product.contains("GPU(裸金属&CVM)")
                    ? NumberUtils.sum(entry.getValue(), DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                    : NumberUtils.sum(entry.getValue(), DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore);
            QueryWaveOverviewGroupRsp.ResultDTO dto = new QueryWaveOverviewGroupRsp.ResultDTO();
            dto.setName(key);
            dto.setLogicNum(value.intValue());
            all.add(dto);
        }
        ListUtils.sortDescNullLast(all, ResultDTO::getLogicNum);
        all = ListUtils.filter(all, o -> o.getLogicNum() > 0);
        return new ArrayList<>(all);
    }


    /**
     * 波动比较方法
     */
    private QueryWaveOverviewRsp compare(String product,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersion,
            List<DwdCrpPplItemVersionForWaveAnalysisVO> curVersion) {
        QueryWaveOverviewRsp rsp = new QueryWaveOverviewRsp();

        //  根节点
        QueryWaveOverviewRsp.ItemData root = new QueryWaveOverviewRsp.ItemData();
        rsp.setRoot(root);
        root.setNodeName("13周预测");
        root.setLastVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                ? NumberUtils.sum(lastVersion, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                : NumberUtils.sum(lastVersion, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
        root.setCurVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                ? NumberUtils.sum(curVersion, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                : NumberUtils.sum(curVersion, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
        root.setDiff(AmountUtils.subtract(root.getCurVersion(), root.getLastVersion()));

        List<QueryWaveOverviewRsp.ItemData> level2List = Lang.list();
        root.setChildren(level2List);

        //  二级节点（部门）
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> lastVersionDept =
                ListUtils.groupBy(lastVersion, o -> industryReportDictService.getCategoryByDept(o.getIndustryDept()));
        Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> curVersionDept =
                ListUtils.groupBy(curVersion, o -> industryReportDictService.getCategoryByDept(o.getIndustryDept()));

        Set<String> allIndDepts = new HashSet<>();
        allIndDepts.addAll(lastVersionDept.keySet());
        allIndDepts.addAll(curVersionDept.keySet());

        for (String each : allIndDepts) {
            QueryWaveOverviewRsp.ItemData item = new QueryWaveOverviewRsp.ItemData();
            level2List.add(item);
            item.setNodeName(each);
            List<DwdCrpPplItemVersionForWaveAnalysisVO> lastVersionList = lastVersionDept.get(each);
            List<DwdCrpPplItemVersionForWaveAnalysisVO> curVersionList = curVersionDept.get(each);
            item.setLastVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                    ? NumberUtils.sum(lastVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                    : NumberUtils.sum(lastVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
            item.setCurVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                    ? NumberUtils.sum(curVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                    : NumberUtils.sum(curVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
            item.setDiff(AmountUtils.subtract(item.getCurVersion(), item.getLastVersion()));

            List<QueryWaveOverviewRsp.ItemData> children = Lang.list();
            item.setChildren(children);

            //  二级节点为其它行业部门时，子节点为行业部门，其它情况下子节点为客户简称
            if (Objects.equals("其它行业部门", each)) {
                Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> lastVersionIndustryDept =
                        ListUtils.groupBy(lastVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getIndustryDept);
                Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> curVersionIndustryDept =
                        ListUtils.groupBy(curVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getIndustryDept);
                Set<String> allIndustryDepts = new HashSet<>();
                allIndustryDepts.addAll(lastVersionIndustryDept.keySet());
                allIndustryDepts.addAll(curVersionIndustryDept.keySet());
                for (String industryDept : allIndustryDepts) {
                    QueryWaveOverviewRsp.ItemData customerItem = new QueryWaveOverviewRsp.ItemData();
                    customerItem.setNodeName(industryDept);
                    customerItem.setLastVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                            ? NumberUtils.sum(lastVersionIndustryDept.get(industryDept),
                            DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                            : NumberUtils.sum(lastVersionIndustryDept.get(industryDept),
                                    DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
                    customerItem.setCurVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                            ? NumberUtils.sum(curVersionIndustryDept.get(industryDept),
                            DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum) :
                            NumberUtils.sum(curVersionIndustryDept.get(industryDept),
                                    DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
                    customerItem.setDiff(
                            AmountUtils.subtract(customerItem.getCurVersion(), customerItem.getLastVersion()));
                    children.add(customerItem);
                }
            } else {
                Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> lastVersionCustomer =
                        ListUtils.groupBy(lastVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getCustomerShortName);
                Map<String, List<DwdCrpPplItemVersionForWaveAnalysisVO>> curVersionCustomer =
                        ListUtils.groupBy(curVersionList, DwdCrpPplItemVersionForWaveAnalysisVO::getCustomerShortName);
                Set<String> allCustomers = new HashSet<>();
                allCustomers.addAll(lastVersionCustomer.keySet());
                allCustomers.addAll(curVersionCustomer.keySet());
                for (String customer : allCustomers) {
                    QueryWaveOverviewRsp.ItemData customerItem = new QueryWaveOverviewRsp.ItemData();
                    customerItem.setNodeName(customer);

                    customerItem.setLastVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                            ? NumberUtils.sum(lastVersionCustomer.get(customer),
                            DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                            : NumberUtils.sum(lastVersionCustomer.get(customer),
                                    DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
                    customerItem.setCurVersion(Objects.equals(product, "GPU(裸金属&CVM)")
                            ? NumberUtils.sum(curVersionCustomer.get(customer),
                            DwdCrpPplItemVersionForWaveAnalysisVO::getTotalGpuNum)
                            : NumberUtils.sum(curVersionCustomer.get(customer),
                                    DwdCrpPplItemVersionForWaveAnalysisVO::getTotalCore));
                    customerItem.setDiff(
                            AmountUtils.subtract(customerItem.getCurVersion(), customerItem.getLastVersion()));

                    children.add(customerItem);
                }
            }

            //  排序逻辑
            SortingUtils.sort(children, getSortingFields());
        }
        //  排序逻辑
        SortingUtils.sort(level2List, getSortingFields());
        return rsp;
    }

    /**
     * 排序规则
     * 先按照差异数逆序排序，若相同则按照本期核心数逆序排
     */
    public static List<SortingField<QueryWaveOverviewRsp.ItemData, ? extends Comparable<?>>> getSortingFields() {
        SortingField<QueryWaveOverviewRsp.ItemData, BigDecimal> diffCore =
                new SortingField<QueryWaveOverviewRsp.ItemData, BigDecimal>(SortingOrderEnum.DESC, false) {
                    @Override
                    public BigDecimal apply(QueryWaveOverviewRsp.ItemData input) {
                        return input.getDiff() == null ? null : input.getDiff();
                    }
                };
        SortingField<QueryWaveOverviewRsp.ItemData, BigDecimal> curVersionCore =
                new SortingField<QueryWaveOverviewRsp.ItemData, BigDecimal>(SortingOrderEnum.DESC, false) {
                    @Override
                    public BigDecimal apply(QueryWaveOverviewRsp.ItemData input) {
                        return input.getDiff() == null ? null : input.getCurVersion();
                    }
                };
        return Lang.list(diffCore, curVersionCore);
    }

    /**
     * 获取上期数据，排除掉最新版本号
     */
    private List<DwdCrpPplItemVersionForWaveAnalysisVO> getLastVersionData(Date start, Date end,
            String newestVersionCode,
            QueryWaveReq req) {
        List<DwdCrpPplItemVersionForWaveAnalysisVO> result = Lang.list();
        Map<String, String> pplVersion = getPplVersion(start, end, newestVersionCode);
        for (Map.Entry<String, String> entry : pplVersion.entrySet()) {
            String yearMonth = entry.getKey();
            String version = entry.getValue();
            List<DwdCrpPplItemVersionForWaveAnalysisVO> list = getAndFilterLast(req, version, yearMonth);
            result.addAll(list);
        }
        return result;
    }

    /**
     * 获取本期数据
     */
    private List<DwdCrpPplItemVersionForWaveAnalysisVO> getNewestVersionData(String newestVersion, QueryWaveReq req) {
        ORMUtils.WhereContent content = req.getWhereContent();
        appendAuthWhere(content, req.getProduct());
        content.addAnd(" version_code = ? ", newestVersion);
        List<DwdCrpPplItemVersionForWaveAnalysisVO> data = DBList.ckcldStdCrpDBHelper
                .getAll(DwdCrpPplItemVersionForWaveAnalysisVO.class, content.getSql(), content.getParams());
        return data;
    }


    /**
     * 获取最新的版本DO对象
     */
    private PplVersionDO getNewestVersion() {
        String raw = "select * from ppl_version where deleted = 0 and status in ('DONE', 'PROCESS') order by id desc limit 1";
        PplVersionDO one = demandDBHelper.getRawOne(PplVersionDO.class, raw);
        return one;
    }


    private List<DwdCrpPplItemVersionForWaveAnalysisVO> getAndFilterLast(QueryWaveReq req, String version,
            String yearMonth) {
        if (StringTools.isBlank(yearMonth)) {
            return Lang.list();
        }
        String[] split = yearMonth.split("-");
        Integer year = NumberUtils.parseInt(split[0]);
        Integer month = NumberUtils.parseInt(split[1]);

        ORMUtils.WhereContent content = req.getWhereContent();
        //  涉及到该VO都要处理软删标记
        appendAuthWhere(content, req.getProduct());
        content.addAnd(" version_code = ? ", version);
        content.addAnd("`year` = ? and `month` = ?", year, month);
        List<DwdCrpPplItemVersionForWaveAnalysisVO> data = DBList.ckcldStdCrpDBHelper
                .getAll(DwdCrpPplItemVersionForWaveAnalysisVO.class, content.getSql(), content.getParams());
        return data;
    }

    /**
     * 获得当前用户的数据权限
     *
     * @return 当管理员时返回null
     */
    private void appendAuthWhere(ORMUtils.WhereContent whereContent, List<String> product) {
        String userName = LoginUtils.getUserName();
        if ("no".equalsIgnoreCase(userName) || "UNKNOWN".equalsIgnoreCase(userName)) {
            return;
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(userName);
        List<IndustryDemandAuthDO> adminAuth =
                ListUtils.filter(auths, o -> IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(o.getRole()));
        if (!adminAuth.isEmpty()) {
            return; // 管理员有所有权限
        }

//        //  查询用户针对某产品的权限
//        List<IndustryDemandAuthDO> authList = Lang.list();
//        //  避免空指针
//        product = ListUtils.isEmpty(product) ? Lang.list("CVM", "CVM&CBS") : product;
//        for (String each : product) {
//            if (StringTools.isNotBlank(each)) {
//                authList.addAll(
//                        ListUtils.filter(auths, o -> (o.getProduct() != null && o.getProduct().contains(each))));
//            } else {
//                // 默认鉴权
//                authList.addAll(ListUtils.filter(auths, o -> o.getProduct() != null
//                        && (o.getProduct().contains("CVM") || o.getProduct().contains("CVM&CBS"))));
//            }
//        }

        List<IndustryDemandAuthDO> authList = Lang.list();

        // 2、是否为行业数据关注人
        List<IndustryDemandAuthDO> industryDataAuth =
                ListUtils.filter(auths,
                        o -> IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode().equals(o.getRole()));

        if (!industryDataAuth.isEmpty()) {
            authList.add(industryDataAuth.get(0));
        }

        // 整合所有角色的行业部门
        Set<String> industryDepts = new HashSet<>();
        for (IndustryDemandAuthDO auth : authList) {
            String industry = auth.getIndustry();
            if (StringTools.isNotBlank(industry)) {
                String[] strs = industry.split(";");
                for (String str : strs) {
                    if (StringTools.isNotBlank(str)) {
                        industryDepts.add(str);
                    }
                }
            }
        }

//        String belongDept = pplDictService.queryIndustryDept(userName).get(0);
//        industryDepts.add(belongDept);

        whereContent.addAnd("industry_dept in (?)", new ArrayList<>(industryDepts));
    }

    /**
     * 获得当前用户的数据权限
     *
     * @return 有权限的行业部门
     */
    private Set<String> authIndustryFilter(Set<String> industryDeptList, List<String> product) {
        String userName = LoginUtils.getUserName();
        if ("no".equalsIgnoreCase(userName) || "UNKNOWN".equalsIgnoreCase(userName)) {
            return new HashSet<>();
        }
        List<IndustryDemandAuthDO> auths = pplCommonService.getAuthRole(userName);
        List<IndustryDemandAuthDO> adminAuth =
                ListUtils.filter(auths, o -> IndustryDemandAuthRoleEnum.ADMIN.getCode().equals(o.getRole()));
        if (!adminAuth.isEmpty()) {
            return industryDeptList; // 管理员有所有权限
        }

        //  查询用户针对某产品的权限
        if (ListUtils.isEmpty(product)) {
            return industryDeptList;
        }

        List<IndustryDemandAuthDO> authList = Lang.list();
        //  避免空指针
        product = ListUtils.isEmpty(product) ? Lang.list("CVM", "CVM&CBS") : product;
        for (String each : product) {
            if (StringTools.isNotBlank(each)) {
                authList.addAll(
                        ListUtils.filter(auths, o -> (o.getProduct() != null && o.getProduct().contains(each))));
            } else {
                // 默认鉴权
                authList.addAll(ListUtils.filter(auths, o -> o.getProduct() != null
                        && (o.getProduct().contains("CVM") || o.getProduct().contains("CVM&CBS"))));
            }
        }

        // 整合所有角色的行业部门
        Set<String> industryDepts = new HashSet<>();
        for (IndustryDemandAuthDO auth : authList) {
            String industry = auth.getIndustry();
            if (StringTools.isNotBlank(industry)) {
                String[] strs = industry.split(";");
                for (String str : strs) {
                    if (StringTools.isNotBlank(str)) {
                        industryDepts.add(str);
                    }
                }
            }
        }
        return industryDepts;
    }

    /**
     * 根据需求年月获取PPL波动分析上期的版本列表
     * version1 2023-1~2023-1
     * version2 2023-2~2023-3
     * version3 2023-3 ~ 2023.4
     * version4 2023-1 2023-4 （本期）
     * 若页面选择2023.1 ~ 2023.4，则对于上期需求
     * 1月需求取v1，2月需求取v2，3、4月需求取v3
     *
     * @return k为年月: 2022-01，v为对应版本
     */
    private Map<String, String> getPplVersion(Date start, Date end, String newestVersionCode) {
        List<Tuple2<Integer, Integer>> yearMonthList = getYearMonthList(start, end);
        Map<String, String> yearMonthVersion = new HashMap<>();
        List<PplVersionVO> all = demandDBHelper.getAll(PplVersionVO.class);
        all = ListUtils.filter(all, o -> {
            //  若当前版本为最新版本，则过滤掉，这里只要非最新版本
            if (Objects.equals(newestVersionCode, o.getVersionCode())) {
                return false;
            }
            //  若当前版本的开始时间比请求参数中的结束时间还晚，则过滤掉
            if (o.getStartYearMonth().compareTo(DateUtils.format(end, yearMonthPattern)) > 0) {
                return false;
            }
            //  若当前版本的结束时间比请求参数中的开始时间还早，则过滤掉
            if (o.getEndYearMonth().compareTo(DateUtils.format(start, yearMonthPattern)) < 0) {
                return false;
            }
            return true;
        });

        for (Tuple2<Integer, Integer> tuple : yearMonthList) {
            String year = String.valueOf(tuple._1);
            String month = tuple._2 >= 10 ? String.valueOf(tuple._2) : "0" + tuple._2;
            String yearMonth = year + "-" + month;
            List<PplVersionVO> filter = ListUtils.filter(all, o ->
                    o.getStartYearMonth().compareTo(yearMonth) <= 0 && o.getEndYearMonth().compareTo(yearMonth) >= 0);
            ListUtils.sortDescNullLast(filter, o -> o.getId());
            if (ListUtils.isNotEmpty(filter)) {
                yearMonthVersion.put(yearMonth, filter.get(0).getVersionCode());
            }
        }
        return yearMonthVersion;
    }

    /**
     * 获取两个年月之间的所有年月
     */
    private List<Tuple2<Integer, Integer>> getYearMonthList(Date start, Date end) {
        List<Tuple2<Integer, Integer>> list = Lang.list();
        while (!start.after(end)) {
            list.add(new Tuple2<>(DateUtils.getYear(start), DateUtils.getMonth(start)));
            start = DateUtils.addTime(start, Calendar.MONTH, 1);
        }
        return list;
    }

    /**
     * 获取两个年月之间的所有年月，转为yyyy-MM的List形式
     */
    private List<String> getYearMonthStrList(Date start, Date end) {
        List<Tuple2<Integer, Integer>> yearMonthList = getYearMonthList(start, end);
        List<String> result = Lang.list();
        for (Tuple2<Integer, Integer> tuple2 : yearMonthList) {
            result.add(toYearMonth(tuple2._1, tuple2._2));
        }
        return result;
    }


    /**
     * 加法运算
     */
    private BigDecimal add(BigDecimal... num) {
        if (num == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal ret = BigDecimal.ZERO;
        for (BigDecimal bigDecimal : num) {
            ret = ret.add(bigDecimal == null ? BigDecimal.ZERO : bigDecimal);
        }
        return ret;
    }

    /**
     * 取反
     */
    public BigDecimal negativeNum(BigDecimal decimal) {
        return decimal == null ? BigDecimal.ZERO : decimal.negate();
    }

    // 获取汇总的版本数据
    private List<PplVersionGroupRecordItemWithOrderSumVO> getSumRecordItem(
            Map<String, List<PplVersionGroupRecordItemWithOrderSumVO>> allMap, boolean calcNetGrowth) {
        List<PplVersionGroupRecordItemWithOrderSumVO> all = Lang.list();
        for (Map.Entry<String, List<PplVersionGroupRecordItemWithOrderSumVO>> entry : allMap.entrySet()) {
            List<PplVersionGroupRecordItemWithOrderSumVO> value = entry.getValue();
            PplVersionGroupRecordItemWithOrderSumVO newValue = new PplVersionGroupRecordItemWithOrderSumVO();
            BeanUtils.copyProperties(value.get(0), newValue);
            newValue.setSumDemandCoreNum(NumberUtils.sum(value, item -> item.calc(calcNetGrowth)).intValue());
            newValue.setSumDemandGpuNum(NumberUtils.sum(value, item -> item.calc(calcNetGrowth)).intValue());
            all.add(newValue);
        }
        return all;
    }

    private List<NewPplVersionGroupRecordItemWithOrderSumVO> newGetSumRecordItem(
            Map<String, List<NewPplVersionGroupRecordItemWithOrderSumVO>> allMap, boolean calcNetGrowth) {
        List<NewPplVersionGroupRecordItemWithOrderSumVO> all = Lang.list();
        for (Map.Entry<String, List<NewPplVersionGroupRecordItemWithOrderSumVO>> entry : allMap.entrySet()) {
            List<NewPplVersionGroupRecordItemWithOrderSumVO> value = entry.getValue();
            NewPplVersionGroupRecordItemWithOrderSumVO newValue = new NewPplVersionGroupRecordItemWithOrderSumVO();
            BeanUtils.copyProperties(value.get(0), newValue);
            newValue.setSumDemandCoreNum(NumberUtils.sum(value, item -> item.calc(calcNetGrowth)).intValue());
            newValue.setSumDemandGpuNum(NumberUtils.sum(value, item -> item.calc(calcNetGrowth)).intValue());
            newValue.setSumSystemCapacity(NumberUtils.sum(value,
                    item -> item.calCBS(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName())));
            newValue.setSumDataCapacity(NumberUtils.sum(value,
                    item -> item.calCBS(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName())));
            all.add(newValue);
        }
        return all;
    }

    // （构建waveDetail返回值）汇总 - 532新版预测量
    private List<Map<String, Object>> getVersion532NewPplItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            ORMUtils.WhereContent where, boolean calcNetGrowth, QueryWaveReq req) {
        List<Map<String, Object>> data = new ArrayList<>();

        WhereContent content = req.yearMonthPartitionCondition();
        content.addAnd(where);

        // 查询532新版预测量
        List<WaveAnalysisPplVersion532NewVO> version532CfDOList = DBList.ckcldStdCrpDBHelper
                .getAll(WaveAnalysisPplVersion532NewVO.class, content.getSql(), content.getParams());

        // Map< year&month, 532需求量>
        Map<String, BigDecimal> version532CfDOMapByIndustry = version532CfDOList.stream()
                .filter(o -> sourceOfIndustry.contains(o.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));
        Map<String, BigDecimal> version532CfDOMapByYun = version532CfDOList.stream()
                .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        // 构建返回值
        Integer industryTotal = 0;
        Integer yunTotal = 0;
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", "532新版预测量");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = version532CfDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = version532CfDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
            industryTotal = industryTotal + (valueByIndustry == null ? 0 : valueByIndustry.intValue());
            yunTotal = yunTotal + (valueByYun == null ? 0 : valueByYun.intValue());
        }

        // 计算合计
        List<WaveValueDTO> valueDTOs = new ArrayList<>();
        WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), yunTotal.toString(), null,
                null);
        WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(), industryTotal.toString(), null,
                null);
        valueDTOs.add(valueDTOByYun);
        valueDTOs.add(valueDTOByIndustry);
        row.put("total",valueDTOs);

        data.add(row);

        return data;
    }

    // （构建waveDetail返回值）汇总 - 基准版
    private List<Map<String, Object>> getVersionBasePplItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            ORMUtils.WhereContent content, boolean calcNetGrowth, QueryWaveReq req) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询基准版预测量
        List<DwsCrpPplItemVersionBaseCfDo> versionBaseCfDOList = DBList.ckcldStdCrpDBHelper
                .getAll(DwsCrpPplItemVersionBaseCfDo.class, content.getSql(), content.getParams());
        if (ListUtils.isNotEmpty(req.getProjectType())) {
            versionBaseCfDOList = versionBaseCfDOList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        // Map< year&month, 基准版>
        Map<String, BigDecimal> versionBaseCfDOMapByIndustry = new HashMap<>();
        Map<String, BigDecimal> versionBaseCfDOMapByYun = new HashMap<>();
        if (req.getProductCategory().equals("CBS")) {
            List<DwsCrpPplItemVersionBaseCfDo> systemList = versionBaseCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            List<DwsCrpPplItemVersionBaseCfDo> dataList = versionBaseCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> systemIndustry = systemList.stream().filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getSystemDiskStorage()).multiply(BigDecimal.valueOf(o.getInstanceNum())), BigDecimal::add));
            Map<String, BigDecimal> systemYun = systemList.stream().filter(o -> sourceOfYun.contains(o.getSource())
                            && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> dataIndustry = dataList.stream().filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getDataDiskNum())
                                    .multiply(BigDecimal.valueOf(o.getDataDiskStorage())).
                                    multiply(BigDecimal.valueOf(o.getInstanceNum())), BigDecimal::add));
            Map<String, BigDecimal> dataYun = dataList.stream().filter(o -> sourceOfYun.contains(o.getSource())
                            && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), BigDecimal::add));
            if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                versionBaseCfDOMapByIndustry = systemIndustry;
                versionBaseCfDOMapByYun = systemYun;
            }else if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                versionBaseCfDOMapByIndustry = dataIndustry;
                versionBaseCfDOMapByYun = dataYun;
            }else {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(systemIndustry.keySet())) {
                    industryKey.addAll(systemIndustry.keySet());
                }
                if (ListUtils.isNotEmpty(dataIndustry.keySet())) {
                    industryKey.addAll(dataIndustry.keySet());
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(systemYun.keySet())) {
                    yunKey.addAll(systemYun.keySet());
                }
                if (ListUtils.isNotEmpty(dataYun.keySet())) {
                    yunKey.addAll(dataYun.keySet());
                }
                for (String key : industryKey) {
                    versionBaseCfDOMapByIndustry.put(key, systemIndustry.getOrDefault(key, BigDecimal.ZERO).add(dataIndustry.getOrDefault(key, BigDecimal.ZERO)));
                }
                for (String key : yunKey) {
                    versionBaseCfDOMapByYun.put(key, systemYun.getOrDefault(key, BigDecimal.ZERO).add(dataYun.getOrDefault(key, BigDecimal.ZERO)));
                }
            }
        }else {
            versionBaseCfDOMapByIndustry = versionBaseCfDOList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct()) ? o.getTotalGpuNum()
                                    : BigDecimal.valueOf(o.getTotalCore()), BigDecimal::add));
            versionBaseCfDOMapByYun = versionBaseCfDOList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calc(calcNetGrowth), BigDecimal::add));
        }

        // 构建返回值
        Integer industryTotal = 0;
        Integer yunTotal = 0;
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", "基准版");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = versionBaseCfDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = versionBaseCfDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
            industryTotal = industryTotal + (valueByIndustry == null ? 0 : valueByIndustry.intValue());
            yunTotal = yunTotal + (valueByYun == null ? 0 : valueByYun.intValue());
        }
        // 计算合计
        List<WaveValueDTO> valueDTOs = new ArrayList<>();
        WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), yunTotal.toString(), null,
                null);
        WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(), industryTotal.toString(), null,
                null);
        valueDTOs.add(valueDTOByYun);
        valueDTOs.add(valueDTOByIndustry);
        row.put("total",valueDTOs);

        data.add(row);

        return data;
    }

    // （构建waveDetail返回值）汇总 - 最新版预测量（每个月最新的运管版本）
    private List<Map<String, Object>> getNewestVersionByMonthSum(List<Tuple2<Integer, Integer>> yearMonthList,
            ORMUtils.WhereContent content, boolean calcNetGrowth, QueryWaveReq req) {
        List<Map<String, Object>> data = new ArrayList<>();

        // 查询最新版预测量
        String pplLatestItemSql = ORMUtils.getSql("/sql/ppl13week/wave_analysis/ppl_item_latest_summary.sql");
        pplLatestItemSql = pplLatestItemSql.replace("${FILTER}", content.getSql());
        List<DwdCrpPplLatestItemForWaveViewVO> versionNewestCfDOList = DBList.ckcldStdCrpDBHelper.getRaw(
                DwdCrpPplLatestItemForWaveViewVO.class, pplLatestItemSql,
                content.getParams());

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            versionNewestCfDOList = versionNewestCfDOList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        // Map< year&month, 最新版预测量>
        Map<String, BigDecimal> versionNewestCfDOMapByIndustry = new HashMap<>();
        Map<String, BigDecimal> versionNewestCfDOMapByYun = new HashMap<>();
        if (req.getProductCategory().equals("CBS")) {
            List<DwdCrpPplLatestItemForWaveViewVO> systemList = versionNewestCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            List<DwdCrpPplLatestItemForWaveViewVO> dataList = versionNewestCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> systemIndustry = systemList.stream().filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> systemYun = systemList.stream().filter(o -> sourceOfYun.contains(o.getSource())
                            && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> dataIndustry = dataList.stream().filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> dataYun = dataList.stream().filter(o -> sourceOfYun.contains(o.getSource())
                            && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), BigDecimal::add));
            if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                versionNewestCfDOMapByIndustry = systemIndustry;
                versionNewestCfDOMapByYun = systemYun;
            }else if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                versionNewestCfDOMapByIndustry = dataIndustry;
                versionNewestCfDOMapByYun = dataYun;
            }else {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(systemIndustry.keySet())) {
                    industryKey.addAll(systemIndustry.keySet());
                }
                if (ListUtils.isNotEmpty(dataIndustry.keySet())) {
                    industryKey.addAll(dataIndustry.keySet());
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(systemYun.keySet())) {
                    yunKey.addAll(systemYun.keySet());
                }
                if (ListUtils.isNotEmpty(dataYun.keySet())) {
                    yunKey.addAll(dataYun.keySet());
                }
                for (String key : industryKey) {
                    versionNewestCfDOMapByIndustry.put(key, systemIndustry.getOrDefault(key, BigDecimal.ZERO).add(dataIndustry.getOrDefault(key, BigDecimal.ZERO)));
                }
                for (String key : yunKey) {
                    versionNewestCfDOMapByYun.put(key, systemYun.getOrDefault(key, BigDecimal.ZERO).add(dataYun.getOrDefault(key, BigDecimal.ZERO)));
                }
            }
        }else {
            versionNewestCfDOMapByIndustry = versionNewestCfDOList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calc(calcNetGrowth), BigDecimal::add));
            versionNewestCfDOMapByYun = versionNewestCfDOList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calc(calcNetGrowth), BigDecimal::add));
        }

        // 构建返回值
        Integer industryTotal = 0;
        Integer yunTotal = 0;
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", "最新版预测量");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = versionNewestCfDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = versionNewestCfDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
            industryTotal = industryTotal + (valueByIndustry == null ? 0 : valueByIndustry.intValue());
            yunTotal = yunTotal + (valueByYun == null ? 0 : valueByYun.intValue());
        }
        // 计算合计
        List<WaveValueDTO> valueDTOs = new ArrayList<>();
        WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), yunTotal.toString(), null,
                null);
        WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(), industryTotal.toString(), null,
                null);
        valueDTOs.add(valueDTOByYun);
        valueDTOs.add(valueDTOByIndustry);
        row.put("total",valueDTOs);

        data.add(row);
        return data;
    }

    // （构建waveDetail返回值）汇总 - 最新预测量、行业报备、系统补充；未转单量
    private List<Map<String, Object>> getNewestPplItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            ORMUtils.WhereContent content, boolean calcNetGrowth) {
        List<Map<String, Object>> data = new ArrayList<>();
        // 查询最新预测量
        String pplItemSql = ORMUtils.getSql("/sql/ppl13week/wave_analysis/ppl_item_summary.sql");
        pplItemSql = pplItemSql.replace("${FILTER}", content.getSql());
        List<DwdCrpPplItemForWaveViewVO> pplItemCfDOList = DBList.ckcldStdCrpDBHelper.getRaw(
                DwdCrpPplItemForWaveViewVO.class, pplItemSql,
                content.getParams());

        List<DwdCrpPplItemForWaveViewVO> pplItemListByIndustry = pplItemCfDOList.stream()
                .filter(o -> sourceOfIndustry.contains(o.getSource())).collect(
                        Collectors.toList());
        List<DwdCrpPplItemForWaveViewVO> pplItemListByYun = pplItemCfDOList.stream()
                .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1).collect(
                        Collectors.toList());

        // Map< year:month, 最新预测量>
        Map<String, BigDecimal> pplItemCfDOMapByIndustry = pplItemListByIndustry.stream()
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));
        Map<String, BigDecimal> pplItemCfDOMapByYun = pplItemListByYun.stream()
                .filter(o -> (PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.FORECAST.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(o.getSource())))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        // Map< year:month, 行业报备>
        Map<String, BigDecimal> pplItemInputDOMapByIndustry = pplItemListByIndustry.stream()
                .filter(o -> PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));
        Map<String, BigDecimal> pplItemInputDOMapByYun = pplItemListByYun.stream()
                .filter(o -> (PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(o.getSource())))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        // Map< year:month, 系统补充>
        Map<String, BigDecimal> systemInputDOMapByIndustry = pplItemListByIndustry.stream()
                .filter(o -> PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(o.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));
        Map<String, BigDecimal> systemInputDOMapByYun = pplItemListByYun.stream()
                .filter(o -> PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(o.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        // Map< year:month, 未转单量>
        Map<String, BigDecimal> validPplItemCfDOMapByIndustry = pplItemListByIndustry.stream()
                .filter(o -> PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource())
                        && PplItemStatusEnum.VALID.getCode().equals(o.getStatus())
                        && Boolean.FALSE.equals(o.getIsExpired()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        Map<String, BigDecimal> validPplItemCfDOMapByYun = pplItemListByYun.stream()
                .filter(o -> PplItemStatusEnum.VALID.getCode().equals(o.getStatus())
                        && Boolean.FALSE.equals(o.getIsExpired())
                        && (PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(o.getSource())))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> o.calc(calcNetGrowth), BigDecimal::add));

        // 最新预测量
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", "最新预测量");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = pplItemCfDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = pplItemCfDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
        }

        data.add(row);

        // 行业报备（最新预测量中行业报备部分）
        Map<String, Object> row1 = new LinkedHashMap<>();
        row1.put("version", "行业报备");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = pplItemInputDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = pplItemInputDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row1.put(dataIndex, valueDTOs);
        }
        data.add(row1);

        // 系统补充（最新预测量中：系统自动补的部分-客户级，行业报备部分-行业级）
        Map<String, Object> row2 = new LinkedHashMap<>();
        row2.put("version", "系统补充");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = systemInputDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = systemInputDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row2.put(dataIndex, valueDTOs);
        }
        data.add(row2);

        // 未转单量
        Map<String, Object> row3 = new LinkedHashMap<>();
        row3.put("version", "未转单量");
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal valueByYun = validPplItemCfDOMapByYun.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    valueByYun == null ? "" : valueByYun.setScale(0, RoundingMode.HALF_UP).toString(), null, null);
            BigDecimal valueByIndustry = validPplItemCfDOMapByIndustry.get(join(yearMonth._1, yearMonth._2));
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    valueByIndustry == null ? "" : valueByIndustry.setScale(0, RoundingMode.HALF_UP).toString(), null,
                    null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row3.put(dataIndex, valueDTOs);
        }

        data.add(row3);

        return data;
    }

    //（构建waveDetail返回值） 汇总 - 预约量、PPL转单、紧急订单
    private List<Map<String, Object>> getNewAppliedPplItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            QueryWaveReq req) {
        List<Map<String, Object>> data = new ArrayList<>();

        //  查询预约信息
        List<DwdCrpPplItemCfDO> pplItemCfDOList = queryAppliedItems(req);
        List<AdsMckForecastSummaryDfDO> adsMckForecastSummaryDfList = queryEmergentOrderItems(req);

        // Map<year:month, PPL转单>
        Map<String, BigDecimal> transFormPplCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.IMPORT.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNum()
                                : BigDecimal.valueOf(o.getTotalCore()), BigDecimal::add));

        // Map<year:month, 紧急订单>
        Map<String, BigDecimal> peCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNum()
                                : BigDecimal.valueOf(o.getTotalCore()), BigDecimal::add));

        Map<String, BigDecimal> emergentOrderMap = adsMckForecastSummaryDfList.stream()
                .collect(Collectors.toMap(o -> join(o.getOrmYear(), o.getOrmMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? BigDecimal.valueOf(o.getTotalGpuNum())
                                : BigDecimal.valueOf(o.getTotalCore()), BigDecimal::add));

        //
        Map<String, BigDecimal> totalOrderMap = new HashMap<>();

        mergeMap(peCfMap, emergentOrderMap);

        // PPL转单 （IMPORT）
        data.add(setApplyItemMap("PPL转单", transFormPplCfMap, yearMonthList));

        // 紧急订单 pe +  order
        data.add(setApplyItemMap("紧急订单", emergentOrderMap, yearMonthList));

        // 订单量
        mergeMap(transFormPplCfMap, totalOrderMap);
        mergeMap(emergentOrderMap, totalOrderMap);
        data.add(setApplyItemMap("订单量", totalOrderMap, yearMonthList));
        return data;

    }

    //（构建waveDetail返回值） 汇总: 最新版PPL拼订单 （1行业PPL、1.1已转单PPL、1.2未转单PPL、  2行业订单、2.1PPL转单、2.2紧急订单）
    private List<Map<String, Object>> getPplJoinOrderItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            QueryWaveReq req,boolean calcNetGrowth) {
        List<Map<String, Object>> data = new ArrayList<>();

        //  查询预约信息
        // 最新版PPL拼订单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> pplJoinOrderVersionItems = queryPplJoinOrderVersionItems(req);

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            pplJoinOrderVersionItems = pplJoinOrderVersionItems.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        // 1-行业PPL
        List<DwsCrpPplJoinOrderVersionNewestCfDO> industryPpl = pplJoinOrderVersionItems.stream()
                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource())))
                .collect(Collectors.toList());

        // 1.1-已转单PPL
        List<DwsCrpPplJoinOrderVersionNewestCfDO> appliedPpl = pplJoinOrderVersionItems.stream()
                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource()))
                && PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus()))
                .collect(Collectors.toList());

        // 1.2-未转单PPL
        List<DwsCrpPplJoinOrderVersionNewestCfDO> notAppliedPpl = pplJoinOrderVersionItems.stream()
                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource()))
                && !PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus()))
                .collect(Collectors.toList());

        // 2-行业订单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> order = pplJoinOrderVersionItems.stream()
                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource()))
                .collect(Collectors.toList());

        // 2.1-PPL转单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> transformOrder = pplJoinOrderVersionItems.stream()
                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource())
                        && OrderSourceEnum.PPL_TRANSFORM.getCode().equals(e.getOrderSource()))
                .collect(Collectors.toList());

        // 2.2-紧急订单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> emergentOrder = pplJoinOrderVersionItems.stream()
                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource())
                        && OrderSourceEnum.URGENT_ORDER.getCode().equals(e.getOrderSource()))
                .collect(Collectors.toList());


        // 最新版PPL拼订单
        data.add(setPplJoinOrderVersionItemMap("最新版PPL拼订单", pplJoinOrderVersionItems, yearMonthList, calcNetGrowth, req));

        // 行业PPL
        data.add(setPplJoinOrderVersionItemMap("行业PPL", industryPpl, yearMonthList, calcNetGrowth, req));

        // 已转单PPL
        data.add(setPplJoinOrderVersionItemMap("已转单PPL", appliedPpl, yearMonthList, calcNetGrowth, req));

        // 未转单PPL
        data.add(setPplJoinOrderVersionItemMap("未转单PPL", notAppliedPpl, yearMonthList, calcNetGrowth, req));

        // 行业订单
        data.add(setPplJoinOrderVersionItemMap("行业订单", order, yearMonthList, calcNetGrowth, req));

        // PPL转单
        data.add(setPplJoinOrderVersionItemMap("PPL转单", transformOrder, yearMonthList, calcNetGrowth, req));

        // 紧急订单
        data.add(setPplJoinOrderVersionItemMap("紧急订单", emergentOrder, yearMonthList, calcNetGrowth, req));
        return data;

    }


    private Map<String, BigDecimal> mergeMap(Map<String, BigDecimal> from, Map<String, BigDecimal> to) {
        to.forEach((k, v) -> {
            if (from.get(k) != null) {
                to.put(k, v.add(from.get(k)));
            }
        });

        from.forEach((k, v) -> {
            to.putIfAbsent(k, v);
        });

        return to;
    }

    //（构建waveDetail返回值） 汇总 - 预约量、有预测预约、无预测预约、中长尾预约、未分类
    private List<Map<String, Object>> getAppliedPplItemSum(List<Tuple2<Integer, Integer>> yearMonthList,
            QueryWaveReq req) {
        List<Map<String, Object>> data = new ArrayList<>();

        //  查询预约信息
        List<DwdCrpPplItemCfDO> pplItemCfDOList = queryAppliedItems(req);

        // Map<year:month, 预约量>
        Map<String, BigDecimal> applyItemCfMap = pplItemCfDOList.stream()
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNumApplyAfter()
                                : BigDecimal.valueOf(o.getTotalCoreApplyAfter()), BigDecimal::add));

        // Map< year:month, 有预测预约>
        Map<String, BigDecimal> applyItemForecastCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.IMPORT.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNumApplyAfter()
                                : BigDecimal.valueOf(o.getTotalCoreApplyAfter()), BigDecimal::add));

        // Map< year:month, 无预测预约>
        Map<String, BigDecimal> applyItemNoForecastCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNumApplyAfter()
                                : BigDecimal.valueOf(o.getTotalCoreApplyAfter()), BigDecimal::add));

        // Map<year:month, 中长尾预约>
        Map<String, BigDecimal> applyItemLongtailCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNumApplyAfter()
                                : BigDecimal.valueOf(o.getTotalCoreApplyAfter()), BigDecimal::add));

        // Map<year:month, 未分类>
        Map<String, BigDecimal> applyItemYunxiaoCfMap = pplItemCfDOList.stream()
                .filter(e -> PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode().equals(e.getSource()))
                .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                        o -> Ppl13weekProductTypeEnum.GPU.getName().equals(o.getProduct())
                                ? o.getTotalGpuNumApplyAfter()
                                : BigDecimal.valueOf(o.getTotalCoreApplyAfter()), BigDecimal::add));

        // 预约量
        data.add(setApplyItemMap("预约量", applyItemCfMap, yearMonthList));

        // 有预测预约（IMPORT）
        data.add(setApplyItemMap("有预测预约", applyItemForecastCfMap, yearMonthList));

        // 无预测预约（APPLY_AUTO_FILL）
        data.add(setApplyItemMap("无预测预约", applyItemNoForecastCfMap, yearMonthList));

        // 中长尾预约（APPLY_AUTO_FILL_LONGTAIL）
        data.add(setApplyItemMap("中长尾预约", applyItemLongtailCfMap, yearMonthList));

        // 未分类（SYNC_YUNXIAO）
        data.add(setApplyItemMap("未分类", applyItemYunxiaoCfMap, yearMonthList));
        return data;
    }

    // 设置预约信息Map
    private Map<String, Object> setApplyItemMap(String name, Map<String, BigDecimal> applyItemCfMap,
            List<Tuple2<Integer, Integer>> yearMonthList) {
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", name);
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal forecastValue = applyItemCfMap.get(join(yearMonth._1, yearMonth._2));

            String value = forecastValue == null ? "" : forecastValue.setScale(0, RoundingMode.HALF_UP).toString();
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    value, null, null);
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    value, null, null);
            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
        }
        return row;
    }

    // 设置PPL订单拼接宽表Map
    private Map<String, Object> setPplJoinOrderVersionItemMap(String name, List<DwsCrpPplJoinOrderVersionNewestCfDO> pplJoinOrderVersionItems,
            List<Tuple2<Integer, Integer>> yearMonthList,boolean calcNetGrowth, QueryWaveReq req) {

        Map<String, BigDecimal> mapByIndustry = new HashMap<>();
        Map<String, BigDecimal> mapByYun = new HashMap<>();
        if (req.getProductCategory().equals("CBS")) {
            List<DwsCrpPplJoinOrderVersionNewestCfDO> industryItems = pplJoinOrderVersionItems.stream()
                    .filter(e -> sourceOfIndustry.contains(e.getSource())).collect(Collectors.toList());
            List<DwsCrpPplJoinOrderVersionNewestCfDO> yunItems = pplJoinOrderVersionItems.stream()
                    .filter(e -> sourceOfYun.contains(e.getSource()) && e.getIsComd() != 1)
                    .collect(Collectors.toList());
            //划分成PPL和共识需求
            List<DwsCrpPplJoinOrderVersionNewestCfDO> industryOrderItems = industryItems.stream()
                    .filter(o -> o.getDataSource().equals("ORDER")).collect(Collectors.toList());
            List<DwsCrpPplJoinOrderVersionNewestCfDO> yunOrderItems = yunItems.stream()
                    .filter(o -> o.getDataSource().equals("ORDER")).collect(Collectors.toList());
            List<DwsCrpPplJoinOrderVersionNewestCfDO> pplItems = pplJoinOrderVersionItems.stream()
                    .filter(o -> !o.getDataSource().equals("ORDER")).collect(Collectors.toList());
            Map<String, List<DiskItem>> industryDiskInfo = getOrderDiskInfo(industryOrderItems, req, calcNetGrowth);
            Map<String, List<DiskItem>> yunDiskInfo = getOrderDiskInfo(yunOrderItems, req, calcNetGrowth);
            Map<String, BigDecimal> industrySystemOrder = industryDiskInfo.get("system").stream().collect(
                    Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getDiskNum()).multiply(BigDecimal.valueOf(o.getDiskStorage())),
                            BigDecimal::add));
            Map<String, BigDecimal> industryDataOrder = industryDiskInfo.get("data").stream().collect(
                    Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getDiskNum()).multiply(BigDecimal.valueOf(o.getDiskStorage())),
                            BigDecimal::add));
            Map<String, BigDecimal> yunSystemOrder = yunDiskInfo.get("system").stream().collect(
                    Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getDiskNum()).multiply(BigDecimal.valueOf(o.getDiskStorage())),
                            BigDecimal::add));
            Map<String, BigDecimal> yunDataOrder = yunDiskInfo.get("data").stream().collect(
                    Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> BigDecimal.valueOf(o.getDiskNum()).multiply(BigDecimal.valueOf(o.getDiskStorage())),
                            BigDecimal::add));

            List<DwsCrpPplJoinOrderVersionNewestCfDO> pplSystemItem = pplItems.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());

            List<DwsCrpPplJoinOrderVersionNewestCfDO> pplDataItem = pplItems.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            Map<String, BigDecimal> pplSystemIndustry = pplSystemItem.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource())).collect(
                            Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> pplSystemYun = pplSystemItem.stream()
                    .filter(e -> sourceOfYun.contains(e.getSource()) && e.getIsComd() != 1).collect(
                            Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> pplDataIndustry = pplDataItem.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource())).collect(
                            Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), BigDecimal::add));
            Map<String, BigDecimal> pplDataYun = pplDataItem.stream()
                    .filter(e -> sourceOfYun.contains(e.getSource()) && e.getIsComd() != 1).collect(
                            Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), BigDecimal::add));
            if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(industrySystemOrder.keySet())) {
                    industryKey.addAll(industrySystemOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplSystemIndustry.keySet())) {
                    industryKey.addAll(pplSystemIndustry.keySet());
                }
                for (String key : industryKey) {
                    mapByIndustry.put(key, industrySystemOrder.getOrDefault(key, BigDecimal.ZERO).add(pplSystemIndustry.getOrDefault(key, BigDecimal.ZERO)));
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(yunSystemOrder.keySet())) {
                    yunKey.addAll(yunSystemOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplSystemYun.keySet())) {
                    yunKey.addAll(pplSystemYun.keySet());
                }
                for (String key : yunKey) {
                    mapByYun.put(key, yunSystemOrder.getOrDefault(key, BigDecimal.ZERO).add(pplSystemYun.getOrDefault(key, BigDecimal.ZERO)));
                }
            }else if (req.getDiskType().size() == 1 && req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(industryDataOrder.keySet())) {
                    industryKey.addAll(industryDataOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplDataIndustry.keySet())) {
                    industryKey.addAll(pplDataIndustry.keySet());
                }
                for (String key : industryKey) {
                    mapByIndustry.put(key, industryDataOrder.getOrDefault(key, BigDecimal.ZERO).add(pplDataIndustry.getOrDefault(key, BigDecimal.ZERO)));
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(yunDataOrder.keySet())) {
                    yunKey.addAll(yunDataOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplDataYun.keySet())) {
                    yunKey.addAll(pplDataYun.keySet());
                }
                for (String key : yunKey) {
                    mapByYun.put(key, yunDataOrder.getOrDefault(key, BigDecimal.ZERO).add(pplDataYun.getOrDefault(key, BigDecimal.ZERO)));
                }
            }else {
                Set<String> industryKey = new HashSet<>();
                if (ListUtils.isNotEmpty(industryDataOrder.keySet())) {
                    industryKey.addAll(industryDataOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplDataIndustry.keySet())) {
                    industryKey.addAll(pplDataIndustry.keySet());
                }
                if (ListUtils.isNotEmpty(industrySystemOrder.keySet())) {
                    industryKey.addAll(industrySystemOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplSystemIndustry.keySet())) {
                    industryKey.addAll(pplSystemIndustry.keySet());
                }
                for (String key : industryKey) {
                    mapByIndustry.put(key, industryDataOrder.getOrDefault(key, BigDecimal.ZERO).add(pplDataIndustry.getOrDefault(key, BigDecimal.ZERO)).add(industrySystemOrder.getOrDefault(key, BigDecimal.ZERO)).add(pplSystemIndustry.getOrDefault(key, BigDecimal.ZERO)));
                }
                Set<String> yunKey = new HashSet<>();
                if (ListUtils.isNotEmpty(industryDataOrder.keySet())) {
                    industryKey.addAll(industryDataOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplDataIndustry.keySet())) {
                    industryKey.addAll(pplDataIndustry.keySet());
                }
                if (ListUtils.isNotEmpty(yunDataOrder.keySet())) {
                    yunKey.addAll(yunDataOrder.keySet());
                }
                if (ListUtils.isNotEmpty(pplDataYun.keySet())) {
                    yunKey.addAll(pplDataYun.keySet());
                }
                for (String key : yunKey) {
                    mapByYun.put(key, yunDataOrder.getOrDefault(key, BigDecimal.ZERO).add(pplDataYun.getOrDefault(key, BigDecimal.ZERO)).add(yunSystemOrder.getOrDefault(key, BigDecimal.ZERO)).add(pplSystemYun.getOrDefault(key, BigDecimal.ZERO)));
                }
            }
        }else {
            mapByIndustry = pplJoinOrderVersionItems.stream()
                    .filter(e -> sourceOfIndustry.contains(e.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calc(calcNetGrowth),BigDecimal::add));

            mapByYun = pplJoinOrderVersionItems.stream()
                    .filter(e -> sourceOfYun.contains(e.getSource()) && e.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getYear(), o.getMonth()),
                            o -> o.calc(calcNetGrowth),BigDecimal::add));
        }
        Map<String, Object> row = new LinkedHashMap<>();
        row.put("version", name);
        Integer industryTotal = 0;
        Integer yunTotal = 0;
        for (Tuple2<Integer, Integer> yearMonth : yearMonthList) {
            List<WaveValueDTO> valueDTOs = new ArrayList<>();
            BigDecimal industryForecastValue = mapByIndustry.get(join(yearMonth._1, yearMonth._2));

            BigDecimal yunForecastValue = mapByYun.get(join(yearMonth._1, yearMonth._2));

            Integer industryValue = industryForecastValue == null ? 0 : industryForecastValue.setScale(0, RoundingMode.HALF_UP).intValue();
            Integer yunValue = yunForecastValue == null ? 0 : yunForecastValue.setScale(0, RoundingMode.HALF_UP).intValue();
            WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(),
                    industryValue.toString(), null, null);
            WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(),
                    yunValue.toString(), null, null);

            valueDTOs.add(valueDTOByYun);
            valueDTOs.add(valueDTOByIndustry);
            String dataIndex = "y" + yearMonth._1 + "m" + yearMonth._2;
            row.put(dataIndex, valueDTOs);
            industryTotal = industryTotal + industryValue;
            yunTotal = yunTotal + yunValue;
        }
        // 计算合计
        List<WaveValueDTO> valueDTOs = new ArrayList<>();
        WaveValueDTO valueDTOByYun = new WaveValueDTO(StatisticalScopeEnum.YUN_PPL.getName(), yunTotal.toString(), null,
                null);
        WaveValueDTO valueDTOByIndustry = new WaveValueDTO(StatisticalScopeEnum.INDUSTRY_PPL.getName(), industryTotal.toString(), null,
                null);
        valueDTOs.add(valueDTOByYun);
        valueDTOs.add(valueDTOByIndustry);
        row.put("total",valueDTOs);
        return row;
    }

    @Data
    public static class DiskItem {

        private Integer year;

        private Integer month;

        private String diskType;

        private Integer instanceNum;

        private Integer diskStorage;

        private Integer diskSingleNum;

        private Integer diskNum;
    }

    public Map<String,List<DiskItem>> getOrderDiskInfo(List<DwsCrpPplJoinOrderVersionNewestCfDO> orderItems,
            QueryWaveReq req, boolean calcNetGrowth) {
        List<DiskItem> orderSystemList = new ArrayList<>();
        List<DiskItem> orderDataList = new ArrayList<>();
        for (DwsCrpPplJoinOrderVersionNewestCfDO orderItem : orderItems) {
            String systemDisk = orderItem.getSystemDisk();
            JSONArray systemArray = JSON.parseArray(systemDisk);
            if (!systemArray.isEmpty()) {
                for (int i = 0 ; i < systemArray.size(); i++) {
                    JSONObject jsonObject = systemArray.getJSONObject(i);
                    DiskItem diskItem = new DiskItem();
                    diskItem.setYear(orderItem.getYear());
                    diskItem.setMonth(orderItem.getMonth());
                    diskItem.setDiskType(jsonObject.getString("diskType"));
                    diskItem.setDiskNum(jsonObject.getInteger("diskNum"));
                    diskItem.setDiskStorage(jsonObject.getInteger("diskStorage"));
                    diskItem.setDiskSingleNum(jsonObject.getInteger("diskSingleNum"));
                    diskItem.setInstanceNum(jsonObject.getInteger("instanceNum"));
                    String demandType = orderItem.getDemandType();
                    boolean isReturn = PplDemandTypeEnum.RETURN.getCode().equals(demandType);
                    if (isReturn && calcNetGrowth) {
                        diskItem.setDiskStorage(-jsonObject.getInteger("diskStorage"));
                    }
                    diskItem.setDiskType(PplDiskTypeEnum.getByCode(diskItem.getDiskType()).getName());
                    boolean flag = false;
                    for (String volume : req.getVolumeType()) {
                        if (diskItem.getDiskType().contains(volume)) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        orderSystemList.add(diskItem);
                    }
                }
            }

            String dataDisk = orderItem.getDataDisk();
            JSONArray dataArray = JSON.parseArray(dataDisk);
            if (!dataArray.isEmpty()) {
                for (int i = 0; i < dataArray.size(); i++) {
                    JSONObject jsonObject = dataArray.getJSONObject(i);
                    DiskItem diskItem = new DiskItem();
                    diskItem.setYear(orderItem.getYear());
                    diskItem.setMonth(orderItem.getMonth());
                    diskItem.setDiskType(jsonObject.getString("diskType"));
                    diskItem.setDiskNum(jsonObject.getInteger("diskNum"));
                    diskItem.setDiskStorage(jsonObject.getInteger("diskStorage"));
                    diskItem.setDiskSingleNum(jsonObject.getInteger("diskSingleNum"));
                    diskItem.setInstanceNum(jsonObject.getInteger("instanceNum"));
                    String demandType = orderItem.getDemandType();
                    boolean isReturn = PplDemandTypeEnum.RETURN.getCode().equals(demandType);
                    if (isReturn && calcNetGrowth) {
                        diskItem.setDiskStorage(-jsonObject.getInteger("diskStorage"));
                    }
                    diskItem.setDiskType(PplDiskTypeEnum.getByCode(diskItem.getDiskType()).getName());
                    boolean flag = false;
                    for (String volume : req.getVolumeType()) {
                        if (diskItem.getDiskType().contains(volume)) {
                            flag = true;
                            break;
                        }
                    }
                    if (flag) {
                        orderDataList.add(diskItem);
                    }
                }
            }
        }
        Map<String,List<DiskItem>> result = new HashMap<>();
        result.put("system", orderSystemList);
        result.put("data", orderDataList);
        return result;
    }

    // （构建excel）明细 - 明细版本数据
    private List<WaveDetailExportExcelDTO> buildVersionItemExcelData(
            List<PplVersionGroupRecordItemWithOrderSumVO> allList,
            Map<String, String> instanceConfigMap,
            Map<String, PplVersionDO> pplVersionConfigMap,
            List<String> statisticalScopes, boolean calcNetGrowth, QueryWaveReq req) {
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        if (!req.getProductCategory().equals("CBS")) {
            // 明细版本数据: Map < 版本号:pplId, Integer >
            Map<String, BigDecimal> versionRecordMapByIndustry = allList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                            o -> o.calc(calcNetGrowth), (v1, v2) -> v2));
            Map<String, BigDecimal> versionRecordMapByYun = allList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                            o -> o.calc(calcNetGrowth), (v1, v2) -> v2));

            // 设置 版本明细excel信息
            for (PplVersionGroupRecordItemWithOrderSumVO pplItemVersionCfDO : allList) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemVersionCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, pplItemVersionCfDO.getBeginBuyDate(),
                        pplItemVersionCfDO.getBeginBuyDate());

                for (String statisticalScope : statisticalScopes) {
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                    BigDecimal targetVersionValue =
                            StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope) ? versionRecordMapByYun.get(
                                    join(excelDTO.getVersionCode(), excelDTO.getPplId()))
                                    : versionRecordMapByIndustry.get(join(excelDTO.getVersionCode(), excelDTO.getPplId()));
                    if (targetVersionValue != null) {
                        WaveDetailExportExcelDTO excelVersionDTO = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excelVersionDTO);
                        excelVersionDTO.setProductCategory(req.getProductCategory());
                        excelVersionDTO.setFirstLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getFirstLevelTargetName());
                        excelVersionDTO.setSecondLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getSecondLevelTargetName());
                        excelVersionDTO.setTargetName(WaveTargetNameEnum.VERSION_DATA.getTargetName());
                        excelVersionDTO.setTargetValue(targetVersionValue.intValue());
                        data.add(excelVersionDTO);
                    }
                }

            }
        }else {
            List<PplVersionGroupRecordItemWithOrderSumVO> systemList = allList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            List<PplVersionGroupRecordItemWithOrderSumVO> dataList = allList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());

            Map<String, BigDecimal> industrySystem = systemList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2));
            Map<String, BigDecimal> yunSystem = systemList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2));

            Map<String, BigDecimal> industryData = dataList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2));
            Map<String, BigDecimal> yunData =dataList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.toMap(o -> join(o.getVersionCode(), o.getPplId()),
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2));
            if (req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                // 设置 版本明细excel信息
                for (PplVersionGroupRecordItemWithOrderSumVO pplItemVersionCfDO : systemList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemVersionCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, pplItemVersionCfDO.getBeginBuyDate(),
                            pplItemVersionCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        BigDecimal targetVersionValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope) ? yunSystem.get(
                                        join(excelDTO.getVersionCode(), excelDTO.getPplId()))
                                        : industrySystem.get(join(excelDTO.getVersionCode(), excelDTO.getPplId()));
                        if (targetVersionValue != null) {
                            WaveDetailExportExcelDTO excelVersionDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelVersionDTO);
                            excelVersionDTO.setProductCategory(req.getProductCategory());
                            excelVersionDTO.setVolumeType(pplItemVersionCfDO.getSystemDiskType());
                            excelVersionDTO.setDiskType(PplWaveDiskTypeEnum.SYSTEM_DISK.getName());
                            excelVersionDTO.setFirstLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getFirstLevelTargetName());
                            excelVersionDTO.setSecondLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getSecondLevelTargetName());
                            excelVersionDTO.setTargetName(WaveTargetNameEnum.VERSION_DATA.getTargetName());
                            excelVersionDTO.setTargetValue(targetVersionValue.intValue());
                            excelVersionDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(pplItemVersionCfDO.getCbsIsSpike()));
                            data.add(excelVersionDTO);
                        }
                    }

                }
            }
            if (req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                for (PplVersionGroupRecordItemWithOrderSumVO pplItemVersionCfDO : dataList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemVersionCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, pplItemVersionCfDO.getBeginBuyDate(),
                            pplItemVersionCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        BigDecimal targetVersionValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope) ? yunData.get(
                                        join(excelDTO.getVersionCode(), excelDTO.getPplId()))
                                        : industryData.get(join(excelDTO.getVersionCode(), excelDTO.getPplId()));
                        if (targetVersionValue != null) {
                            WaveDetailExportExcelDTO excelVersionDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelVersionDTO);
                            excelVersionDTO.setProductCategory(req.getProductCategory());
                            excelVersionDTO.setVolumeType(pplItemVersionCfDO.getDataDiskType());
                            excelVersionDTO.setDiskType(PplWaveDiskTypeEnum.DATA_DISK.getName());
                            excelVersionDTO.setFirstLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getFirstLevelTargetName());
                            excelVersionDTO.setSecondLevelTargetName(WaveTargetNameEnum.VERSION_DATA.getSecondLevelTargetName());
                            excelVersionDTO.setTargetName(WaveTargetNameEnum.VERSION_DATA.getTargetName());
                            excelVersionDTO.setTargetValue(targetVersionValue.intValue());
                            excelVersionDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(pplItemVersionCfDO.getCbsIsSpike()));
                            data.add(excelVersionDTO);
                        }
                    }

                }
            }


        }

        return data;
    }

    // （构建excel）明细 - 532新版预测量
    private List<WaveDetailExportExcelDTO> build532NewVersionItemExcelData(ORMUtils.WhereContent content,
            Map<String, String> instanceConfigMap, Map<String, PplVersionDO> pplVersionConfigMap,
            List<String> statisticalScopes, boolean calcNetGrowth, QueryWaveReq req) {
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        // 查询532版本明细数据
        List<DwsCrpPplItemVersion532NewMifDO> version532CfDOList = DBList.ckcldStdCrpDBHelper
                .getAll(DwsCrpPplItemVersion532NewMifDO.class, content.getSql(), content.getParams());

        // 532预测数据: Map < 版本号, Map < pplId, BigDecimal > >
        Map<String, Map<String, BigDecimal>> version532MapByIndustry = version532CfDOList.stream()
                .filter(o -> sourceOfIndustry.contains(o.getSource()))
                .collect(Collectors.groupingBy(DwsCrpPplItemVersion532NewMifDO::getVersionCode,
                        Collectors.toMap(DwsCrpPplItemVersion532NewMifDO::getPplId,
                                o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));
        Map<String, Map<String, BigDecimal>> version532MapByYun = version532CfDOList.stream()
                .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                .collect(Collectors.groupingBy(DwsCrpPplItemVersion532NewMifDO::getVersionCode,
                        Collectors.toMap(DwsCrpPplItemVersion532NewMifDO::getPplId,
                                o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));

        // 设置 532预测excel信息
        for (DwsCrpPplItemVersion532NewMifDO version532ItemCfDO : version532CfDOList) {
            WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(version532ItemCfDO);
            excelDTO.setCommonInstanceType(
                    instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

            setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, version532ItemCfDO.getBeginBuyDate(),
                    version532ItemCfDO.getBeginBuyDate());

            for (String statisticalScope : statisticalScopes) {
                excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                Map<String, BigDecimal> version532ItemMapByYun = version532MapByYun.getOrDefault(
                        version532ItemCfDO.getVersionCode(), new HashMap<>());
                Map<String, BigDecimal> version532ItemMapByIndustry = version532MapByIndustry.getOrDefault(
                        version532ItemCfDO.getVersionCode(), new HashMap<>());
                BigDecimal target532Value =
                        StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                ? version532ItemMapByYun.get(
                                excelDTO.getPplId()) : version532ItemMapByIndustry.get(excelDTO.getPplId());
                if (target532Value != null) {
                    WaveDetailExportExcelDTO excel532DTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excel532DTO);
                    excel532DTO.setProductCategory(req.getProductCategory());
                    excel532DTO.setFirstLevelTargetName(WaveTargetNameEnum.VERSION_532_DATA.getFirstLevelTargetName());
                    excel532DTO.setSecondLevelTargetName(WaveTargetNameEnum.VERSION_532_DATA.getSecondLevelTargetName());
                    excel532DTO.setTargetName(WaveTargetNameEnum.VERSION_532_DATA.getTargetName());
                    excel532DTO.setTargetValue(target532Value.setScale(0, RoundingMode.HALF_UP).intValue());
                    data.add(excel532DTO);
                }
            }
        }
        return data;
    }

    // （构建excel）明细 - 基准版
    private List<WaveDetailExportExcelDTO> buildVersionBaseItemExcelData(
            ORMUtils.WhereContent content,
            Map<String, String> instanceConfigMap, Map<String, PplVersionDO> pplVersionConfigMap,
            List<String> statisticalScopes, boolean calcNetGrowth, QueryWaveReq req) {
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        // 查询基准版明细数据
        List<DwsCrpPplItemVersionBaseCfDo> versionBaseCfDOList = DBList.ckcldStdCrpDBHelper
                .getAll(DwsCrpPplItemVersionBaseCfDo.class, content.getSql(), content.getParams());

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            versionBaseCfDOList = versionBaseCfDOList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        if (!req.getProductCategory().equals("CBS")) {
            // 基准版数据: Map < 版本号, Map < pplId, BigDecimal > >
            Map<String, Map<String, BigDecimal>> versionBaseMapByIndustry = versionBaseCfDOList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> versionBaseMapByYun = versionBaseCfDOList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));

            // 设置 基准版预测excel信息
            for (DwsCrpPplItemVersionBaseCfDo versionBaseItemCfDO : versionBaseCfDOList) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(versionBaseItemCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, versionBaseItemCfDO.getBeginBuyDate(),
                        versionBaseItemCfDO.getBeginBuyDate());

                for (String statisticalScope : statisticalScopes) {
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                    Map<String, BigDecimal> versionBaseItemMapByYun = versionBaseMapByYun.getOrDefault(
                            versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                    Map<String, BigDecimal> versionBaseItemMapByIndustry = versionBaseMapByIndustry.getOrDefault(
                            versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                    BigDecimal targetValue =
                            StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                    ? versionBaseItemMapByYun.get(
                                    excelDTO.getPplId()) : versionBaseItemMapByIndustry.get(excelDTO.getPplId());
                    if (targetValue != null) {
                        WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                        excelBaseDTO.setProductCategory(req.getProductCategory());
                        excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.BASE_DATA.getFirstLevelTargetName());
                        excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.BASE_DATA.getSecondLevelTargetName());
                        excelBaseDTO.setTargetName(WaveTargetNameEnum.BASE_DATA.getTargetName());
                        excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                        data.add(excelBaseDTO);
                    }
                }
            }
        }else {
            List<DwsCrpPplItemVersionBaseCfDo> systemList = versionBaseCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            List<DwsCrpPplItemVersionBaseCfDo> dataList = versionBaseCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());

            // 基准版数据: Map < 版本号, Map < pplId, BigDecimal > >
            Map<String, Map<String, BigDecimal>> versionBaseMapByIndustrySystem = systemList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> versionBaseMapByYunSystem = systemList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2)));

            Map<String, Map<String, BigDecimal>> versionBaseMapByIndustryData = dataList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> versionBaseMapByYunData =dataList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionBaseCfDo::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionBaseCfDo::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2)));

            if (req.getDiskType().contains("系统盘")) {
                // 设置 基准版预测excel信息
                for (DwsCrpPplItemVersionBaseCfDo versionBaseItemCfDO : systemList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(versionBaseItemCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, versionBaseItemCfDO.getBeginBuyDate(),
                            versionBaseItemCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        Map<String, BigDecimal> versionBaseItemMapByYun = versionBaseMapByYunSystem.getOrDefault(
                                versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                        Map<String, BigDecimal> versionBaseItemMapByIndustry = versionBaseMapByIndustrySystem.getOrDefault(
                                versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                        BigDecimal targetValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                        ? versionBaseItemMapByYun.get(
                                        excelDTO.getPplId()) : versionBaseItemMapByIndustry.get(excelDTO.getPplId());
                        if (targetValue != null) {
                            WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                            excelBaseDTO.setProductCategory(req.getProductCategory());
                            excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.BASE_DATA.getFirstLevelTargetName());
                            excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.BASE_DATA.getSecondLevelTargetName());
                            excelBaseDTO.setTargetName(WaveTargetNameEnum.BASE_DATA.getTargetName());
                            excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                            excelBaseDTO.setVolumeType(versionBaseItemCfDO.getSystemDiskType());
                            excelBaseDTO.setDiskType("系统盘");
                            excelBaseDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(versionBaseItemCfDO.getCbsIsSpike()));
                            data.add(excelBaseDTO);
                        }
                    }
                }
            }

            if (req.getDiskType().contains("数据盘")) {
                // 设置 基准版预测excel信息
                for (DwsCrpPplItemVersionBaseCfDo versionBaseItemCfDO : dataList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(versionBaseItemCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, versionBaseItemCfDO.getBeginBuyDate(),
                            versionBaseItemCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        Map<String, BigDecimal> versionBaseItemMapByYun = versionBaseMapByYunData.getOrDefault(
                                versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                        Map<String, BigDecimal> versionBaseItemMapByIndustry = versionBaseMapByIndustryData.getOrDefault(
                                versionBaseItemCfDO.getVersionCode(), new HashMap<>());
                        BigDecimal targetValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                        ? versionBaseItemMapByYun.get(
                                        excelDTO.getPplId()) : versionBaseItemMapByIndustry.get(excelDTO.getPplId());
                        if (targetValue != null) {
                            WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                            excelBaseDTO.setProductCategory(req.getProductCategory());
                            excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.BASE_DATA.getFirstLevelTargetName());
                            excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.BASE_DATA.getSecondLevelTargetName());
                            excelBaseDTO.setTargetName(WaveTargetNameEnum.BASE_DATA.getTargetName());
                            excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                            excelBaseDTO.setVolumeType(versionBaseItemCfDO.getDataDiskType());
                            excelBaseDTO.setDiskType("数据盘");
                            excelBaseDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(versionBaseItemCfDO.getCbsIsSpike()));
                            data.add(excelBaseDTO);
                        }
                    }
                }
            }

        }
        return data;
    }

    // （构建excel）明细 - 最新版预测量（每个月最新的运管版本）
    private List<WaveDetailExportExcelDTO> buildNewestVersionItemExcelData(ORMUtils.WhereContent content,
            Map<String, String> instanceConfigMap, Map<String, PplVersionDO> pplVersionConfigMap,
            List<String> statisticalScopes, boolean calcNetGrowth, QueryWaveReq req) {
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        // 查询最新版明细数据
        List<DwsCrpPplItemVersionNewestCfDO> newestVersionCfDOList = DBList.ckcldStdCrpDBHelper
                .getAll(DwsCrpPplItemVersionNewestCfDO.class, content.getSql(), content.getParams());

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            newestVersionCfDOList = newestVersionCfDOList.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        if (!req.getProductCategory().equals("CBS")) {
            // 最新版数据: Map < 版本号, Map < pplId, BigDecimal > >
            Map<String, Map<String, BigDecimal>> newestVersionMapByIndustry = newestVersionCfDOList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> newestVersionMapByYun = newestVersionCfDOList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calc(calcNetGrowth), (v1, v2) -> v2)));

            // 设置最新版预测excel信息
            for (DwsCrpPplItemVersionNewestCfDO newestVersionItemCfDO : newestVersionCfDOList) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(newestVersionItemCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, newestVersionItemCfDO.getBeginBuyDate(),
                        newestVersionItemCfDO.getBeginBuyDate());

                for (String statisticalScope : statisticalScopes) {
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                    Map<String, BigDecimal> newestVersionItemMapByYun = newestVersionMapByYun.getOrDefault(
                            newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                    Map<String, BigDecimal> newestVersionItemMapByIndustry = newestVersionMapByIndustry.getOrDefault(
                            newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                    BigDecimal targetValue =
                            StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                    ? newestVersionItemMapByYun.get(
                                    excelDTO.getPplId()) : newestVersionItemMapByIndustry.get(excelDTO.getPplId());
                    if (targetValue != null) {
                        WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                        excelBaseDTO.setProductCategory(req.getProductCategory());
                        excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getFirstLevelTargetName());
                        excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getSecondLevelTargetName());
                        excelBaseDTO.setTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getTargetName());
                        excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                        data.add(excelBaseDTO);
                    }
                }
            }
        }else {
            List<DwsCrpPplItemVersionNewestCfDO> systemList = newestVersionCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getSystemDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());
            List<DwsCrpPplItemVersionNewestCfDO> dataList = newestVersionCfDOList.stream().filter(o -> {
                for (String volume : req.getVolumeType()) {
                    if (o.getDataDiskType().contains(volume)) {
                        return true;
                    }
                }
                return false;
            }).collect(Collectors.toList());

            // 基准版数据: Map < 版本号, Map < pplId, BigDecimal > >
            Map<String, Map<String, BigDecimal>> newestVersionMapByIndustrySystem = systemList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> newestVersionMapByYunSystem = systemList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.SYSTEM_DISK.getName()), (v1, v2) -> v2)));

            Map<String, Map<String, BigDecimal>> newestVersionMapByIndustryData = dataList.stream()
                    .filter(o -> sourceOfIndustry.contains(o.getSource()))
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2)));
            Map<String, Map<String, BigDecimal>> newestVersionMapByYunData =dataList.stream()
                    .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1)
                    .collect(Collectors.groupingBy(DwsCrpPplItemVersionNewestCfDO::getVersionCode,
                            Collectors.toMap(DwsCrpPplItemVersionNewestCfDO::getPplId,
                                    o -> o.calcDiskCapacity(calcNetGrowth, PplWaveDiskTypeEnum.DATA_DISK.getName()), (v1, v2) -> v2)));

            if (req.getDiskType().contains(PplWaveDiskTypeEnum.SYSTEM_DISK.getName())) {
                for (DwsCrpPplItemVersionNewestCfDO newestVersionItemCfDO : newestVersionCfDOList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(newestVersionItemCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, newestVersionItemCfDO.getBeginBuyDate(),
                            newestVersionItemCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        Map<String, BigDecimal> newestVersionItemMapByYun = newestVersionMapByYunSystem.getOrDefault(
                                newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                        Map<String, BigDecimal> newestVersionItemMapByIndustry = newestVersionMapByIndustrySystem.getOrDefault(
                                newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                        BigDecimal targetValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                        ? newestVersionItemMapByYun.get(
                                        excelDTO.getPplId()) : newestVersionItemMapByIndustry.get(excelDTO.getPplId());
                        if (targetValue != null) {
                            WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                            excelBaseDTO.setProductCategory(req.getProductCategory());
                            excelBaseDTO.setVolumeType(newestVersionItemCfDO.getSystemDiskType());
                            excelBaseDTO.setDiskType(PplWaveDiskTypeEnum.SYSTEM_DISK.getName());
                            excelBaseDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(newestVersionItemCfDO.getCbsIsSpike()));
                            excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getFirstLevelTargetName());
                            excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getSecondLevelTargetName());
                            excelBaseDTO.setTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getTargetName());
                            excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                            data.add(excelBaseDTO);
                        }
                    }
                }
            }

            if (req.getDiskType().contains(PplWaveDiskTypeEnum.DATA_DISK.getName())) {
                for (DwsCrpPplItemVersionNewestCfDO newestVersionItemCfDO : newestVersionCfDOList) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(newestVersionItemCfDO);
                    excelDTO.setCommonInstanceType(
                            instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), "-"));

                    setIsInnerVersionFlag(pplVersionConfigMap, excelDTO, newestVersionItemCfDO.getBeginBuyDate(),
                            newestVersionItemCfDO.getBeginBuyDate());

                    for (String statisticalScope : statisticalScopes) {
                        excelDTO.setStatisticalScope(StatisticalScopeEnum.getNameByCode(statisticalScope));

                        Map<String, BigDecimal> newestVersionItemMapByYun = newestVersionMapByYunData.getOrDefault(
                                newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                        Map<String, BigDecimal> newestVersionItemMapByIndustry = newestVersionMapByIndustryData.getOrDefault(
                                newestVersionItemCfDO.getVersionCode(), new HashMap<>());
                        BigDecimal targetValue =
                                StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                        ? newestVersionItemMapByYun.get(
                                        excelDTO.getPplId()) : newestVersionItemMapByIndustry.get(excelDTO.getPplId());
                        if (targetValue != null) {
                            WaveDetailExportExcelDTO excelBaseDTO = new WaveDetailExportExcelDTO();
                            BeanUtils.copyProperties(excelDTO, excelBaseDTO);
                            excelBaseDTO.setProductCategory(req.getProductCategory());
                            excelBaseDTO.setVolumeType(newestVersionItemCfDO.getDataDiskType());
                            excelBaseDTO.setDiskType(PplWaveDiskTypeEnum.DATA_DISK.getName());
                            excelBaseDTO.setCbsProjectType(PplWaveProjectTypeEnum.getNameByCode(newestVersionItemCfDO.getCbsIsSpike()));
                            excelBaseDTO.setFirstLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getFirstLevelTargetName());
                            excelBaseDTO.setSecondLevelTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getSecondLevelTargetName());
                            excelBaseDTO.setTargetName(WaveTargetNameEnum.LATEST_VERSION_DATA.getTargetName());
                            excelBaseDTO.setTargetValue(targetValue.setScale(0, RoundingMode.HALF_UP).intValue());
                            data.add(excelBaseDTO);
                        }
                    }
                }
            }
        }
        return data;
    }


    // （构建excel）明细 - 最新预测量、行业报备、系统补充；未转单量
    private List<WaveDetailExportExcelDTO> buildNewestItemExcelData(ORMUtils.WhereContent content,
            Map<String, String> instanceConfigMap, List<String> statisticalScopes, boolean calcNetGrowth) {
        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        // 查询最新预测量
        List<DwdCrpPplItemCfDO> pplItemCfDOList = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                content.getSql(), content.getParams());

        List<DwdCrpPplItemCfDO> pplItemListByIndustry = pplItemCfDOList.stream()
                .filter(o -> sourceOfIndustry.contains(o.getSource())).collect(
                        Collectors.toList());
        List<DwdCrpPplItemCfDO> pplItemListByYun = pplItemCfDOList.stream()
                .filter(o -> sourceOfYun.contains(o.getSource()) && o.getIsComd() != 1).collect(
                        Collectors.toList());

        // 最新预测量: Map < pplId, Integer >
        Map<String, BigDecimal> pplItemCfDOMapByIndustry = pplItemListByIndustry.stream()
                .collect(
                        Collectors.toMap(DwdCrpPplItemCfDO::getPplId,
                                o -> o.calc(calcNetGrowth), (v1, v2) -> v2));
        Map<String, BigDecimal> pplItemCfDOMapByYun = pplItemListByYun.stream()
                .filter(o -> (PplOrderSourceTypeEnum.IMPORT.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.FORECAST.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(o.getSource())
                        || PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(o.getSource())))
                .collect(
                        Collectors.toMap(DwdCrpPplItemCfDO::getPplId,
                                o -> o.calc(calcNetGrowth), (v1, v2) -> v2));

        // 设置 预测excel信息（最新预测量、行业报备、系统补充；有预测预约、无预测预约；未转单量）
        for (String statisticalScope : statisticalScopes) {
            List<DwdCrpPplItemCfDO> excelPplItemDOList =
                    StatisticalScopeEnum.INDUSTRY_PPL.getCode().equals(statisticalScope) ? pplItemListByIndustry
                            : pplItemListByYun;
            String statisticalName = StatisticalScopeEnum.getNameByCode(statisticalScope);

            for (DwdCrpPplItemCfDO pplItemCfDO : excelPplItemDOList) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), excelDTO.getInstanceType()));
                excelDTO.setStatisticalScope(statisticalName);

                // 1、最新预测量
                BigDecimal newValue =
                        StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                ? pplItemCfDOMapByYun.get(
                                excelDTO.getPplId()) : pplItemCfDOMapByIndustry.get(excelDTO.getPplId());
                if (newValue != null) {
                    WaveDetailExportExcelDTO excelNewestDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelNewestDTO);
                    excelNewestDTO.setTargetName("最新预测量");
                    excelNewestDTO.setTargetValue(newValue.setScale(0, RoundingMode.HALF_UP).intValue());
                    data.add(excelNewestDTO);

                    // 1.1、行业报备量
                    if (PplOrderSourceTypeEnum.IMPORT.getCode().equals(pplItemCfDO.getSource())
                            || (StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                            && PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(pplItemCfDO.getSource()))) {
                        WaveDetailExportExcelDTO excelInputDTO = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excelInputDTO);
                        excelInputDTO.setTargetName("行业报备量");
                        excelInputDTO.setTargetValue(newValue.setScale(0, RoundingMode.HALF_UP).intValue());
                        data.add(excelInputDTO);
                    }

                    // 1.2、系统补充
                    if (PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(pplItemCfDO.getSource())) {
                        WaveDetailExportExcelDTO excelSystemInputDTO = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excelSystemInputDTO);
                        excelSystemInputDTO.setTargetName("系统补充");
                        excelSystemInputDTO.setTargetValue(newValue.setScale(0, RoundingMode.HALF_UP).intValue());
                        data.add(excelSystemInputDTO);
                    }
                }

                // 2、未转单量
                // 为过期的数据
                if (!pplItemCfDO.getIsExpired() &&
                        PplItemStatusEnum.VALID.getCode().equals(pplItemCfDO.getStatus()) &&
                        (PplOrderSourceTypeEnum.IMPORT.getCode().equals(pplItemCfDO.getSource())
                                || (StatisticalScopeEnum.YUN_PPL.getCode().equals(statisticalScope)
                                && PplOrderSourceTypeEnum.COMD_INTERVENE.getCode().equals(pplItemCfDO.getSource())))
                ) {
                    WaveDetailExportExcelDTO excelNoAppliedDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelNoAppliedDTO);
                    excelDTO.setTargetName("未转单量");
                    excelDTO.setTargetValue(pplItemCfDO.calc(calcNetGrowth).intValue());
                    data.add(excelDTO);
                }

            }
        }
        return data;
    }

    // （构建excel）明细 - 有预测预约、无预测预约、中长尾预约、未分类
    private List<WaveDetailExportExcelDTO> buildAppliedItemExcelData(QueryWaveReq req,
            Map<String, String> instanceConfigMap) {
        //  查询预约信息
        List<DwdCrpPplItemCfDO> pplItemCfDOList = queryAppliedItems(req);

        List<WaveDetailExportExcelDTO> data = new ArrayList<>();
        // 设置 预测excel信息（有预测预约、无预测预约）
        for (String statisticalScope : req.getStatisticalScope()) {
            String statisticalName = StatisticalScopeEnum.getNameByCode(statisticalScope);

            for (DwdCrpPplItemCfDO pplItemCfDO : pplItemCfDOList) {
                boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(pplItemCfDO.getProduct());

                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), excelDTO.getInstanceType()));
                excelDTO.setStatisticalScope(statisticalName);

                // 1、有预测预约
                if (PplOrderSourceTypeEnum.IMPORT.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelAppliedDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelAppliedDTO);
                    excelAppliedDTO.setTargetName("有预测预约");
                    excelAppliedDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNumApplyAfter().intValue()
                                    : pplItemCfDO.getTotalCoreApplyAfter().intValue());
                    data.add(excelAppliedDTO);
                    continue;
                }

                // 2、无预测预约
                if (PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelSystemDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelSystemDTO);
                    excelSystemDTO.setTargetName("无预测预约");
                    excelSystemDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNumApplyAfter().intValue()
                                    : pplItemCfDO.getTotalCoreApplyAfter().intValue());
                    data.add(excelSystemDTO);
                    continue;
                }

                // 3、中长尾预约
                if (PplOrderSourceTypeEnum.APPLY_AUTO_FILL_LONGTAIL.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelSystemDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelSystemDTO);
                    excelSystemDTO.setTargetName("中长尾预约");
                    excelSystemDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNumApplyAfter().intValue()
                                    : pplItemCfDO.getTotalCoreApplyAfter().intValue());
                    data.add(excelSystemDTO);
                    continue;
                }

                // 4、未分类
                if (PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelSystemDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelSystemDTO);
                    excelSystemDTO.setTargetName("未分类");
                    excelSystemDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNumApplyAfter().intValue()
                                    : pplItemCfDO.getTotalCoreApplyAfter().intValue());
                    data.add(excelSystemDTO);
                }

            }
        }
        return data;
    }

    // （构建excel）明细 - PPL转单、紧急订单
    private List<WaveDetailExportExcelDTO> buildNewAppliedItemExcelData(QueryWaveReq req,
            Map<String, String> instanceConfigMap) {

        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        //  查询预约信息
        List<DwdCrpPplItemCfDO> pplItemCfDOList = queryAppliedItems(req);
        List<AdsMckForecastSummaryDfDO> adsMckForecastSummaryDfList = queryEmergentOrderItems(req);

        // 设置 预测excel信息（PPL转单、紧急订单）
        for (String statisticalScope : req.getStatisticalScope()) {
            String statisticalName = StatisticalScopeEnum.getNameByCode(statisticalScope);

            for (DwdCrpPplItemCfDO pplItemCfDO : pplItemCfDOList) {
                boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(pplItemCfDO.getProduct());

                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(pplItemCfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), excelDTO.getInstanceType()));
                excelDTO.setStatisticalScope(statisticalName);

                // 1、PPL转单
                if (PplOrderSourceTypeEnum.IMPORT.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelAppliedDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelAppliedDTO);
                    excelAppliedDTO.setTargetName("PPL转单");
                    excelAppliedDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNum().intValue()
                                    : pplItemCfDO.getTotalCore());
                    data.add(excelAppliedDTO);
                    continue;
                }

                // 2、紧急订单
                if (PplOrderSourceTypeEnum.APPLY_AUTO_FILL.getCode().equals(pplItemCfDO.getSource())) {
                    WaveDetailExportExcelDTO excelSystemDTO = new WaveDetailExportExcelDTO();
                    BeanUtils.copyProperties(excelDTO, excelSystemDTO);
                    excelSystemDTO.setTargetName("紧急订单");
                    excelSystemDTO.setTargetValue(
                            isGpu ? pplItemCfDO.getTotalGpuNumApplyAfter().intValue()
                                    : pplItemCfDO.getTotalCoreApplyAfter().intValue());
                    data.add(excelSystemDTO);
                    continue;
                }

            }

            for (AdsMckForecastSummaryDfDO adsMckForecastSummaryDfDO : adsMckForecastSummaryDfList) {
                boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(adsMckForecastSummaryDfDO.getProduct());

                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(adsMckForecastSummaryDfDO);
                excelDTO.setCommonInstanceType(
                        instanceConfigMap.getOrDefault(excelDTO.getInstanceType(), excelDTO.getInstanceType()));
                excelDTO.setStatisticalScope(statisticalName);

                // 2、紧急订单
                WaveDetailExportExcelDTO excelSystemDTO = new WaveDetailExportExcelDTO();
                BeanUtils.copyProperties(excelDTO, excelSystemDTO);
                excelSystemDTO.setTargetName("紧急订单");
                excelSystemDTO.setTargetValue(
                        isGpu ? adsMckForecastSummaryDfDO.getTotalGpuNum()
                                : adsMckForecastSummaryDfDO.getTotalCore());
                data.add(excelSystemDTO);
                continue;
            }
        }
        return data;
    }


    // （构建excel）明细 - 版本拼接宽表  0最新版PPL拼订单 （1行业PPL、1.1已转单PPL、1.2未转单PPL、  2行业订单、2.1PPL转单、2.2紧急订单）
    private List<WaveDetailExportExcelDTO> buildPplJoinOrderVersionItemExcelData(QueryWaveReq req) {


        List<WaveDetailExportExcelDTO> data = new ArrayList<>();

        // 0-最新版PPL拼订单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> pplJoinOrderVersionItems = queryPplJoinOrderVersionItems(req);

        if (ListUtils.isNotEmpty(req.getProjectType())) {
            pplJoinOrderVersionItems = pplJoinOrderVersionItems.stream().filter(o -> req.getProjectType().contains(
                    PplWaveProjectTypeEnum.getNameByCode(o.getCbsIsSpike()))).collect(Collectors.toList());
        }

        // 1-行业PPL
//        List<DwdCrpPplJoinOrderVersionCfDO> industryPpl = pplJoinOrderVersionItems.stream()
//                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
//                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource())))
//                .collect(Collectors.toList());

        // 1.1-已转单PPL
        List<DwsCrpPplJoinOrderVersionNewestCfDO> appliedPpl = pplJoinOrderVersionItems.stream()
                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource()))
                        && PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus()))
                .collect(Collectors.toList());

        // 1.2-未转单PPL
        List<DwsCrpPplJoinOrderVersionNewestCfDO> notAppliedPpl = pplJoinOrderVersionItems.stream()
                .filter(e -> (PplJoinOrderVersionItemDataSource.VERSION.getCode().equals(e.getDataSource())
                        || PplJoinOrderVersionItemDataSource.EXTEND.getCode().equals(e.getDataSource()))
                        && !PplItemStatusEnum.APPLIED.getCode().equals(e.getStatus()))
                .collect(Collectors.toList());

        // 2-行业订单
//        List<DwdCrpPplJoinOrderVersionCfDO> order = pplJoinOrderVersionItems.stream()
//                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource()))
//                .collect(Collectors.toList());

        // 2.1-PPL转单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> transformOrder = pplJoinOrderVersionItems.stream()
                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource())
                        && OrderSourceEnum.PPL_TRANSFORM.getCode().equals(e.getOrderSource()))
                .collect(Collectors.toList());

        // 2.2-紧急订单
        List<DwsCrpPplJoinOrderVersionNewestCfDO> emergentOrder = pplJoinOrderVersionItems.stream()
                .filter(e -> PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(e.getDataSource())
                        && OrderSourceEnum.URGENT_ORDER.getCode().equals(e.getOrderSource()))
                .collect(Collectors.toList());


        // 最新版PPL拼订单
//        data.addAll(transformExcelDTO("最新版PPL拼订单", pplJoinOrderVersionItems));

        // 行业PPL
//        data.addAll(transformExcelDTO("行业PPL", industryPpl));

        // 已转单PPL
        data.addAll(transformExcelDTO(WaveTargetNameEnum.APPLIED_PPL, appliedPpl, req));

        // 未转单PPL
        data.addAll(transformExcelDTO(WaveTargetNameEnum.NOT_APPLIED_PPL, notAppliedPpl, req));

        // 行业订单
//        data.addAll(transformExcelDTO("行业订单", order));

        // PPL转单
        data.addAll(transformExcelDTO(WaveTargetNameEnum.PPL_TRANSFORM, transformOrder, req));

        // 紧急订单
        data.addAll(transformExcelDTO(WaveTargetNameEnum.URGENT_ORDER, emergentOrder, req));
        return data;


    }

    private List<WaveDetailExportExcelDTO> transformExcelDTO(WaveTargetNameEnum waveTargetNameEnum,List<DwsCrpPplJoinOrderVersionNewestCfDO> pplJoinOrderVersionItems, QueryWaveReq req){

        List<WaveDetailExportExcelDTO> result = new ArrayList<>();
        if (ListUtils.isEmpty(pplJoinOrderVersionItems)) {
            return result;
        }

        List<DwsCrpPplJoinOrderVersionNewestCfDO> industry = pplJoinOrderVersionItems.stream()
                .filter(e -> sourceOfIndustry.contains(e.getSource())).collect(Collectors.toList());

        List<DwsCrpPplJoinOrderVersionNewestCfDO> yun = pplJoinOrderVersionItems.stream()
                .filter(e -> sourceOfYun.contains(e.getSource()) && e.getIsComd() != 1).collect(Collectors.toList());

        if (!req.getProductCategory().equals("CBS")) {
            // 行业预测
            for (DwsCrpPplJoinOrderVersionNewestCfDO item : industry) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                excelDTO.setStatisticalScope(StatisticalScopeEnum.INDUSTRY_PPL.getName());
                excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                excelDTO.setTargetValue(Ppl13weekProductTypeEnum.GPU.getName().equals(excelDTO.getProduct())
                        ? item.getTotalGpuNum().intValue()
                        : item.getTotalCore());
                excelDTO.setProductCategory(req.getProductCategory());
                result.add(excelDTO);
            }

            for (DwsCrpPplJoinOrderVersionNewestCfDO item : yun) {
                WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                excelDTO.setStatisticalScope(StatisticalScopeEnum.YUN_PPL.getName());
                excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                excelDTO.setTargetValue(Ppl13weekProductTypeEnum.GPU.getName().equals(excelDTO.getProduct())
                        ? item.getTotalGpuNum().intValue()
                        : item.getTotalCore());
                excelDTO.setProductCategory(req.getProductCategory());
                result.add(excelDTO);
            }
        }else {
            String dataSource = pplJoinOrderVersionItems.get(0).getDataSource();
            if (PplJoinOrderVersionItemDataSource.ORDER.getCode().equals(dataSource)) {
                for (DwsCrpPplJoinOrderVersionNewestCfDO item : industry) {
                    Map<String, List<DiskItem>> orderDiskInfo = getOrderDiskInfo(Collections.singletonList(item), req, false);
                    List<DiskItem> systemList = orderDiskInfo.get("system");
                    List<DiskItem> dataList = orderDiskInfo.get("data");
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.INDUSTRY_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setProductCategory(req.getProductCategory());
                    for (DiskItem diskItem : systemList) {
                        WaveDetailExportExcelDTO excel = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excel);
                        excel.setVolumeType(diskItem.getDiskType());
                        excel.setDiskType(PplWaveDiskTypeEnum.SYSTEM_DISK.getName());
                        excel.setTargetValue(diskItem.getDiskStorage() * diskItem.getDiskNum());
                        result.add(excel);
                    }
                    for (DiskItem diskItem : dataList) {
                        WaveDetailExportExcelDTO excel = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excel);
                        excel.setVolumeType(diskItem.getDiskType());
                        excel.setDiskType(PplWaveDiskTypeEnum.DATA_DISK.getName());
                        excel.setTargetValue(diskItem.getDiskStorage() * diskItem.getDiskNum());
                        result.add(excel);
                    }
                }

                for (DwsCrpPplJoinOrderVersionNewestCfDO item : yun) {
                    Map<String, List<DiskItem>> orderDiskInfo = getOrderDiskInfo(Collections.singletonList(item), req, false);
                    List<DiskItem> systemList = orderDiskInfo.get("system");
                    List<DiskItem> dataList = orderDiskInfo.get("data");
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.YUN_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setProductCategory(req.getProductCategory());
                    for (DiskItem diskItem : systemList) {
                        WaveDetailExportExcelDTO excel = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excel);
                        excel.setVolumeType(diskItem.getDiskType());
                        excel.setDiskType(PplWaveDiskTypeEnum.SYSTEM_DISK.getName());
                        excel.setTargetValue(diskItem.getDiskStorage() * diskItem.getDiskNum());
                        result.add(excel);
                    }
                    for (DiskItem diskItem : dataList) {
                        WaveDetailExportExcelDTO excel = new WaveDetailExportExcelDTO();
                        BeanUtils.copyProperties(excelDTO, excel);
                        excel.setVolumeType(diskItem.getDiskType());
                        excel.setDiskType(PplWaveDiskTypeEnum.DATA_DISK.getName());
                        excel.setTargetValue(diskItem.getDiskStorage() * diskItem.getDiskNum());
                        result.add(excel);
                    }
                }
            } else {
                List<DwsCrpPplJoinOrderVersionNewestCfDO> industrySystem = industry.stream().filter(o -> {
                    for (String volume : req.getVolumeType()) {
                        if (o.getSystemDiskType().contains(volume)) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());
                List<DwsCrpPplJoinOrderVersionNewestCfDO> industryData = industry.stream().filter(o -> {
                    for (String volume : req.getVolumeType()) {
                        if (o.getDataDiskType().contains(volume)) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());

                List<DwsCrpPplJoinOrderVersionNewestCfDO> yunSystem = yun.stream().filter(o -> {
                    for (String volume : req.getVolumeType()) {
                        if (o.getSystemDiskType().contains(volume)) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());

                List<DwsCrpPplJoinOrderVersionNewestCfDO> yunData = yun.stream().filter(o -> {
                    for (String volume : req.getVolumeType()) {
                        if (o.getDataDiskType().contains(volume)) {
                            return true;
                        }
                    }
                    return false;
                }).collect(Collectors.toList());

                for (DwsCrpPplJoinOrderVersionNewestCfDO item : industrySystem) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.INDUSTRY_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setTargetValue(item.getSystemDiskNum() * item.getInstanceNum());
                    excelDTO.setProductCategory(req.getProductCategory());
                    excelDTO.setVolumeType(item.getSystemDiskType());
                    excelDTO.setDiskType("系统盘");
                    result.add(excelDTO);
                }

                for (DwsCrpPplJoinOrderVersionNewestCfDO item : industryData) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.INDUSTRY_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setTargetValue(item.getDataDiskNum() * item.getDataDiskStorage() * item.getInstanceNum());
                    excelDTO.setProductCategory(req.getProductCategory());
                    excelDTO.setVolumeType(item.getDataDiskType());
                    excelDTO.setDiskType("数据盘");
                    result.add(excelDTO);
                }

                for (DwsCrpPplJoinOrderVersionNewestCfDO item : yunSystem) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.YUN_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setTargetValue(item.getSystemDiskNum() * item.getInstanceNum());
                    excelDTO.setProductCategory(req.getProductCategory());
                    excelDTO.setVolumeType(item.getSystemDiskType());
                    excelDTO.setDiskType("系统盘");
                    result.add(excelDTO);
                }

                for (DwsCrpPplJoinOrderVersionNewestCfDO item : yunData) {
                    WaveDetailExportExcelDTO excelDTO = WaveDetailExportExcelDTO.transFrom(item);
                    excelDTO.setStatisticalScope(StatisticalScopeEnum.YUN_PPL.getName());
                    excelDTO.setFirstLevelTargetName(waveTargetNameEnum.getFirstLevelTargetName());
                    excelDTO.setSecondLevelTargetName(waveTargetNameEnum.getSecondLevelTargetName());
                    excelDTO.setTargetName(waveTargetNameEnum.getTargetName());
                    excelDTO.setTargetValue(item.getDataDiskNum() * item.getDataDiskStorage() * item.getInstanceNum());
                    excelDTO.setProductCategory(req.getProductCategory());
                    excelDTO.setVolumeType(item.getDataDiskType());
                    excelDTO.setDiskType("数据盘");
                    result.add(excelDTO);
                }
            }
        }

        return result;
    }
    // 设置是否版本周期内数据标识
    private void setIsInnerVersionFlag(Map<String, PplVersionDO> pplVersionConfigMap, WaveDetailExportExcelDTO excelDTO,
            LocalDate beginBuyDate, LocalDate endBuyDate) {
        PplVersionDO pplVersionDO = pplVersionConfigMap.getOrDefault(excelDTO.getVersionCode(), null);
        if (pplVersionDO == null || beginBuyDate == null || endBuyDate == null) {
            return;
        }
        LocalDate versionBeginDate = YearMonth.of(pplVersionDO.getDemandBeginYear(),
                pplVersionDO.getDemandBeginMonth()).atDay(1);
        LocalDate versionEndDate = YearMonth.of(pplVersionDO.getDemandEndYear(),
                pplVersionDO.getDemandEndMonth()).atEndOfMonth();
        excelDTO.setIsInnerVersion(
                beginBuyDate.isBefore(versionBeginDate) || endBuyDate.isAfter(versionEndDate) ? "否" : "是");
    }

    //  查询预约信息（从ppl_item维度查询）
    private List<DwdCrpPplItemCfDO> queryAppliedItems(QueryWaveReq req) {
        ORMUtils.WhereContent content = req.getWaveDetailContent();
        req.setSourceContent(content);
        appendAuthWhere(content, req.getProduct());
        content.addAnd("status = 'APPLIED' ");
        content.addAnd(
                "yunxiao_order_status not in ('CREATED', 'CANCELED', 'BAD_CANCELED', 'REJECTED') ");
//        content.addAnd("source in ('IMPORT', 'APPLY_AUTO_FILL', 'APPLY_AUTO_FILL_LONGTAIL', 'SYNC_YUNXIAO') ");
        List<DwdCrpPplItemCfDO> pplItemCfDOList = DBList.ckcldStdCrpDBHelper.getAll(DwdCrpPplItemCfDO.class,
                content.getSql(), content.getParams());

        for (DwdCrpPplItemCfDO item : pplItemCfDOList) {
            if (item.getTotalGpuNumApplyAfter() == null) {
                item.setTotalGpuNumApplyAfter(BigDecimal.ZERO);
            }
            if (item.getTotalCoreApplyAfter() == null) {
                item.setTotalCoreApplyAfter(0);
            }
        }

        return pplItemCfDOList;
    }

    /**
     * ppl拼接订单 item 查询。
     * @param req
     * @return
     */
    private List<DwsCrpPplJoinOrderVersionNewestCfDO> queryPplJoinOrderVersionItems(QueryWaveReq req) {
        ORMUtils.WhereContent content = req.getWaveDetailContent();
        req.setSourceContent(content);
        if (ListUtils.isNotEmpty(req.getOrderLabel())){
            // 订单的标签，PPL 的默认(空值)
            content.addAnd("order_label in (?) or order_label = '(空值)'", req.getOrderLabel());
        }
        appendAuthWhere(content, req.getProduct());
//        String newestVersionCode = DBList.ckcldStdCrpDBHelper.getRawOne(String.class, " select version_code "
//                + " from std_crp.dwd_crp_ppl_join_order_version_cf group by version_code order by version_code desc limit 1");
//        content.addAnd("version_code = ?",newestVersionCode);
        content.addAnd("total_core >= 0");

        return DBList.ckcldStdCrpDBHelper.getAll(DwsCrpPplJoinOrderVersionNewestCfDO.class,
                content.getSql(), content.getParams());
    }


    //  查询紧急订单信息(从需求汇总表取)
    private List<AdsMckForecastSummaryDfDO> queryEmergentOrderItems(QueryWaveReq req) {
        ORMUtils.WhereContent content = req.getOrderInfoContent();
        appendAuthWhere(content, req.getProduct());

        LocalDate latestStatTime = DBList.ckcldStdCrpDBHelper.getRawOne(LocalDate.class,
                "select stat_time from std_crp.ads_mck_forecast_summary_df order by stat_time desc limit 1");
        content.addAnd("stat_time = ?", latestStatTime);
        content.addAnd("demand_source = ?", "预约单");
        content.addAnd("order_source = ?", OrderSourceEnum.URGENT_ORDER.getCode());
        List<AdsMckForecastSummaryDfDO> newOrderList = DBList.ckcldStdCrpDBHelper.getAll(
                AdsMckForecastSummaryDfDO.class,
                content.getSql(), content.getParams());

        return newOrderList;
    }

}
