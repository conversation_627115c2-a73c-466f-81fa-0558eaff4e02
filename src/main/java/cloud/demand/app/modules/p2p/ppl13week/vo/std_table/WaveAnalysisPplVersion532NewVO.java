package cloud.demand.app.modules.p2p.ppl13week.vo.std_table;

import cloud.demand.app.modules.p2p.ppl13week.entity.std_table.SumTotalCoreOrGpu;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dws_crp_ppl_item_version_532_new_mif")
public class WaveAnalysisPplVersion532NewVO implements SumTotalCoreOrGpu {

    /** ppl来源<br/>Column: [source] */
    @Column(value = "source")
    private String source;

    /** ppl需求总核心数<br/>Column: [total_core] */
    @Column(value = "total_core")
    private BigDecimal totalCore;

    /** GPU总卡数<br/>Column: [total_gpu_num] */
    @Column(value = "total_gpu_num")
    private BigDecimal totalGpuNum;

    /** 需求类型，NEW新增，ELASTIC弹性，RETURN退回<br/>Column: [demand_type] */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求所属产品，例如CVM&CBS<br/>Column: [product] */
    @Column(value = "product")
    private String product;

    /**
     * ppl需求-年<br/>Column: [year]
     */
    @Column(value = "year")
    private Integer year;

    /**
     * ppl需求-月<br/>Column: [month]
     */
    @Column(value = "month")
    private Integer month;

    /**
     * 是否被干预，0未被干预，1被干预
     */
    @Column(value = "is_comd")
    private Integer isComd;


    @Override
    public BigDecimal totalCoreGet() {
        return this.totalCore;
    }

    @Override
    public BigDecimal totalGpuNumGet() {
        return this.totalGpuNum;
    }

    @Override
    public BigDecimal totalSystemDiskCapacityGet() {
        return BigDecimal.ZERO;
    }

    @Override
    public BigDecimal totalDataDiskCapacityGet() {
        return BigDecimal.ZERO;
    }

    @Override
    public PplDemandTypeEnum demandTypeEnumGet() {
        return PplDemandTypeEnum.getByCode(this.demandType);
    }

    @Override
    public Ppl13weekProductTypeEnum productEnumGet() {
        return Ppl13weekProductTypeEnum.getByName(this.product);
    }

}
