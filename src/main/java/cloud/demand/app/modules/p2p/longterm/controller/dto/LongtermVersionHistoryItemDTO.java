package cloud.demand.app.modules.p2p.longterm.controller.dto;

import cloud.demand.app.modules.p2p.longterm.entity.LongtermVersionHistoryItemDO;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemSourceTypeEnum;
import cloud.demand.app.modules.p2p.longterm.enums.LongtermVersionGroupItemTimeUnitEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import lombok.Data;

@Data
public class LongtermVersionHistoryItemDTO extends LongtermVersionHistoryItemDO {

    private String demandTypeName;

    private Integer demandYear;

    private String timeUnit;

    private String sourceType;

    public static LongtermVersionHistoryItemDTO transFromDO(LongtermVersionHistoryItemDO itemDO){

        LongtermVersionHistoryItemDTO dto = new LongtermVersionHistoryItemDTO();
        dto.setId(itemDO.getId());
        dto.setVersionCode(itemDO.getVersionCode());
        dto.setVersionGroupId(itemDO.getVersionGroupId());
        dto.setIndustryDept(itemDO.getIndustryDept());
        dto.setProduct(itemDO.getProduct());
        dto.setPaasProduct(itemDO.getPaasProduct());
        dto.setYear(itemDO.getYear());
        dto.setDemandType(itemDO.getDemandType());
        if (PplDemandTypeEnum.RETURN.getCode().equals(dto.getDemandType())) {
            dto.setCoreNum(itemDO.getCoreNum().negate());
            dto.setGpuNum(itemDO.getGpuNum().negate());
        } else {
            dto.setCoreNum(itemDO.getCoreNum());
            dto.setGpuNum(itemDO.getGpuNum());
        }

        dto.setDemandYear(itemDO.getYear());
        dto.setDemandTypeName(PplDemandTypeEnum.getNameByCode(dto.getDemandType()));
        dto.setTimeUnit(LongtermVersionGroupItemTimeUnitEnum.YEAR.getCode());
        dto.setSourceType(LongtermVersionGroupItemSourceTypeEnum.HISTORY_SCALE.getCode());
        return dto;

    }

}
