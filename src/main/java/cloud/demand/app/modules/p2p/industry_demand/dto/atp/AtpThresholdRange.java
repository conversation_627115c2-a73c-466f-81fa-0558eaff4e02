package cloud.demand.app.modules.p2p.industry_demand.dto.atp;

import cloud.demand.app.common.excel.checker.PredicateWithRuleMessage.NumberRangePredicate;
import cloud.demand.app.modules.p2p.industry_demand.constant.AtpLevel;
import cn.hutool.core.util.StrUtil;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

@Data
public class AtpThresholdRange extends NumberRangePredicate {

    private int atpLevel;

    private String atpLevelName;

    public AtpThresholdRange(AtpLevel level,
            Number min, Number max, boolean isIncludeMin, boolean isIncludeMax) {
        super(min, max, isIncludeMin, isIncludeMax);
        this.atpLevel = level.getLevel();
        this.atpLevelName = level.getName();
    }

    public static List<AtpThresholdRange> defaultRange() {
        List<AtpThresholdRange> ranges = new ArrayList<>();
        // 供应不足： 可用ATP < 需求*(1+10%)
        ranges.add(new AtpThresholdRange(AtpLevel.lack, null, 1.1, false, false));
        // 供应正常： 需求*(1+50%) >= 可用ATP >= 需求*(1+10%)
        ranges.add(new AtpThresholdRange(AtpLevel.normal, 1.1, 1.5, true, true));
        // 供应充足： 可用ATP > 需求*(1+50%)
        ranges.add(new AtpThresholdRange(AtpLevel.high, 1.5, null, false, false));
        return ranges;
    }

    @Override
    public String getRuleMessage() {
        String fmt = "{}，可用ATP/需求 在 {} 范围内";
        return StrUtil.format(fmt, atpLevelName, getMathRange());
    }
}
