package cloud.demand.app.modules.p2p.ppl13week.service.data_check;

import cloud.demand.app.modules.p2p.ppl13week.dto.DataCheckRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import com.pugwoo.dbhelper.DBHelper;
import java.util.Date;
import java.util.Map;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public abstract class AbstractDataCheckService implements DataCheckService {

    @Resource
    private DataCheckServiceAdapter adapter;

    @Resource
    private DBHelper demandDBHelper;

    @PostConstruct
    public void init() {
        adapter.registry(this);
    }


    public UnificatedVersionData getUnificatedVersionReqData(Map<String, Object> dataMap) {
        UnificatedVersionDTO unificatedVersion = (UnificatedVersionDTO) dataMap.get("unificatedVersion");
        UnificatedVersionEventDO event = (UnificatedVersionEventDO) dataMap.get("event");
        UnificatedVersionData unificatedVersionData = new UnificatedVersionData();
        unificatedVersionData.setUnificatedVersionDTO(unificatedVersion);
        unificatedVersionData.setEvent(event);
        return unificatedVersionData;
    }

    public void finishUnificatedVersionEvent(DataCheckRsp dataCheckRsp, UnificatedVersionEventDO eventDO) {
        if (dataCheckRsp.getIsPass()) {
            eventDO.setIsDone(Boolean.TRUE);
            eventDO.setDeadline(new Date());
            demandDBHelper.update(eventDO);
        }
    }

    @Data
    @ToString
    public class UnificatedVersionData {

        private UnificatedVersionDTO unificatedVersionDTO;

        private UnificatedVersionEventDO event;
    }
}
