package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.ExcelBase;
import cloud.demand.app.common.ExcelBase.ColErrorList;
import cloud.demand.app.common.ExcelBase.ColErrorMessage;
import cloud.demand.app.common.ExcelBase.ErrorList;
import cloud.demand.app.common.ExcelBase.ErrorMessage;
import cloud.demand.app.common.ExcelBase.ExcelRet;
import cloud.demand.app.common.WorkWrapWithAuthentication;
import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.DBHelder;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.entity.rrp.RRPFlowDO;
import cloud.demand.app.entity.rrp.RRPVersionDO;
import cloud.demand.app.entity.shuttle.DeviceApplyDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.entity.BasObsCloudCvmTypeDO;
import cloud.demand.app.modules.common.service.impl.DictServiceImpl;
import cloud.demand.app.modules.erp_transfer_return.service.ErpBaseService;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.service.SupplyPlanQueryService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplGpuGroupViewDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CbsStockPlanRsq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CbsStockPlanRsq.Response;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.CheckStockSupplyResultVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.DescribeCbsStockPlanTaskReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.DescribeCbsStockPlanTaskRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.DescribeCbsStockPlanTaskRsp.Result;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyApplyRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyDemandRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.PplSummaryDto;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplyBuyViewReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplySummaryRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplySummaryRsp.SummaryItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplyViewBuyRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplyViewReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplyViewRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplStockSupplyViewRsp.Item;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryPplSupplyReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockGroupSummaryRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyGroupViewReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyGroupViewRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyGroupViewRsp.Item.Detail;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyGroupViewRsp.Item.StockSupplyData;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyGroupViewRsp.Item.YearMonthData;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.QueryStockSupplyStatusRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.RegionZoneDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.SaveCbsStockPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.stock_supply.SaveCbsStockPlanReq.Disk;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ExportPplVersionItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ExportVersionPplOrStockReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CreateStockSupplyPlanRsp.Data;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderDetailDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchResultDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchSummaryDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.OrderMatchSummaryDTO.MatchItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QueryStockSupplyResultRsp;
import cloud.demand.app.modules.p2p.ppl13week.entity.IdcRiskZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.Ppl13weekCvmSupplyImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplAppliedSupplylDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplConfigStockSupplyDefaultCityZoneDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGroupStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemAppliedDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyGpuDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyGpuDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyReqDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspHostNumSumVO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemSumDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplConsensusStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupApproveResultEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.SystemUserConstant;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyDataSourceEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoAppRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplBMStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConsensusService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConvertPhysicalServerService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDataAccessService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDataBaseStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplGpuStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.QcbsApiService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithAppliedVo;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplRecordItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplStockSupplyReqVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplStockSupplyReqWithRspVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemDictVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemWithOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordWithGroupVO;
import cloud.demand.app.modules.rrp_new_feature.service.W13ProductAcceptIndustryService;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.enums.DemandFlowStepEnum;
import cloud.demand.app.modules.sop_util.entity.ObsBudgetRollAdjustCvmAppendDeviceDO;
import cloud.demand.app.modules.sop_util.service.SopUtilCommonService;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.BaseDataDTO;
import cloud.demand.app.modules.tencent_cloud_utils.dto.yunxiao.MainInstanceTypeDTO;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.enums.FeatureEnum;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.dbhelper.utils.DOInfoReader;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.SortingField;
import com.pugwoo.wooutils.collect.SortingOrderEnum;
import com.pugwoo.wooutils.collect.SortingUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.RedisHelper;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.rainbow.util.StringUtil;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import io.vavr.Tuple4;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;


@Slf4j
@Service
public class PplStockSupplyServiceImpl implements PplStockSupplyService {

    @Resource
    private IndustryDemandDictService industryDemandDictService;

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper shuttleDBHelper;
    @Resource
    private YunxiaoAPIService yunxiaoAPIService;
    @Resource
    private QcbsApiService qcbsApiService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private PplVersionService pplVersionService;
    @Resource
    private PplDataAccessService pplDataAccessService;
    @Resource
    private Alert alert;
    @Resource
    private PplGpuStockSupplyService pplGpuStockSupplyService;
    @Resource
    private PplBMStockSupplyService pplBMStockSupplyService;

    @Resource
    private PplDataBaseStockSupplyService pplDBStockSupplyService;
    @Resource
    private PplConsensusService pplConsensusService;
    @Resource
    private DictService dictService;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private PplConvertPhysicalServerService pplConvertPhysicalServerService;
    @Resource
    private W13ProductAcceptIndustryService w13ProductAcceptIndustryService;

    @Resource
    DBHelper rrpDBHelper;
    @Resource
    private RedisHelper redisHelper;
    @Resource
    private SupplyPlanQueryService supplyPlanQueryService;

    @Resource
    private SopUtilCommonService sopUtilCommonService;

    final int NOT_DEPLOY = 0;
    final int NO_GAP = 0;
    final int HAS_GAP = 1;

    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    /**
     * 预约单 已结束状态
     */
    List<String> endStatus = Lists.newArrayList("CREATED", "FINISHED");

    /**
     * 预约单 已废弃状态
     */
    List<String> discardStatus = Lists.newArrayList("CANCELED", "REJECTED", "BAD_CANCELED");

    /**
     * 满足方式 锁定资源状态枚举
     */
    List<String> lockStatus = Lists.newArrayList("buy", "move");


    private DBHelder demandDb() {
        return ORMUtils.db(demandDBHelper);
    }

    @Override
    public PplStockSupplyDO getVersionLatestDoneSupply(String pplVersionCode) {
        if (StringTools.isBlank(pplVersionCode)) {
            return null;
        }
        return demandDBHelper.getOne(PplStockSupplyDO.class, "where status=? and version_code=? order by id desc",
                PplStockSupplyStatusEnum.DONE.getCode(), pplVersionCode);
    }

    /* 对冲下发的数据 SQL

      select *
      from ppl_version_group_record_item a
               left join ppl_item b
                         on a.ppl_id = b.ppl_id
      where version_group_record_id in (
      #         选出所有的组 group 的record 中id最大的
          select max(id)
          from ppl_version_group_record
          where version_group_id in (
      #         选出一个版本所有的组 group
              select id
              from ppl_version_group
              where version_code = 'test-27-1'
                and deleted = 0)
            and deleted = 0
          group by version_group_id)
        and a.deleted = 0
        and b.deleted = 0
      #   排除已预约的数据
        and b.status != 'APPLIED'
        and a.instance_num != 0 ;

     */

    /**
     * 2022-12-27
     * 1、 排除预约单的数据
     * 2、 排除GPU的数据
     *
     * @param versionCode record 对应的版本
     * @return 所有需要对冲的数据
     */
    public List<PplVersionGroupRecordItemWithOrderVO> getStockDataSwitchStatus(String versionCode, boolean isSystem) {

        List<String> overseas = pplDictService.queryAllRegionName(true);

        String recordSql = "select *\n"
                + "from ppl_version_group_record\n"
                + "where id in (\n"
                + "    select max(id)\n"
                + "    from ppl_version_group_record\n"
                + "    where version_group_id in (\n"
                + "        #   选出一个版本所有的组 group\n"
                + "        select id\n"
                + "        from ppl_version_group\n"
                + "        where version_code = ? and product in (?) \n"
                + "          and deleted = 0)\n"
                + "      and deleted = 0\n"
                + "    group by version_group_id\n"
                + "    );";

        List<PplVersionGroupRecordWithGroupVO> allRecords = demandDBHelper.getRaw(
                PplVersionGroupRecordWithGroupVO.class, recordSql,versionCode,Ppl13weekProductTypeEnum.getCvmCategoryProductNameList());

        if (isSystem) {
            allRecords = allRecords.stream()
                    .filter((o) -> Strings.equals(PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode(), o.getStatus()))
                    .collect(Collectors.toList());
        } else {
            // 查询出所有处于云运管干预节点的分组
            List<PplVersionGroupRecordWithGroupVO> comdNode = allRecords.stream()
                    .filter((o) -> Strings.equals(PplVersionGroupStatusEnum.COMD_INTERVENE.getCode(), o.getStatus()))
                    .collect(Collectors.toList());

            allRecords = allRecords.stream().filter((o) -> Lang.list(PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode(),
                    PplVersionGroupStatusEnum.CREATE.getCode(), PplVersionGroupStatusEnum.COMD_APPROVE.getCode(),
                    PplVersionGroupStatusEnum.DONE.getCode()).contains(o.getStatus())).collect(Collectors.toList());

            // 所有处于云运管干预节点，且没有PPL数据的分组 删除并完成待办
            List<PplVersionGroupRecordWithGroupVO> deleteVersionGroup = new ArrayList<>();
            for (PplVersionGroupRecordWithGroupVO pplVersionGroupRecordWithGroupVO : comdNode) {
                List<PplVersionGroupRecordItemDO> recordItem = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                        "where version_group_record_id = ? and instance_num > 0",
                        pplVersionGroupRecordWithGroupVO.getId());
                if (recordItem.size() == 0) {
                    deleteVersionGroup.add(pplVersionGroupRecordWithGroupVO);
                } else {
                    // 如果有数据则加入到待处理记录中
                    allRecords.add(pplVersionGroupRecordWithGroupVO);
                }
            }
            if (!CollectionUtils.isEmpty(deleteVersionGroup)) {
                pplVersionGroupService.removeGroup(deleteVersionGroup);
            }

            // 同时流转状态 将需要转化至库存对冲的 节点转化
            List<PplVersionGroupRecordWithGroupVO> notInStockSupply = allRecords.stream()
                    .filter((o) -> !Strings.equals(PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode(), o.getStatus()))
                    .collect(Collectors.toList());
            pplVersionGroupService.toStockSupplyStatus(notInStockSupply);
        }

        // 移除同步预约单状态&对冲逻辑
//        List<Long> versionGroupId = allRecords.stream()
//                .filter((o) -> !Strings.equals(o.getGroupDO().getProduct(), Ppl13weekProductTypeEnum.GPU.getName()))
//                .map(PplVersionGroupRecordDO::getVersionGroupId).collect(Collectors.toList());
//        for (Long groupId : versionGroupId) {
//            long startWatch = System.currentTimeMillis();
//            pplVersionGroupService.updateYunXiaoApplyData(groupId);
//            startWatch = System.currentTimeMillis() - startWatch;
//            taskLogService.genRunWarnLog("updateYunXiaoApplyData " + groupId, "approveVersionGroup",
//                    String.valueOf(startWatch));
//        }

        WhereContent whereContent = new WhereContent();
        List<Long> recordIds = allRecords.stream().map(PplVersionGroupRecordDO::getId).collect(Collectors.toList());
        // 仅获取开始购买日期 > 版本开始日期的数据
        PplVersionDO versionDO = pplVersionService.getByVersionCode(versionCode);
        // 23-11-30  版本结束日期之外的也下发对冲
//        Date versionEndDate = cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonth(
//                cloud.demand.app.common.utils.DateUtils.getNextYearMonth(versionDO.getDemandEndYear(),
//                        versionDO.getDemandEndMonth()));
        Date versionBeginDate = cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonth(
                versionDO.getDemandBeginYear() + "-" + versionDO.getDemandBeginMonth());
        whereContent.andNotEqual(PplVersionGroupRecordItemWithOrderVO::getInstanceNum, 0);
        whereContent.andIn(PplVersionGroupRecordItemWithOrderVO::getVersionGroupRecordId, recordIds);
        whereContent.andGTE(PplVersionGroupRecordItemWithOrderVO::getBeginBuyDate, versionBeginDate);
//        whereContent.andLT(PplVersionGroupRecordItemWithOrderVO::getBeginBuyDate, versionEndDate);
        // 只查未锁定的ppl
        whereContent.andEqual(PplVersionGroupRecordItemWithOrderVO::getIsLock, Boolean.FALSE);
        List<PplVersionGroupRecordItemWithOrderVO> all = demandDb().getAll(PplVersionGroupRecordItemWithOrderVO.class,
                whereContent);
//        23-11-22 已预约数据也取版本中的下发
//        all = ListUtils.filter(all, (o) -> {
//            // 过滤掉 预约状态的数据，预约状态 从 GroupRecordItem获取
//            return !Strings.equals(o.getStatus(), PplItemStatusEnum.APPLIED.getCode());
//        });

        all = ListUtils.filter(all, (o) -> {
            // 过滤掉 海外需求且不满足海外需求年月数据的ppl
            // 即留下 非海外 或者 满足海外年月的ppl
            return !overseas.contains(o.getRegionName()) || versionDO.isSatisfyOverseasYearMonth(o.getBeginBuyDate());
        });

        all = ListUtils.filter(all, (o) -> {
            // 过滤掉 产品为GPU、裸金属的数据
            return !Strings.equals(o.getProduct(), Ppl13weekProductTypeEnum.GPU.getName()) && !Strings.equals(
                    o.getProduct(), Ppl13weekProductTypeEnum.BM.getName());
        });

        all = ListUtils.filter(all, (o) -> {
            // 过滤掉 所有被干预的数据
            return o.getIsComd().equals(Boolean.FALSE);
        });

        all = ListUtils.filter(all, (o) -> {
            // 过滤掉 中长尾分组中的指定机型（S5/M5/SA2/DA4） 不下发对冲
            List<String> instanceType = Arrays.asList("S5", "M5", "SA2", "DA4");
            return (!o.getIndustryDept().equals(IndustryDeptEnum.LONG_TAIL.getName())
                    || !instanceType.contains(o.getInstanceType()));
        });

        // 设置都是来自 recordItem 中的，只有退回的数据来自 item
        ListUtils.forEach(all, (o) -> o.setFromItem(false));

        return all;
    }


    /**
     * 获取订单采购满足的数据
     * @param versionCode
     * @return
     */
    public List<PplStockSupplyReqDO> getOrderBuyItemData(String versionCode,Long supplyId,Integer priority){

        // 1.查出已锁定的PPL
        String recordSql = "select id\n"
                + "from ppl_version_group_record\n"
                + "where id in (\n"
                + "    select max(id)\n"
                + "    from ppl_version_group_record\n"
                + "    where version_group_id in (\n"
                + "        #   选出一个版本所有的组 group\n"
                + "        select id\n"
                + "        from ppl_version_group\n"
                + "        where version_code = ? and product in (?) \n"
                + "          and status = 'STOCK_SUPPLY' and deleted = 0)\n"
                + "      and deleted = 0\n"
                + "    group by version_group_id\n"
                + "    );";

        List<Long> recordIds = demandDBHelper.getRaw(Long.class, recordSql,versionCode,
                Ppl13weekProductTypeEnum.getCvmCategoryProductNameList());
        if (recordIds.isEmpty()){
            return new ArrayList<>();
        }
        List<PplVersionGroupRecordItemWithOrderVO> itemList = demandDBHelper.getAll(PplVersionGroupRecordItemWithOrderVO.class,
                "where version_group_record_id in (?) and is_lock = ?", recordIds, Boolean.TRUE);

        if (itemList.isEmpty()){
            return new ArrayList<>();
        }
        Map<String, PplVersionGroupRecordItemWithOrderVO> orderNumberToPpl = itemList.stream().collect(
                Collectors.toMap(PplVersionGroupRecordItemWithOrderVO::getYunxiaoOrderId, v->v, (a, b) -> a));



        // 2.查出供应方案是采购的订单
        List<OrderSupplyPlanDetailDO> buySupplyDetail = supplyPlanQueryService.querySupplyPlanDetailByOrderNumber(
                PplStockSupplyMatchTypeEnum.BUY.getCode(), new ArrayList<>(orderNumberToPpl.keySet()));

        if (buySupplyDetail.isEmpty()){
            return  new ArrayList<>();
        }

        // 3.生成req
        List<PplStockSupplyReqDO> result = new ArrayList<>();

        for (OrderSupplyPlanDetailDO orderSupplyPlanDetailDO : buySupplyDetail) {
            PplVersionGroupRecordItemWithOrderVO item = orderNumberToPpl.get(
                    orderSupplyPlanDetailDO.getOrderNumber());
            if (item == null){
                continue;
            }
            PplStockSupplyReqDO req = transCvmFrom(item, supplyId);
            if (req == null){
                continue;
            }
            req.setDataSource(PplStockSupplyDataSourceEnum.ORDER_BUY.getCode());
            // 当是订单锁定采购时 remark设置为订单号
            req.setOrderNumber(orderSupplyPlanDetailDO.getOrderNumber());
            priority++;
            req.setPriority(priority);
            result.add(req);
        }

        return result;

    }

    private PplVersionGroupRecordItemWithOrderVO toItem(PplItemWithOrderVO source) {
        PplVersionGroupRecordItemWithOrderVO ret = new PplVersionGroupRecordItemWithOrderVO();
        ret.setPplOrderDO(source.getPplOrderDO());
        ret.setPplOrder(source.getPplOrder());
        ret.setPplId(source.getPplId());
        ret.setStatus(source.getStatus());
        ret.setProduct(source.getProduct());
        ret.setDemandType(source.getDemandType());
        ret.setDemandScene(source.getDemandScene());
        ret.setProjectName(source.getProjectName());
        ret.setBillType(source.getBillType());
        ret.setWinRate(source.getWinRate());
        ret.setImportantDemand(source.getImportantDemand());
        ret.setBeginBuyDate(source.getBeginBuyDate());
        ret.setEndBuyDate(source.getEndBuyDate());
        ret.setBeginElasticDate(source.getBeginElasticDate());
        ret.setEndElasticDate(source.getEndElasticDate());
        ret.setNote(source.getNote());
        ret.setRegionName(source.getRegionName());
        ret.setZoneName(source.getZoneName());
        ret.setInstanceType(source.getInstanceType());
        ret.setInstanceModel(source.getInstanceModel());
        ret.setInstanceNum(source.getInstanceNum());
        ret.setTotalCore(source.getTotalCore());
        ret.setAlternativeInstanceType(source.getAlternativeInstanceType());
        ret.setAffinityType(source.getAffinityType());
        ret.setAffinityValue(source.getAffinityValue());
        ret.setSystemDiskType(source.getSystemDiskType());
        ret.setSystemDiskStorage(source.getSystemDiskStorage());
        ret.setSystemDiskNum(source.getSystemDiskNum());
        ret.setDataDiskType(source.getDataDiskType());
        ret.setDataDiskStorage(source.getDataDiskStorage());
        ret.setDataDiskNum(source.getDataDiskNum());
        ret.setId(source.getId());
        ret.setDeleted(source.getDeleted());
        ret.setCreateTime(source.getCreateTime());
        ret.setUpdateTime(source.getUpdateTime());
        return ret;
    }


    /**
     * 查询当前状态
     *
     * @param versionCode 对应的版本
     */
    @Override
    public QueryStockSupplyStatusRsp queryStockSupplyStatus(String versionCode) {

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyDO::getVersionCode, versionCode);
        whereContent.order("id desc");
        PplStockSupplyDO pplStockSupplyDO = demandDb().getOne(PplStockSupplyDO.class, whereContent);

        if (pplStockSupplyDO == null) {
            return new QueryStockSupplyStatusRsp();
        }

        QueryStockSupplyStatusRsp ret = new QueryStockSupplyStatusRsp();

        ret.setStatus(pplStockSupplyDO.getStatus());
        ret.setStockId(pplStockSupplyDO.getId());
        ret.setCreateTime(DateUtils.format(pplStockSupplyDO.getCreateTime()));
        ret.setOperator(pplStockSupplyDO.getOperator());

        ret.setCbsStatus(PplStockSupplyStatusEnum.getNameByCode(pplStockSupplyDO.getCbsTaskStatus()));
        ret.setCvmStatus(PplStockSupplyStatusEnum.getNameByCode(pplStockSupplyDO.getCvmTaskStatus()));
        ret.setCvmErrorMsg(pplStockSupplyDO.getCvmErrorMsg());
        ret.setCbsErrorMsg(pplStockSupplyDO.getCbsErrorMsg());

        if (Strings.equals(pplStockSupplyDO.getStatus(), PplStockSupplyStatusEnum.SENT.getCode())) {
            if (Strings.equals("DONE", pplStockSupplyDO.getCbsTaskStatus())) {
                ret.setCbsStatus("已回传");
            } else if (Strings.equals("SENT", pplStockSupplyDO.getCbsTaskStatus())) {
                ret.setCbsStatus("对冲中");
            }
            if (Strings.equals("DONE", pplStockSupplyDO.getCvmTaskStatus())) {
                ret.setCvmStatus("已回传");
            } else if (Strings.equals("SENT", pplStockSupplyDO.getCvmTaskStatus())) {
                ret.setCvmStatus("对冲中");
            }
        }
        if (Strings.equals(pplStockSupplyDO.getStatus(), PplStockSupplyStatusEnum.DONE.getCode())) {
            ret.setCbsStatus("已完成");
            ret.setCvmStatus("已完成");
        }

        if (pplStockSupplyDO.getIssueStatus() != 0) {
            if (pplStockSupplyDO.getIssueStatus().equals(1)) {
                // 下发成功
                ret.setStatus("FINISH");
            } else if (pplStockSupplyDO.getIssueStatus().equals(2)) {
                // 下发中
                ret.setStatus("ISSUING");
            } else if (pplStockSupplyDO.getIssueStatus().equals(3)) {
                // 下发失败
                ret.setStatus("ERROR");
            }

        }

        return ret;
    }


    // 这里的状态可能会并发，放到一起，方便查看
    // 最新完成了的版本跳转到下一个状态
    // 开启加了锁，这里不会有
    @Override
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    public void checkGroupPass() {

        WhereContent versionWhere = new WhereContent();
        versionWhere.andEqual(PplVersionDO::getStatus, PplVersionStatusEnum.PROCESS.getCode());
        PplVersionDO versionDO = ORMUtils.db(demandDBHelper).closeInfoLog().getOne(PplVersionDO.class, versionWhere);
        if (versionDO == null) {
            return;
        }

        WhereContent stockWhere = new WhereContent();
        stockWhere.andEqual(PplStockSupplyDO::getVersionCode, versionDO.getVersionCode());
        stockWhere.order("id desc");
        PplStockSupplyDO stockDO = ORMUtils.db(demandDBHelper).closeInfoLog()
                .getOne(PplStockSupplyDO.class, stockWhere);
        // 没有对冲记录， 第一条未完成不流转状态
        if (stockDO == null || !Strings.equals(stockDO.getStatus(), PplStockSupplyStatusEnum.DONE.getCode())) {
            return;
        }

        WhereContent groupWhere = new WhereContent();
//            groupWhere.andEqual(PplVersionGroupDO::getStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode());
        groupWhere.andEqual(PplVersionGroupDO::getVersionCode, versionDO.getVersionCode());
        groupWhere.andEqual(PplVersionGroupDO::getStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode());
        groupWhere.andNotEqual(PplVersionGroupDO::getProduct, Ppl13weekProductTypeEnum.GPU.getName());
        List<PplVersionGroupDO> allGroups = ORMUtils.db(demandDBHelper).closeInfoLog()
                .getAll(PplVersionGroupDO.class, groupWhere);

        // 所有库存对冲的状态流转到下一个
        if (Lang.isEmpty(allGroups)) {
            return;
        }
        //所有处于 stock_supply 的分组 自动进入下一步
        for (PplVersionGroupDO oneGroup : allGroups) {
            ApproveVersionGroupReq data = new ApproveVersionGroupReq();
            data.setApproveNote("对冲完成，自动通过");
            data.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            data.setGroupId(oneGroup.getId());
            data.setSystem(true);
            data.setCurrentStatus(PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode());
            //
            pplVersionGroupService.approveVersionGroupWithOutLock(data);
        }
        setAppendInfo(stockDO, "已自动进入下一状态， groupId:" + Strings.join(";", allGroups));
    }

    /**
     * 计算已预约数据的需求缺口
     * 1. 计算已预约数据的缺口情况
     * 2. 更新版本最后一个版本中ppl的预约状态
     *
     * 输出结果 已预约数据
     * applyGapStatus 0 无缺口 1 有缺口
     * ｜-applyStatus 1 未锁定资源，2 部分锁定 。3 已结束
     * deployFlag  0 不需要下发 1 下发 2  下发完成(回调)  --后续（下发对冲）流程触发
     *
     * @param versionCode
     */
    @Override
    @Transactional("demandTransactionManager")
    public void calcApplyForSupply(String versionCode) {
        PplAppliedSupplylDO one = demandDBHelper.getOne(PplAppliedSupplylDO.class, "where version_code =?",
                versionCode);
        if (one != null) {
            // 如果当前版本已经存在已预约数据,那么不再进行预约数据生成
            return;
        }
        PplVersionDO versionDO = pplVersionService.getByVersionCode(versionCode);

        val versionBeginDate = LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);
        val versionEndDate = LocalDate.of(versionDO.getDemandEndYear(), versionDO.getDemandEndMonth(), 1).plusMonths(1);
        WhereContent applyWhere = new WhereContent();
        applyWhere.andEqual("t1.status", "APPLIED"); //已预约数据
        applyWhere.andNotEqual("t1.yunxiao_order_id", ""); //过滤掉 预约单中云霄单号为空的单据 （云运管干预）
        applyWhere.andGTE("t1.begin_buy_date", versionBeginDate);
//        applyWhere.andLT("t1.end_buy_date", versionEndDate);
        // 通过yunxiao_oredr_id 关联
        val appliedPpl = demandDBHelper.getAll(PplItemWithAppliedVo.class, applyWhere.getSql(), applyWhere.getParams());

        Map<String, String> stringStringMap = pplDictService.queryInstanceTypeNamePrefixMap();
        Map<String, String> landMap = pplDictService.queryRegion2Country();
        log.info("辅助数据预加载...");

        // 已预约数据
        List<PplAppliedSupplylDO> completeItems = appliedPpl.stream()
                .filter(o -> Objects.nonNull(o.getAppliedItem()))
                .map(vo -> {
                    // 已结束的单据是用原始需求还是用已预约的需求？
                    PplItemDO item = vo.getItem();
                    PplItemAppliedDO apply = vo.getAppliedItem();

                    PplAppliedSupplylDO detail = new PplAppliedSupplylDO();
                    detail.setVersionCode(versionCode);
                    detail.setPplOrder(item.getPplOrder());
                    detail.setPplId(item.getPplId());
                    detail.setProduct(item.getProduct());
                    detail.setWinRate(item.getWinRate());
                    detail.setInstanceNum(item.getInstanceNum());
                    detail.setInstanceCore(item.getTotalCore());
                    detail.setApplyStatus(YunxiaoOrderStatusEnum.getNameByCode(item.getYunxiaoOrderStatus()));
                    // 上海 -》国内
                    detail.setCountryName(landMap.getOrDefault(item.getRegionName(), ""));

                    detail.setApplyOrderId(apply.getYunxiaoOrderId());
                    detail.setApplyOrderDetailId(apply.getYunxiaoDetailId());
                    detail.setIndustryDept(apply.getIndustryDept());
                    detail.setCustomerType(apply.getCustomerType());
                    detail.setCustomerShortName(apply.getCustomerShortName());
                    detail.setWarZone(apply.getWarZone());
                    detail.setSubmitUser(apply.getSubmitUser());
                    detail.setRegionName(apply.getRegionName());
                    detail.setZoneName(apply.getZoneName());

                    detail.setCustomerUin(apply.getCustomerUin());
                    detail.setDemandScene(apply.getDemandScene());
                    detail.setDemandType(apply.getDemandType());
                    detail.setInstanceType(apply.getInstanceType());
                    detail.setInstanceModel(apply.getInstanceModel());
                    // GN7 -> GPU计算型GN7
                    detail.setInstanceTypeNamePrefix(stringStringMap.getOrDefault(apply.getInstanceType(), ""));
                    detail.setAlternativeInstanceType(apply.getAlternativeInstanceType());

                    detail.setBeginBuyDate(apply.getBeginBuyDate());
                    detail.setEndBuyDate(apply.getEndBuyDate());
                    detail.setYearMonth(apply.getBeginBuyDate().toString().substring(0, 7));
                    detail.setAppRole(yunxiaoAPIService.getYunxiaoAppRoleMapping(detail.getProduct()));
                    detail.setSystemDiskNum(apply.getSystemDiskNum());
                    detail.setSystemDiskType(apply.getSystemDiskType());
                    detail.setSystemDiskStorage(apply.getSystemDiskStorage());
                    detail.setDataDiskNum(apply.getDataDiskNum());
                    detail.setDataDiskStorage(apply.getDataDiskStorage());
                    detail.setDataDiskType(apply.getDataDiskType());
                    // 不再取ItemApplied里面的数据， 大包上之后ppl具体的预约已经落在了item里面
                    detail.setApplyNum(item.getInstanceNumApplyAfter());
                    detail.setApplyCore(item.getTotalCoreApplyAfter());
                    detail.setApplyPayJson(JSON.toJson(apply));

                    if (endStatus.contains(item.getYunxiaoOrderStatus())) {
                        detail.setDeployFlag(NOT_DEPLOY); //不用下发
                        detail.setApplyGapStatus(NO_GAP); //无缺口
                        // 设置锁定资源量 已经结束的单据 锁定资源量=预约量
                        detail.setApplyLockNum(detail.getApplyNum());
                        detail.setApplyLockCore(detail.getApplyCore());
                        detail.setNote("单据本地状态处于草稿或已结束，当作无缺口，不下发");
                    }

                    if (discardStatus.contains(item.getYunxiaoOrderStatus())) {
                        //单据状态已更新
                        detail.setApplyStatus(YunxiaoOrderStatusEnum.getNameByCode(item.getYunxiaoOrderStatus()));
                        detail.setDeployFlag(NOT_DEPLOY);
                        detail.setApplyGapStatus(NO_GAP);
                        detail.setApplyLockNum(0);
                        detail.setApplyLockCore(0);
                        detail.setNote("单据已被取消或驳回，不下发");
                    }
                    return detail;
                }).collect(Collectors.toList());

        log.info("已预约的ppl数量, {}", completeItems.size());

        //已预约未结束需要继续分析资源锁定状态
        completeItems.stream().filter(item -> item.getDeployFlag() != 0)
                .collect(Collectors.groupingBy(PplAppliedSupplylDO::getApplyOrderId)).entrySet().stream()
                .forEach(entry -> {
                    applyStatusReCalc(entry.getKey(), entry.getValue());
                });

//        val ids = completeItems.stream().map(PplAppliedSupplylDO::getPplId).filter(Objects::nonNull)
//                .collect(Collectors.toList());
//        updatePplApplyStatus(versionCode, ids);

        demandDBHelper.insertBatchWithoutReturnId(completeItems);
    }

    @Override
    public List<PplAppliedSupplylDO> queryApplyPpl(String versionCode, String industryDept, String product) {
        WhereSQL sql = new WhereSQL();
        sql.and("version_code = ?", versionCode);
        sql.and("industry_dept = ?", industryDept);
        sql.and("product = ?", product);
        return demandDBHelper.getAll(PplAppliedSupplylDO.class, sql.getSQL(), sql.getParams());
    }

    /**
     * 已预约单据  是否 参与对冲计算
     * 1.单据未完结，并且 ppl-绑定的预约单 只有一个预扣明细，
     * 2.根据供应满足结果，决定还需要对冲多少
     * https://doc.weixin.qq.com/flowchart/f4_AQAAmwbNAMwkpoMOK3uTI66Iyyf4e?scode=AJEAIQdfAAoO8zbcGGAQAAmwbNAMw
     */
    private void applyStatusReCalc(String applyOrder, List<PplAppliedSupplylDO> details) {
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        // 查下云霄预约单最新结果
        OrderDetailDTO orderDetailDTO = yunxiaoAPIService.queryOrderDetail(applyOrder);
        if (orderDetailDTO == null || orderDetailDTO.getData() == null || Lang.isEmpty(
                orderDetailDTO.getData().getOrderDetails())) {
            // 查询不到预约单结果，（如待CVM审批/待CBS审批这种状态） 说明还没锁定资源，需要下发
            details.forEach(detail -> {
                detail.setDeployFlag(1);
                detail.setApplyGapStatus(HAS_GAP);
                detail.setApplyLockNum(0);
                detail.setApplyLockCore(0);
                detail.setNote("云霄接口查询不到,说明预约单还没锁定资源，需要下发");
            });
            return;
        }
        if (endStatus.contains(orderDetailDTO.getData().getStatus())) {
            //结束状态
            details.forEach(detail -> {
                detail.setApplyStatus(YunxiaoOrderStatusEnum.getNameByCode(orderDetailDTO.getData().getStatus()));
                detail.setDeployFlag(NOT_DEPLOY);
                detail.setApplyGapStatus(NO_GAP);
                detail.setApplyLockNum(detail.getApplyNum());
                detail.setApplyLockCore(detail.getApplyCore());
                detail.setNote("单据本地状态处于草稿或已结束，当作无缺口，不下发");
            });
            return;
        }
        if (discardStatus.contains(orderDetailDTO.getData().getStatus())) {
            //废弃单据
            details.forEach(detail -> {
                // 废弃单据数据不会刷到流程中
                detail.setApplyStatus(YunxiaoOrderStatusEnum.getNameByCode(orderDetailDTO.getData().getStatus()));
                detail.setDeployFlag(NOT_DEPLOY);
                detail.setApplyGapStatus(NO_GAP);
                detail.setApplyLockNum(0);
                detail.setApplyLockCore(0);
                detail.setNote("单据已被取消或驳回，不下发");
            });
            return;
        }

        log.info("查询预约单满足情况");
        OrderMatchSummaryDTO summaryDTO = yunxiaoAPIService.getMatchSummary(applyOrder);
        if (summaryDTO == null || summaryDTO.getData() == null || Lang.isEmpty(
                summaryDTO.getData().getMatchDetails())) {
            log.info("{},未反馈满足方式，按预约单下发", applyOrder);
            details.forEach(detail -> {
                detail.setDeployFlag(1);
                detail.setApplyGapStatus(HAS_GAP);
                detail.setApplyLockNum(0);
                detail.setApplyLockCore(0);
                detail.setNote("未反馈满足方式,无需锁单，需要下发");
            });
            return;
        }
        String json = JSON.toJson(summaryDTO);
        //保留满足结果信息，方便后续对账

        // 先假设情况全部为锁单
        details.forEach(detail -> {
            detail.setDeployFlag(NOT_DEPLOY);
            detail.setApplyGapStatus(NO_GAP);
            detail.setApplyLockNum(detail.getApplyNum());
            detail.setApplyLockCore(detail.getApplyCore());
            detail.setNote("存在满足锁单的满足方式,判定这个预约单下所有ppl都被锁单了");
            detail.setApplyPayJson(json);
        });

        List<MatchItem> matchTypeList = summaryDTO.getData().getMatchDetails();
        // 如果 满足方式只有一种 且为大盘满足 且没有预扣 那么无锁单，需要下发  否则其他情况均全部锁单
        if (matchTypeList.size() == 1 && matchTypeList.get(0).getMatchType().equals("satisfy")) {
            OrderMatchResultDTO resultDTO = yunxiaoAPIService.getMatchResult(applyOrder);
            Integer reservedCore = 0;
            if (resultDTO != null || resultDTO.getData() != null) {
                reservedCore = resultDTO.getData().getTotalReservedCpuCount();
            }
            // 预扣核心数等于0 代表还没出现锁单
            if (reservedCore == 0) {
                details.forEach(detail -> {
                    detail.setDeployFlag(1);
                    detail.setApplyGapStatus(HAS_GAP);
                    detail.setApplyLockNum(0);
                    detail.setApplyLockCore(0);
                    detail.setNote("不存在锁单情况,预约单下所有ppl都被需要下发");
                    detail.setApplyPayJson(json);
                });
            }
        }
        // 旧逻辑废弃 23-11-23
        // 预约单多个，满足方式1个 可以分;预约单1个，满足方式多个，也可以分;预约单多个，满足方式多个，无法区分
//        val opt = summaryDTO.getData().getMatchDetails().stream()
//                .filter(d -> !"satisfy".equals(d.getMatchType())).findAny();
//        if (opt.isPresent()) {
//            //大盘满足
//            Integer satisfy = opt.get().getMatchCpuCount();
//            Integer reservedCore = 0;
//            OrderMatchResultDTO resultDTO = yunxiaoAPIService.getMatchResult(applyOrder);
//            if (resultDTO != null || resultDTO.getData() != null) {
//                reservedCore = resultDTO.getData().getTotalReservedCpuCount();
//            }
//            Integer totalGap = satisfy - reservedCore; // 缺口 下发对冲
//
//            //已预扣量(已准备资源量)
//            //总需求
//            BigDecimal totalCore = NumberUtils.sum(details, PplAppliedSupplylDO::getApplyCore);
//
//            log.info("{} satisfy:{},reservedCore:{}", applyOrder, satisfy, reservedCore);
//
//            DictService dictService = SpringUtil.getBean(DictServiceImpl.class);
//            for (PplAppliedSupplylDO detail : details) {
//                if (satisfy <= reservedCore) {
//                    detail.setApplyGapStatus(NO_GAP);
//                    detail.setApplyLockNum(detail.getApplyLockNum());
//                    detail.setApplyLockCore(detail.getApplyCore());
//                    detail.setNote("大盘满足，预扣大于等于大盘， 没有缺口不需要下发");
//
//                } else if (reservedCore == 0) {
//                    //预扣量 < 大盘满足
//                    detail.setApplyGapStatus(HAS_GAP);
//                    detail.setApplyLockNum(0);
//                    detail.setApplyLockCore(0);
//                    detail.setApplyReservedCore(0);
//                    detail.setApplyMatchSatisfy(detail.getApplyCore());
//
//                    detail.setNote("大盘满足，有缺口，无预扣");
//                } else {
//                    //预扣量 < 大盘满足
//                    detail.setApplyGapStatus(HAS_GAP);
//                    log.info("  {} / {}", detail.getApplyCore(), totalCore);
//                    BigDecimal rt = NumberUtils.divide(detail.getApplyCore(), totalCore, 5);
//
//                    detail.setApplyMatchSatisfy(Math.min(detail.getApplyCore(),
//                            rt.multiply(BigDecimal.valueOf(satisfy)).intValue()));
//
//                    detail.setApplyReservedCore(Math.min(detail.getApplyMatchSatisfy(),
//                            rt.multiply(BigDecimal.valueOf(reservedCore)).intValue()));
//
//                    Integer lockCore =
//                            detail.getApplyCore() - (detail.getApplyMatchSatisfy() - detail.getApplyReservedCore());
//                    detail.setApplyLockCore(Math.max(lockCore, 0));
//
//                    int insCore = dictService.getInstanceCoreNum(detail.getInstanceModel());
//                    if (insCore > 0) {
//                        int num = lockCore / insCore;
//                        detail.setApplyLockNum(Math.max(0, num));
//                    } else {
//                        detail.setApplyLockNum(Math.max(detail.getApplyNum(), 0));
//                    }
//                    detail.setNote("大盘满足，预扣不足，有缺口");
//                }
//                detail.setApplyPayJson(json);
//            }
//
//        } else {
//            //没有大盘满足 不需要计算缺口
//            details.forEach(detail -> {
//                detail.setDeployFlag(NOT_DEPLOY);
//                detail.setApplyGapStatus(NO_GAP);
//                detail.setApplyLockNum(detail.getApplyNum());
//                detail.setApplyLockCore(detail.getApplyCore());
//                detail.setNote("没有大盘满足，没有缺口不需要下发");
//                detail.setApplyPayJson(json);
//            });
//        }

    }

    /**
     * 把 审批中的 最后一个版本，数据中的ppl标记为已预约状态
     *
     * @param versionCode
     * @param ids
     */
    private void updatePplApplyStatus(String versionCode, List<String> ids) {
        List<Integer> groupRecordIds = getVersionGroupRecord(versionCode);
        if (!groupRecordIds.isEmpty() && !ids.isEmpty()) {
            val rds = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class, "where version_group_record_id in (?) "
                    + "and ppl_id in (?)", groupRecordIds, ids);
            rds.forEach(r -> r.setStatus("APPLIED"));
            demandDBHelper.update(rds);
        }
    }

    /**
     * 获取分组版本内所有的 最新版本的 record_id
     *
     * @param version
     * @return
     */
    List<Integer> getVersionGroupRecord(String version) {
        String sql = "select max(id)\n"
                + "    from ppl_version_group_record\n"
                + "    where version_group_id in (\n"
                + "        #   选出一个版本所有的组 group\n"
                + "        select id\n"
                + "        from ppl_version_group\n"
                + "        where version_code = ? and product in (?) \n"
                + "          and deleted = 0)\n"
                + "      and deleted = 0\n"
                + "    group by version_group_id";
        List<Integer> groupRecordIds = demandDBHelper.getRaw(Integer.class, sql, version,
                Ppl13weekProductTypeEnum.getCvmCategoryProductNameList());
        log.info("版本 {} ,分组 record_id : {}", version, groupRecordIds);
        return groupRecordIds;
    }

    @Resource
    private DBHelper obsDBHelper;



    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    public void startNewStockSupply(String versionCode, boolean isSystem) {

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionDO::getVersionCode, versionCode);
        PplVersionDO one = demandDb().getOne(PplVersionDO.class, whereContent);
        if (one == null) {
            throw BizException.makeThrow("没有找到对应的版本： %s", versionCode);
        }

        PplStockSupplyDO pplStockSupplyDO = new PplStockSupplyDO();
        pplStockSupplyDO.setVersionCode(versionCode);
        pplStockSupplyDO.setOperator(LoginUtils.getUserName());
        // 进入下一个状态
        pplStockSupplyDO.setStatus(PplStockSupplyStatusEnum.SENDING.getCode());
        pplStockSupplyDO.setCvmTaskStatus(PplStockSupplyStatusEnum.SENDING.getCode());
        pplStockSupplyDO.setCbsTaskStatus(PplStockSupplyStatusEnum.SENDING.getCode());
        //默认 20 秒
        pplStockSupplyDO.setIntervalTime(600);
        demandDBHelper.insert(pplStockSupplyDO);

        List<PplVersionGroupRecordItemWithOrderVO> stockData = getStockDataSwitchStatus(
                pplStockSupplyDO.getVersionCode(), isSystem);

        // 2024-4-22 当前中长尾模型预测PPL条数过多
        // 加入需求聚合逻辑 - 避免对冲条目过多
        buildGroupForecastItem(stockData, pplStockSupplyDO.getId());

        for (PplVersionGroupRecordItemWithOrderVO stockDatum : stockData) {
            if (stockDatum.getBeginBuyDate() != null) {
                String yyyyMM = LocalDateTimeUtil.format(stockDatum.getBeginBuyDate(), "yyyyMM");
                stockDatum.setYearMonth(yyyyMM);
            }
        }
        //2023-1-30  变更排序策略 三个排序指标 根据 年月 - 产品（CVM最优先） - 同月日期
        List<PplVersionGroupRecordItemWithOrderVO> sortedData = stockData.stream()
                .sorted(Comparator.nullsLast(Comparator.comparing(PplVersionGroupRecordItemWithOrderVO::getYearMonth))
                        .thenComparing(Comparator.nullsLast(
                                Comparator.comparing(PplVersionGroupRecordItemWithOrderVO::getProduct))).thenComparing(
                                Comparator.nullsLast(
                                        Comparator.comparing(PplVersionGroupRecordItemDO::getBeginBuyDate))))
                .collect(Collectors.toList());

        List<PplStockSupplyReqDO> insertData = Lang.list();
//        //先插入 已预约缺口数据， 库存优先级  > 未预约
//        List<PplStockSupplyReqDO> applyGap = getFromAppliedGap(versionCode, pplStockSupplyDO.getId());
//        if (!applyGap.isEmpty()) {
//            insertData.addAll(applyGap);
//        }
        Integer priority = 0;
        for (int i = 0; i < sortedData.size(); i++) {
            priority = i + 1;
            PplVersionGroupRecordItemWithOrderVO oneItem = sortedData.get(i);
            // cbs 不传上一个月的退回数据
            if (!oneItem.getFromItem()) {
                insertData.addAll(getCBSReq(oneItem, pplStockSupplyDO.getId(), priority));
            }

            // 临时处理逻辑 cvm实例规格为空不下发 todo @fireflychen
            PplStockSupplyReqDO cvmReq = getCVMReq(oneItem, pplStockSupplyDO.getId(), priority);
            if (cvmReq != null){
                insertData.add(cvmReq);
            }
        }


        // 2024-12-03 增加已锁定的数据到req里
        insertData.addAll(getOrderBuyItemData(versionCode,pplStockSupplyDO.getId(),priority));

        demandDBHelper.insertBatchWithoutReturnId(insertData);
    }

    public void buildGroupForecastItem(List<PplVersionGroupRecordItemWithOrderVO> stockData, Long supplyId) {
        // 过滤除需要聚合的数据
        // 只取CVM的
        // 只取 中长尾行业的 或 is_from_forecast = true （变更为 客户简称中 包含模型预测 的） 的 ppl数据
        List<PplVersionGroupRecordItemWithOrderVO> needGroupData = stockData.stream()
                .filter(v -> v.getProduct().equals(Ppl13weekProductTypeEnum.CVM.getName())
                        && v.getIndustryDept().equals(IndustryDeptEnum.LONG_TAIL.getName())
                        || v.getCustomerShortName().contains("模型预测"))
                .collect(Collectors.toList());

        // 移除掉过滤出来的部分
        stockData.removeAll(needGroupData);

        // 聚合数据
        // 聚合维度： 需求类型 - 开始购买日期 - 结束购买日期 - 可用区 - 机型规格
        List<PplVersionGroupRecordItemWithOrderVO> groupList = new ArrayList<>();
        Map<String, List<PplVersionGroupRecordItemWithOrderVO>> keyToList = needGroupData.stream()
                .collect(Collectors.groupingBy(v -> String.join("@",
                        v.getDemandType(), v.getBeginBuyDate().toString(), v.getEndBuyDate().toString(),
                        v.getZoneName(), v.getInstanceModel())));

        PplOrderDO groupPplOrderDO = buildGroupPplOrder();
        List<PplGroupStockSupplyRspDO> groupStockSupplyRspDOList = new ArrayList<>();
        keyToList.forEach((k, v) -> {
            if (v.size() == 1) {
                // 如果只有一个 直接加入
                groupList.add(v.get(0));
                return;
            }
            PplVersionGroupRecordItemWithOrderVO item = v.get(0);
            PplVersionGroupRecordItemWithOrderVO
                    groupData = new PplVersionGroupRecordItemWithOrderVO();

            // 创建聚合PPL单
            String pplId = pplCommonService.generatePplItemId(groupPplOrderDO.getPplOrder());
            BeanUtils.copyProperties(item, groupData);
            groupData.setPplOrderDO(groupPplOrderDO);
            groupData.setPplOrder(groupPplOrderDO.getPplOrder());
            groupData.setPplId(pplId);
            groupData.setStatus(PplItemStatusEnum.VALID.getCode());
            groupData.setVersionGroupId(null);
            groupData.setVersionGroupRecordId(null);
            groupData.setRecordVersion(null);
            groupData.setIsAcceptAdjust(Boolean.FALSE);
            groupData.setAlternativeInstanceType("");
            groupData.setConsensusStatus(PplConsensusStatusEnum.NOT_CONSENSUS.getCode());
            groupData.setInstanceNum(v.stream().mapToInt(PplVersionGroupRecordItemWithOrderVO::getInstanceNum).sum());
            groupData.setTotalCore(v.stream().mapToInt(PplVersionGroupRecordItemWithOrderVO::getTotalCore).sum());
            groupList.add(groupData);

            for (PplVersionGroupRecordItemWithOrderVO pplVersionGroupRecordItemWithOrderVO : v) {
                PplGroupStockSupplyRspDO pplGroupStockSupplyRspDO = new PplGroupStockSupplyRspDO();
                pplGroupStockSupplyRspDO.setGroupPplId(pplId);
                pplGroupStockSupplyRspDO.setChildPplId(pplVersionGroupRecordItemWithOrderVO.getPplId());
                pplGroupStockSupplyRspDO.setChildInstanceNum(pplVersionGroupRecordItemWithOrderVO.getInstanceNum());
                pplGroupStockSupplyRspDO.setChildTotalCore(pplVersionGroupRecordItemWithOrderVO.getTotalCore());
                pplGroupStockSupplyRspDO.setSupplyId(supplyId);
                groupStockSupplyRspDOList.add(pplGroupStockSupplyRspDO);
            }
        });

        stockData.addAll(groupList);
        // 插入聚合ppl和原ppl的关联关系。
        demandDBHelper.insertBatchWithoutReturnId(groupStockSupplyRspDOList);
    }

    public PplOrderDO buildGroupPplOrder() {
        String pplOrderId = pplCommonService.generatePplOrderId("G");
        PplOrderDO pplOrderDO = new PplOrderDO();
        pplOrderDO.setStatus(PplOrderStatusEnum.VALID.getCode());
        pplOrderDO.setPplOrder(pplOrderId);
        pplOrderDO.setIndustryDept("中长尾");
        pplOrderDO.setIndustry("");
        pplOrderDO.setWarZone("");
        pplOrderDO.setCustomerType(CustomerTypeEnum.EXISTING.getCode());
        pplOrderDO.setCustomerName("模型预测");
        pplOrderDO.setCustomerShortName("模型预测");
        pplOrderDO.setSubmitUser("system");
        pplOrderDO.setCustomerSource("");
        pplOrderDO.setSource(PplOrderSourceTypeEnum.FORECAST.getCode());

        demandDBHelper.insert(pplOrderDO);
        return pplOrderDO;
    }

    @Override
    public boolean hasStartedStockSupply(String versionCode) {
        String versionCodeWhere = "where version_code = ?";
        long bmCount = demandDBHelper.getCount(PplStockSupplyDO.class, versionCodeWhere, versionCode);
        return bmCount > 0;
    }

    @Override
    public void startNewStockSupplyAll(String versionCode) {
        if (Strings.isBlank(versionCode)) {
            throw new BizException("请选择版本启动对冲");
        }
        String key = keyOfStartNewStockSupplyAll(versionCode);
        String value = "running";
        String flag = redisHelper.getString(key);
        if (value.equals(flag)) {
            throw new BizException("请稍后，正在启动对冲中...");
        }
        redisHelper.setString(key, 60, value);
        PplStockSupplyServiceImpl cvmStockService = (PplStockSupplyServiceImpl) SpringUtil
                .getBean(PplStockSupplyService.class);
        WorkWrapWithAuthentication work = new WorkWrapWithAuthentication(
                () -> cvmStockService.asyncStartNewStockSupplyAll(versionCode));
        threadPool.execute(work);

    }

    @Transactional("demandTransactionManager")
    public void asyncStartNewStockSupplyAll(String versionCode) {
        try {
            PplStockSupplyService cvmStockService = SpringUtil.getBean(PplStockSupplyService.class);
            if (!cvmStockService.hasStartedStockSupply(versionCode)) {
                cvmStockService.startNewStockSupply(versionCode, false);
            }
            if (!pplGpuStockSupplyService.hasStartedStockSupply(versionCode)) {
                pplGpuStockSupplyService.startNewGpuStockSupply(versionCode);
            }
            if (!pplBMStockSupplyService.hasStartedStockSupply(versionCode)) {
                pplBMStockSupplyService.startNewStockSupply(versionCode);
            }
            demandDBHelper.executeRaw("update unificated_version_event set is_done = 1,operate_user = ?,deadline= ? "
                            + "where deleted = 0 and event_code = ? and version_id in "
                            + "(select id from unificated_version where deleted = 0 and version_code = ?) ",
                    LoginUtils.getUserNameWithSystem(),
                    new Date(), CrpEventEnum.STOCK_SUPPLY_DEADLINE.getCode(), versionCode
            );
        } catch (Exception e) {
            // 发送异常消息
            alert.sendRtx("dotyou;" + LoginUtils.getUserName(), "版本" + versionCode + "启动对冲异常",
                    "操作人：" + LoginUtils.getUserName() + "\n" + e.getMessage());
            throw e;
        } finally {
            redisHelper.remove(keyOfStartNewStockSupplyAll(versionCode));
        }
    }

    private String keyOfStartNewStockSupplyAll(String versionCode) {
        return "stock.supply.start.all" + versionCode;
    }

    @Override
    public CheckStockSupplyResultVO checkStartNewStockSupplyAll(String versionCode) {
        boolean cvmNotStart = !hasStartedStockSupply(versionCode);
        boolean gpuNotStart = !pplGpuStockSupplyService.hasStartedStockSupply(versionCode);
        boolean bmNotStart = !pplBMStockSupplyService.hasStartedStockSupply(versionCode);
        boolean dbNotStart = !pplDBStockSupplyService.hasStartedStockSupply(versionCode);
        boolean bmSupplyFinish = pplBMStockSupplyService.hasFinishStockSupply(versionCode);


        boolean dbStockFinish = pplDBStockSupplyService.hasFinishStockSupply(versionCode);
        CheckStockSupplyResultVO result = new CheckStockSupplyResultVO();
        result.setBmStockStart(!bmNotStart);
        result.setGpuStockStart(!gpuNotStart);
        result.setCvmStockStart(!cvmNotStart);
        result.setDbStockStart(!dbNotStart);


        result.setBmStockFinish(bmSupplyFinish);
        result.setDbStockFinish(dbStockFinish);
        // 只要有一个没有开始对冲，就能调用启动全部对冲接口
        result.setShowStockAll(cvmNotStart || gpuNotStart || bmNotStart || dbNotStart);
        return result;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    @Synchronized(namespace = "ppl-startAllNewStockSupply", waitLockMillisecond = 1000, keyScript = "args[0]", throwExceptionIfNotGetLock = false)
    public void startAllNewStockSupply(String versionCode) {
        PplStockSupplyService cvmStockService = SpringUtil.getBean(PplStockSupplyService.class);
        if (!cvmStockService.hasStartedStockSupply(versionCode)) {
            cvmStockService.startNewStockSupply(versionCode, false);
        }
        try {
            if (!pplGpuStockSupplyService.hasStartedStockSupply(versionCode)) {
                pplGpuStockSupplyService.startNewGpuStockSupply(versionCode);
            }
        } catch (Exception e){
            AlarmRobotUtil.doAlarm("startNewGpuStockSupply", "GPU启动对冲异常" + e.getMessage(),null,false);
        }

        try {
            if (!pplBMStockSupplyService.hasStartedStockSupply(versionCode)) {
                pplBMStockSupplyService.startNewStockSupply(versionCode);
            }
        } catch (Exception e){
            AlarmRobotUtil.doAlarm("BM-startNewStockSupply", "裸金属启动对冲异常" + e.getMessage(),null,false);
        }

        try {
            if (!pplDBStockSupplyService.hasStartedStockSupply(versionCode)) {
                pplDBStockSupplyService.startNewStockSupply(versionCode);
            }
        } catch (Exception e){
            AlarmRobotUtil.doAlarm("DB-startNewStockSupply", "数据库启动对冲异常" + e.getMessage(),null,false);
        }

    }

    public List<PplStockSupplyReqDO> getFromAppliedGap(String version, Long supplyId) {
        WhereContent cnd = new WhereContent();
        cnd.andEqual("version_code", version);
        cnd.andEqual("apply_gap_status", 1);
        cnd.addAnd(" product != 'GPU(裸金属&CVM)' ");
        cnd.addAnd("product != '裸金属'");
        cnd.order("begin_buy_date");
        List<PplAppliedSupplylDO> ls = demandDBHelper.getAll(PplAppliedSupplylDO.class, cnd.getSql(), cnd.getParams());

        List<String> pplIdList = ListUtils.transform(ls, PplAppliedSupplylDO::getPplId)
                .stream().distinct().collect(Collectors.toList());
        List<PplVersionGroupRecordItemDO> pplVersionItems = pplVersionGroupService
                .queryLatestPplVersionItem(version, pplIdList);
        Map<String, PplVersionGroupRecordItemDO> pplMap = ListUtils.toMap(pplVersionItems,
                PplVersionGroupRecordItemDO::getPplId, item -> item);

        AtomicInteger i = new AtomicInteger(0);

        Map<String, String> regionNameMap = SpringUtil.getBean(DictServiceImpl.class).getRegionNameMap();
        Map<String, String> zoneNameMap = SpringUtil.getBean(DictServiceImpl.class).getZoneNameMap();
        List<PplStockSupplyReqDO> reqs = ls.stream().map(
                item -> {
                    item.setDeployFlag(1);
                    item.setDeployNum(item.getApplyNum() - item.getApplyLockNum());
                    item.setDeployCore(item.getApplyCore() - item.getApplyLockCore());
                    PplStockSupplyReqDO pplStockSupplyReqDO = new PplStockSupplyReqDO();
                    pplStockSupplyReqDO.setProduct(item.getProduct());
                    pplStockSupplyReqDO.setInstanceNum(item.getDeployNum());
                    pplStockSupplyReqDO.setInstanceTotalCore(item.getDeployCore());
                    pplStockSupplyReqDO.setSupplyId(supplyId);
                    pplStockSupplyReqDO.setType("CVM");
                    pplStockSupplyReqDO.setAppRole(yunxiaoAPIService.getYunxiaoAppRoleMapping(item.getProduct()));
                    pplStockSupplyReqDO.setDemandScene(item.getDemandScene());
                    pplStockSupplyReqDO.setInstanceTypeNamePrefix(item.getInstanceTypeNamePrefix());
                    setRegionInfo(pplStockSupplyReqDO);
                    pplStockSupplyReqDO.setCountryName(item.getCountryName());
                    pplStockSupplyReqDO.setPplOrder(item.getPplOrder());
                    pplStockSupplyReqDO.setPplId(item.getPplId());
                    pplStockSupplyReqDO.setCustomerUin(item.getCustomerUin());
                    pplStockSupplyReqDO.setRegionName(item.getRegionName());
                    pplStockSupplyReqDO.setZoneName(item.getZoneName());

                    pplStockSupplyReqDO.setRegion(regionNameMap.getOrDefault(item.getRegionName(), ""));
                    pplStockSupplyReqDO.setZone(zoneNameMap.getOrDefault(item.getZoneName(), ""));

                    pplStockSupplyReqDO.setInstanceType(item.getInstanceType());
                    pplStockSupplyReqDO.setInstanceModel(item.getInstanceModel());
                    pplStockSupplyReqDO.setAlternativeInstanceType(item.getAlternativeInstanceType());
                    pplStockSupplyReqDO.setBeginBuyDate(item.getBeginBuyDate());
                    pplStockSupplyReqDO.setDemandType(item.getDemandType());
                    pplStockSupplyReqDO.setYearMonth(item.getYearMonth());
                    pplStockSupplyReqDO.setIndustryDept(item.getIndustryDept());
                    pplStockSupplyReqDO.setCustomerType(item.getCustomerType());
                    pplStockSupplyReqDO.setCustomerShortName(item.getCustomerShortName());
                    pplStockSupplyReqDO.setWarZone(item.getWarZone());
                    pplStockSupplyReqDO.setDemandScene(item.getDemandScene());
                    pplStockSupplyReqDO.setEndBuyDate(item.getEndBuyDate());
                    pplStockSupplyReqDO.setWinRate(item.getWinRate());
                    pplStockSupplyReqDO.setApplyInstanceNum(item.getApplyLockNum());
                    pplStockSupplyReqDO.setApplyTotalCore(item.getApplyLockCore());
                    pplStockSupplyReqDO.setRemark(item.getApplyOrderId());
                    pplStockSupplyReqDO.setSource(1);
                    pplStockSupplyReqDO.setPriority(i.incrementAndGet());

                    PplVersionGroupRecordItemDO ppl = pplMap.get(item.getPplId());
                    if (ppl != null) {
                        pplStockSupplyReqDO.setConsensusStatus(ppl.getConsensusStatus());
                        pplStockSupplyReqDO.setVersionGroupRecordId(ppl.getVersionGroupRecordId());
                        pplStockSupplyReqDO.setGroupRecordItemId(ppl.getId());
                        pplStockSupplyReqDO.setRecordVersion(ppl.getRecordVersion());
                    }
                    return pplStockSupplyReqDO;
                }).collect(Collectors.toList());
        demandDBHelper.update(ls);
        return reqs;
    }

    private List<PplVersionGroupInfoVO> getAllNotGpuVersionGroup(String versionCode) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupInfoVO::getVersionCode, versionCode);
        whereContent.andNotEqual(PplVersionGroupInfoVO::getProduct, Ppl13weekProductTypeEnum.GPU.getName());
        return ORMUtils.db(DBList.demandDBHelper).getAll(PplVersionGroupInfoVO.class, whereContent);
    }

    @Override
    public QueryStockGroupSummaryRsp queryCvmGroupSummary(String versionCode) {

        List<PplVersionGroupInfoVO> versionGroupVO = getAllNotGpuVersionGroup(versionCode);

        List<Long> lastRecordIds = ListUtils.transform(versionGroupVO, PplVersionGroupInfoVO::getLastRecordId);
        WhereContent itemWhere = new WhereContent();
        itemWhere.andIn(PplVersionGroupRecordItemSumDO::getVersionGroupRecordId, lastRecordIds);
        itemWhere.groupBy("version_group_record_id");
        List<PplVersionGroupRecordItemSumDO> itemGpuSum = ORMUtils.db(DBList.demandDBHelper)
                .getAll(PplVersionGroupRecordItemSumDO.class, itemWhere);

        Map<Long, PplVersionGroupRecordItemSumDO> sumMap = ListUtils.toMap(itemGpuSum,
                PplVersionGroupRecordItemDO::getVersionGroupRecordId, Function.identity());

        List<QueryStockGroupSummaryRsp.Data> transform = ListUtils.transform(versionGroupVO, (o) -> {

            QueryStockGroupSummaryRsp.Data data = new QueryStockGroupSummaryRsp.Data();
            data.setIndustryDept(o.getIndustryDept());
            data.setStatus(o.getStatus());
            data.setProduct(o.getProduct());
            data.setStatusName(PplVersionGroupStatusEnum.getNameByCode(o.getStatus()));
            data.setOperator(o.getCurrentProcessor());

            PplVersionGroupRecordItemSumDO tmp = sumMap.get(o.getLastRecordId());
            if (tmp != null) {
                data.setDemandNum(tmp.getTotalCoreNumSum());
            }
            return data;
        });
        return new QueryStockGroupSummaryRsp(transform);
    }


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(throwExceptionIfNotGetLock = true)
    public void finishStockSupply(String versionCode) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = getVersionLatestDoneSupply(versionCode);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        stockSupply.setIssueStatus(2);
        demandDBHelper.update(stockSupply);

        // 对冲完成更新大版本事件标识
        demandDBHelper.executeRaw(
                "UPDATE unificated_version_event \n"
                        + "SET deadline = ? \n"
                        + "WHERE\n"
                        + "\tdeleted = 0 \n"
                        + "\tAND event_code = ? and version_id IN (\n"
                        + "\tSELECT\n"
                        + "\t\tid \n"
                        + "\tFROM\n"
                        + "\t\tunificated_version \n"
                        + "\tWHERE\n"
                        + "\tdeleted = 0 \n"
                        + "\tAND version_code = ?)", new Date(), CrpEventEnum.STOCK_SUPPLY_DONE.getCode(), versionCode);

//        // 先拿当前唯一生效的13周产品版本实体出来
//        RRPVersionDO rrpVersionDO = rrpDBHelper.getOne(RRPVersionDO.class,
//                "where status = 0");
//        if (rrpVersionDO == null) {
//            AlarmRobotUtil.doAlarm("finishStockSupply", "当前星云预测没有开启的版本，不能同步需求",
//                    SystemUserConstant.ppl13WeekUser,
//                    false);
//            throw new BizException("当前13周产品预测没有开启的版本，不能同步需求");
//        }
//        if (!rrpVersionDO.getIndustryVersion().equals(versionCode)) {
//            AlarmRobotUtil.doAlarm("finishStockSupply", "13周版本号与星云版本未对应上",
//                    SystemUserConstant.ppl13WeekUser,
//                    false);
//            throw new BizException("当前13周产品预测生效的版本只允许接收来自行业版本号为[" +
//                    rrpVersionDO.getIndustryVersion()
//                    + "]的13周行业预测");
//        }
//        // 再判断这个规划产品有没有流程
//        RRPFlowDO flowDO = rrpDBHelper.getOne(RRPFlowDO.class,
//                "where product = ? and rrp_config_id = ?",
//                ProductTypeEnum.CVM.getName(), rrpVersionDO.getId());
//        Date now = new Date();
//
//        List<Integer> rejectStatus = Arrays.asList(DemandFlowStepEnum.REJECT_ID_ARRAY);
//        if (DateUtils.toLocalDate(rrpVersionDO.getStartTime()).isAfter(DateUtils.toLocalDate(now))
//                || DateUtils.toLocalDate(rrpVersionDO.getEndTime()).isBefore(DateUtils.toLocalDate(now))) {
//            // 此时说明不在版本开放时间了，没有提交过的不允许同步PPL
//            // 但是提交过被驳回的，版本关闭之前依旧支持同步，可以再次提交
//            if (!(flowDO != null && rejectStatus.contains(flowDO.getCurrentStep()))) {
//                throw new BizException("当前版本[" + rrpVersionDO.getPlanVersion() + "]预测录入时间已结束，不允许同步");
//            }
//        }

        threadPool.execute(() -> asyncApprove(versionCode, stockSupply.getId()));

    }

    private void asyncApprove(String versionCode, Long supplyId) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupInfoVO::getVersionCode, versionCode);
        whereContent.andNotEqual(PplVersionGroupInfoVO::getProduct, Ppl13weekProductTypeEnum.GPU.getName());
        ArrayList<? extends Serializable> list = Lang.list(PplVersionGroupStatusEnum.COMD_APPROVE.getCode(),
                PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), PplVersionGroupStatusEnum.PAAS_SUBMIT.getCode(),
                PplVersionGroupStatusEnum.PRE_SUBMIT.getCode());
        whereContent.andIn(PplVersionGroupInfoVO::getStatus, list);
        List<PplVersionGroupInfoVO> approveGroup = ORMUtils.db(DBList.demandDBHelper)
                .getAll(PplVersionGroupInfoVO.class, whereContent);

        List<PplVersionGroupInfoVO> comdApproveGroup = ListUtils.filter(approveGroup,
                (o) -> Strings.equals(o.getStatus(), PplVersionGroupStatusEnum.COMD_APPROVE.getCode()));
        CountDownLatch latch = new CountDownLatch(comdApproveGroup.size());
        Vector<Exception> errors = new Vector<>();
        Boolean flag = false;
        // 所有处于 stock_supply 的分组 自动进入下一步
        for (PplVersionGroupDO oneGroup : comdApproveGroup) {
            try {
                ApproveVersionGroupReq data = new ApproveVersionGroupReq();
                data.setApproveNote("管理员确认完成并通过");
                data.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
                data.setGroupId(oneGroup.getId());
                data.setSystem(true);
                data.setCurrentStatus(PplVersionGroupStatusEnum.COMD_APPROVE.getCode());
//                threadPool.execute(() -> approveVersionGroupWithOutLock(data, latch, errors));
//                // 由于下游接口不支持多线程，这里又已经做了异步处理，因此直接串行调用
                pplVersionGroupService.approveVersionGroupWithOutLock(data);
            } catch (Exception e) {
                AlarmRobotUtil.doAlarm("asyncApprove",
                        oneGroup.getIndustryDept() + oneGroup.getProduct() + "完成审批报错：" + e.getMessage(),
                        SystemUserConstant.ppl13WeekUser,
                        false);
            }
        }
//        // 25-05-27 上了物理机一张表后，不再需要调用下发接口
//        try {
//            RrpConfigDO one = rrpDBHelper.getOne(RrpConfigDO.class, "where plan_version = ?", versionCode);
//            w13ProductAcceptIndustryService.receivePythonModifyIndustryVersionSignal(
//                    Integer.parseInt(one.getId().toString()), versionCode);
//        } catch (Exception e) {
//            // 若调用失败，将flag设置为true
//            flag = true;
//            AlarmRobotUtil.doAlarm("receivePythonModifyIndustryVersionSignal",
//                    "下发物理机数据到星云失败请检查",
//                    SystemUserConstant.ppl13WeekUser,
//                    false);
//        }

        PplStockSupplyDO pplStockSupplyDO = new PplStockSupplyDO();
        pplStockSupplyDO.setId(supplyId);
        pplStockSupplyDO.setIssueStatus(flag ? 2 : 1);
        pplStockSupplyDO.setIssueTime(new Date());
        demandDBHelper.update(pplStockSupplyDO);

        // 推送供应方案给共识
        pplConsensusService.acceptStockSupplyPush(versionCode);

        // 对冲完成更新大版本事件标识
        demandDBHelper.executeRaw(
                "UPDATE unificated_version_event \n"
                        + "SET is_done = 1,\n"
                        + "deadline = ? \n"
                        + "WHERE\n"
                        + "\tdeleted = 0 \n"
                        + "\tAND event_code = ? and version_id IN (\n"
                        + "\tSELECT\n"
                        + "\t\tid \n"
                        + "\tFROM\n"
                        + "\t\tunificated_version \n"
                        + "\tWHERE\n"
                        + "\tdeleted = 0 \n"
                        + "\tAND version_code = ?)", new Date(), CrpEventEnum.STOCK_SUPPLY_DONE.getCode(), versionCode);
        dictService.eventNotice(CrpEventEnum.STOCK_SUPPLY_DONE.getCode(), null, null);
    }

    private void approveVersionGroupWithOutLock(ApproveVersionGroupReq req, CountDownLatch latch,
            Vector<Exception> errors) {
        try {
            pplVersionGroupService.approveVersionGroupWithOutLock(req);
        } catch (Exception e) {
            AlarmRobotUtil.doAlarm("asyncApprove",
                    "审批分组id: " + req.getGroupId() + " 完成审批报错：" + e.getMessage(),
                    SystemUserConstant.ppl13WeekUser,
                    false);
            errors.add(e);
            throw e;
        } finally {
            latch.countDown();
        }
    }


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(throwExceptionIfNotGetLock = true)
    public void rewriteGroupStockRsp(Long supplyId) {
        List<PplStockSupplyReqWithRspVO> all = demandDBHelper.getAll(PplStockSupplyReqWithRspVO.class,
                "where supply_id = ? "
                        + "and demand_type != ? and type = 'CVM' and ppl_id like ?",
                supplyId, PplDemandTypeEnum.RETURN.getCode(), "PG%");
        List<PplGroupStockSupplyRspDO> groupRspList = demandDBHelper.getAll(PplGroupStockSupplyRspDO.class,
                "where supply_id = ?", supplyId);
        Map<String, List<PplGroupStockSupplyRspDO>> groupPplIdToList = groupRspList.stream()
                .collect(Collectors.groupingBy(PplGroupStockSupplyRspDO::getGroupPplId));

        List<PplGroupStockSupplyRspDO> updateList = new ArrayList<>();
        List<PplGroupStockSupplyRspDO> insertList = new ArrayList<>();
        for (PplStockSupplyReqWithRspVO pplStockSupplyReqWithRspVO : all) {
            List<PplGroupStockSupplyRspDO> groupStockSupplyRspDOList = groupPplIdToList.get(
                    pplStockSupplyReqWithRspVO.getPplId());
            // 如果为空代表没有对应供应方案，如退回需求
            if (ListUtils.isEmpty(groupStockSupplyRspDOList)) {
                continue;
            }
            // 被聚合的ppl 按实例核心数 从小到大排序
            groupStockSupplyRspDOList = groupStockSupplyRspDOList.stream()
                    .sorted(Comparator.nullsLast(Comparator.comparing(
                            PplGroupStockSupplyRspDO::getChildTotalCore))).collect(Collectors.toList());

            List<PplStockSupplyRspDO> rsp = pplStockSupplyReqWithRspVO.getRsp();
            // rsp根据 matchType 进行排序，库存满足 > 库存引导 > 搬迁满足 > 库存满足 > 采购引导 > 无法满足
            // 1.库存满足
            // 2.库存引导
            // 3.搬迁满足
            // 4.库存满足
            // 5.采购引导
            // 6.无法满足
            List<PplStockSupplyRspDO> stockRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.SATISFY.getCode()));
            List<PplStockSupplyRspDO> suggestRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.SUGGEST.getCode()));
            List<PplStockSupplyRspDO> moveRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.MOVE.getCode()));
            List<PplStockSupplyRspDO> buyRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.BUY.getCode()));
            List<PplStockSupplyRspDO> suggestBuyRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getCode()));
            List<PplStockSupplyRspDO> failRsp = ListUtils.filter(rsp,
                    (o) -> o.getMatchType().equals(PplStockSupplyMatchTypeEnum.FAIL.getCode()));
            List<PplStockSupplyRspDO> sortedRsp = new ArrayList<>();
            sortedRsp.addAll(stockRsp);
            sortedRsp.addAll(suggestRsp);
            sortedRsp.addAll(moveRsp);
            sortedRsp.addAll(buyRsp);
            sortedRsp.addAll(suggestBuyRsp);
            sortedRsp.addAll(failRsp);

            Integer flag = 0;
            for (PplGroupStockSupplyRspDO pplGroupStockSupplyRspDO : groupStockSupplyRspDOList) {
                matchGroupStockRsp(sortedRsp, pplGroupStockSupplyRspDO, flag, insertList);
            }
            updateList.addAll(groupStockSupplyRspDOList);
        }

        if (ListUtils.isNotEmpty(insertList)) {
            demandDBHelper.insertBatchWithoutReturnId(insertList);
        }

        if (ListUtils.isNotEmpty(updateList)) {
            demandDBHelper.update(updateList);
        }


    }

    public void matchGroupStockRsp(List<PplStockSupplyRspDO> sortedRsp,
            PplGroupStockSupplyRspDO pplGroupStockSupplyRspDO
            , Integer flag, List<PplGroupStockSupplyRspDO> newInsertGroupList) {
        PplStockSupplyRspDO currentMatchTypeRsp = sortedRsp.get(flag);
        pplGroupStockSupplyRspDO.setReqId(currentMatchTypeRsp.getReqId());
        pplGroupStockSupplyRspDO.setRegionName(currentMatchTypeRsp.getRegionName());
        pplGroupStockSupplyRspDO.setZoneName(currentMatchTypeRsp.getZoneName());
        pplGroupStockSupplyRspDO.setCountryName(currentMatchTypeRsp.getCountryName());
        pplGroupStockSupplyRspDO.setMatchType(currentMatchTypeRsp.getMatchType());
        pplGroupStockSupplyRspDO.setMatchInstanceType(currentMatchTypeRsp.getMatchInstanceType());
        pplGroupStockSupplyRspDO.setPlanProductName(currentMatchTypeRsp.getPlanProductName());
        pplGroupStockSupplyRspDO.setMatchDemandDate(currentMatchTypeRsp.getMatchDemandDate());
        if (pplGroupStockSupplyRspDO.getChildTotalCore() < currentMatchTypeRsp.getInstanceTotalCore()) {
            // 如果被聚合ppl核心数 小于等于 满足方案核心数 直接截取
            if (currentMatchTypeRsp.getMatchType().equals(PplStockSupplyMatchTypeEnum.BUY.getCode()) ||
                    currentMatchTypeRsp.getMatchType()
                            .equals(PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getCode())) {
                Double hostUnit =
                        currentMatchTypeRsp.getInstanceNum().doubleValue() / currentMatchTypeRsp.getHostNum()
                                .doubleValue();
                pplGroupStockSupplyRspDO.setHostType(currentMatchTypeRsp.getHostType());
                // 更新物理机数
                pplGroupStockSupplyRspDO.setHostNum(
                        new BigDecimal(pplGroupStockSupplyRspDO.getChildTotalCore() / hostUnit));
            }

            // 更新实例数量
            pplGroupStockSupplyRspDO.setInstanceNum(pplGroupStockSupplyRspDO.getChildInstanceNum());
            currentMatchTypeRsp.setInstanceNum(
                    currentMatchTypeRsp.getInstanceNum() - pplGroupStockSupplyRspDO.getInstanceNum());
            // 更新总核心数
            pplGroupStockSupplyRspDO.setInstanceTotalCore(pplGroupStockSupplyRspDO.getChildTotalCore());
            currentMatchTypeRsp.setInstanceTotalCore(currentMatchTypeRsp.getInstanceTotalCore()
                    - pplGroupStockSupplyRspDO.getInstanceTotalCore());


        } else {
            // 如果被聚合ppl核心数 大于 等于 满足方案的核心数
            pplGroupStockSupplyRspDO.setInstanceNum(currentMatchTypeRsp.getInstanceNum());
            pplGroupStockSupplyRspDO.setInstanceTotalCore(currentMatchTypeRsp.getInstanceTotalCore());
            pplGroupStockSupplyRspDO.setHostType(currentMatchTypeRsp.getHostType());
            pplGroupStockSupplyRspDO.setHostNum(currentMatchTypeRsp.getHostNum());

            flag = flag + 1;
            if (!pplGroupStockSupplyRspDO.getChildTotalCore().equals(currentMatchTypeRsp.getInstanceTotalCore())) {
                // 如果大于 进行递归
                // 代表被聚合的ppl 有多种满足方式
                PplGroupStockSupplyRspDO newGroupRspDO = new PplGroupStockSupplyRspDO();
                newGroupRspDO.setGroupPplId(pplGroupStockSupplyRspDO.getGroupPplId());
                newGroupRspDO.setChildPplId(pplGroupStockSupplyRspDO.getChildPplId());
                newGroupRspDO.setChildInstanceNum(
                        pplGroupStockSupplyRspDO.getChildInstanceNum() - pplGroupStockSupplyRspDO.getInstanceNum());
                newGroupRspDO.setChildTotalCore(
                        pplGroupStockSupplyRspDO.getChildTotalCore() - pplGroupStockSupplyRspDO.getInstanceTotalCore());
                newGroupRspDO.setSupplyId(pplGroupStockSupplyRspDO.getSupplyId());
                newInsertGroupList.add(newGroupRspDO);
                matchGroupStockRsp(sortedRsp, newGroupRspDO, flag, newInsertGroupList);
            }
            // 如果等于则直接结束

        }
    }

    @Override
    @Transactional("demandTransactionManager")
    public void reSyncStockData(String versionCode) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = demandDBHelper.getOne(PplStockSupplyDO.class,
                "where version_code=? order by id desc", versionCode);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        if (!Lang.list(PplStockSupplyStatusEnum.SENT.getCode(), PplStockSupplyStatusEnum.DONE.getCode())
                .contains(stockSupply.getStatus())) {
            throw BizException.makeThrow("%s,此状态不可操作", stockSupply.getStatus());
        }

        stockSupply.setStatus(PplStockSupplyStatusEnum.SENT.getCode());
        stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.SENT.getCode());
        stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.SENT.getCode());
        demandDBHelper.update(stockSupply);

        WhereContent pplItemWhere = new WhereContent();
        pplItemWhere.andIn(PplStockSupplyReqVO::getSupplyId, stockSupply.getId());
        List<PplStockSupplyReqVO> all = ORMUtils.db(demandDBHelper).getAll(PplStockSupplyReqVO.class, pplItemWhere);

        List<PplStockSupplyRspDO> listStream = all.stream().map(PplStockSupplyReqVO::getPplStockSupplyRsp)
                .flatMap(Collection::stream).collect(Collectors.toList());
        demandDBHelper.delete(listStream);
    }


    @Override
    @Transactional("demandTransactionManager")
    public void syncCbsStockData(Long stockId) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = getStockSupply(stockId, true);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        if (!Strings.equals(stockSupply.getCbsTaskStatus(), PplStockSupplyStatusEnum.SENT.getCode()) && !Strings.equals(
                stockSupply.getCbsTaskStatus(), PplStockSupplyStatusEnum.FAIL.getCode())) {
            return;
        }

        DescribeCbsStockPlanTaskReq req = new DescribeCbsStockPlanTaskReq();
        req.setTaskId(stockSupply.getCbsTaskId());

        DescribeCbsStockPlanTaskRsp ret = null;
        try {

            ret = qcbsApiService.describeCbsStockPlanTask(req);
        } catch (Exception e) {
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCbsErrorMsg("同步数据异常： exception：" + e.getMessage());
            demandDBHelper.update(stockSupply);
            return;
        }

        DescribeCbsStockPlanTaskRsp.Response response = ret.getResponse();
        if (response == null) {
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCbsErrorMsg(" CBS 对冲结果返回空值");
            demandDBHelper.update(stockSupply);
            return;
        }

        if (response.getError() != null) {
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCbsErrorMsg(" CBS 对冲结果返回错误： " + JSON.toJson(response.getError()));
            demandDBHelper.update(stockSupply);
            return;
        }

        List<Result> result = response.getResult();
        String status = response.getStatus();

        if (Strings.equals("FAILED", status)) {
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCbsErrorMsg(" CBS 对冲结果返回失败。");
            demandDBHelper.update(stockSupply);
            return;
        }

        if (!Strings.equals("SUCCESS", status)) { // 其它状态继续轮训
            setAppendInfo(stockSupply, "cbs not ready,status:" + status);
            demandDBHelper.update(stockSupply);
//            log.info("cbs not ready start, status {}", status);
            return;
        }

        if (Lang.isNotEmpty(result)) {
            try {
                WhereContent whereContent = new WhereContent();
                whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, stockId);
                whereContent.andEqual(PplStockSupplyReqDO::getType, "CBS");
                List<PplStockSupplyReqDO> allReq = demandDb().getAll(PplStockSupplyReqDO.class, whereContent);

                Map<String, List<PplStockSupplyReqDO>> pplIdGroupBy = ListUtils.groupBy(allReq,
                        PplStockSupplyReqDO::getPplId);

                // 去关联资源中台的国内外表
                Map<String, String> landMap = shuttleDBHelper.getRaw(Map.class,
                                "select r.en, ifnull(a.domes_foreig, '未知') land\n" + "from region r\n"
                                        + "left join area a on r.area_id = a.id").stream()
                        .collect(Collectors.toMap(e -> (String) e.get("en"), e -> (String) e.get("land"), (e1, e2) -> e1));

                Map<String, StaticZoneDO> zone2DOMap = dictService.getZone2DOMap();

                for (Result result1 : result) {
                    PplStockSupplyRspDO pplStockSupplyRspDO = transFrom(result1);
                    pplStockSupplyRspDO.setSupplyId(stockId);
                    List<PplStockSupplyReqDO> pplStockSupplyReqDOS = pplIdGroupBy.get(result1.getLabel());

                    List<PplStockSupplyReqDO> filterByDiskType = ListUtils.filter(pplStockSupplyReqDOS,
                            (o) -> Strings.equals(o.getDiskType(), result1.getDiskType()));
                    if (Lang.isNotEmpty(filterByDiskType)) {
                        pplStockSupplyRspDO.setReqId(filterByDiskType.get(0).getId());
                    }

                    pplStockSupplyRspDO.setPlanProductName("腾讯云CBS");

                    pplStockSupplyRspDO.setDiskType(result1.getDiskType());
                    pplStockSupplyRspDO.setDiskTypeName(PplDiskTypeEnum.getNameByCode(result1.getDiskType()));
                    pplStockSupplyRspDO.setMatchDiskTypeName(PplDiskTypeEnum.getNameByCode(result1.getMatchDiskType()));

                    setRegionInfo(pplStockSupplyRspDO,landMap,zone2DOMap);

                    //todo map info
                    demandDBHelper.insert(pplStockSupplyRspDO);
                }

            } catch (Exception e) {
                log.info("cbs deal error");
                e.printStackTrace();
                stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.ERROR.getCode());
                stockSupply.setCbsErrorMsg("插入 CBS 处理失败： " + e.getMessage());
                demandDBHelper.update(stockSupply);
                return;
            }
        }

        stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.DONE.getCode());
        demandDBHelper.update(stockSupply);

    }

    private void setAppendInfo(PplStockSupplyDO supplyDO, String msg) {
        String rotationTimeAppend = supplyDO.getRotationTimeAppend();
        rotationTimeAppend = rotationTimeAppend == null ? "" : rotationTimeAppend;
        rotationTimeAppend += DateUtils.format(new Date()) + ":" + msg + "; \n";
        if (rotationTimeAppend.length() > 16777000) {
            rotationTimeAppend = rotationTimeAppend.substring(rotationTimeAppend.length() - 100000);
        }
        supplyDO.setRotationTimeAppend(rotationTimeAppend);
    }

    private PplStockSupplyRspDO transFrom(Result source) {
        PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
        pplStockSupplyRspDO.setRegion(source.getRegion());
        pplStockSupplyRspDO.setZone(source.getZone());
//        pplStockSupplyRspDO.setSupplyId(); over
//        pplStockSupplyRspDO.setReqId(); over
//        pplStockSupplyRspDO.setRegionName(); over
//        pplStockSupplyRspDO.setZoneName(); over

        pplStockSupplyRspDO.setRemark(source.getRemark());

        PplStockSupplyMatchTypeEnum cbs = PplStockSupplyMatchTypeEnum.getCbs(source.getMatchType());
        if (cbs != null) {
            pplStockSupplyRspDO.setMatchType(cbs.getCode());
        } else {
            pplStockSupplyRspDO.setMatchType(source.getMatchType());
        }

        pplStockSupplyRspDO.setMatchInstanceType(source.getMatchDiskType());
        pplStockSupplyRspDO.setMatchDiskType(source.getMatchDiskType());
        pplStockSupplyRspDO.setHostType(source.getHostType());
        pplStockSupplyRspDO.setHostNum(source.getHostCount());
//        pplStockSupplyRspDO.setInstanceNum();
//        pplStockSupplyRspDO.setInstanceTotalCore();
        pplStockSupplyRspDO.setDiskTotalSize(source.getDiskTotalSize());
//        pplStockSupplyRspDO.setCountryName(); over
//        pplStockSupplyRspDO.setPlanProductName(); over
        return pplStockSupplyRspDO;
    }


    @Override
    public FileNameAndBytesDTO downloadCvmSupply(Long supplyGpuId) {

        InputStream templateIn = IOUtils.readClasspathResourceInputStream(
                "excel/ppl13week/ppl13week_cvm_supply_import.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.write(Lang.list(), writeSheet).finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                "CVM-需求对冲空白模版" + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx");

        return fileNameAndBytesDTO;
    }

    @Override
    @Transactional("demandTransactionManager")
    public Object uploadAndSaveCvmSupply(MultipartFile file, Map<String, String> params) throws IOException {

        String versionCode = getVersionCode(params);
        // 这里可能有并发危险
        PplStockSupplyDO pplStockSupplyDO = demandDBHelper.getOne(PplStockSupplyDO.class,
                "where version_code=? order by id desc", versionCode);

        if (pplStockSupplyDO == null || !Strings.equals(pplStockSupplyDO.getStatus(),
                PplStockSupplyStatusEnum.DONE.getCode())) {
            throw BizException.makeThrow("%s,此状态不可操作", pplStockSupplyDO.getStatus());
        }

        PplVersionDO versionDO = pplVersionService.getByVersionCode(versionCode);
        LocalDate versionBeginDate = versionDO == null ? LocalDate.now()
                : LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);

        List<IndustryDemandRegionZoneInstanceTypeDictDO> zoneInfos = industryDemandDictService.listAllZones(null);
        List<String> zones = zoneInfos.stream().map(IndustryDemandRegionZoneInstanceTypeDictDO::getZoneName)
                .collect(Collectors.toList());
        List<String> stockMatchTypeNames = Arrays.stream(PplStockSupplyMatchTypeEnum.values())
                .map(PplStockSupplyMatchTypeEnum::getName).collect(Collectors.toList());


        Map<String, PplStockSupplyReqDO> dbItemsGroup = getDbItems(pplStockSupplyDO.getId());

        ExcelRet<Ppl13weekCvmSupplyImportExcelDTO> excelRet = ExcelBase.config(Ppl13weekCvmSupplyImportExcelDTO.class)
                .setMultipartFile(file).trimAllString(true).setRowOffset(2)
                // 检查空值
                .checkValueNotEmpty(Ppl13weekCvmSupplyImportExcelDTO::getPplId).checkAndRetError((o) -> {
                    if (Strings.equals(o.getMatchTypeName(), PplStockSupplyMatchTypeEnum.BUY.getName())
                            || Strings.equals(o.getMatchTypeName(),
                            PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getName())) {
                        if (!erpBaseService.getBasStrategeDeviceTypeMap().containsKey(o.getHostType())) {
                            return o.makeErrorIfNotContain(Ppl13weekCvmSupplyImportExcelDTO::getHostType,
                                    erpBaseService.getBasStrategeDeviceTypeMap().keySet());
                        }
                    }
                    return null;
                })
                // 检查值是否在一个范围内
                .checkValueIn(Ppl13weekCvmSupplyImportExcelDTO::getMatchTypeName, stockMatchTypeNames)
                //  其它检查
                .checkAndRetError((o) -> {
                    // 如果满足方式=采购满足，采购引导，必须填写母机机型，母机数量，其他满足方式不允许填写母机机型、母机数量
                    if (PplStockSupplyMatchTypeEnum.BUY.getName().equals(o.getMatchTypeName())
                            || PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getName().equals(o.getMatchTypeName())) {
                        if (o.getHostNum() == null || o.getHostNum().intValue() == 0) {
                            return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getHostNum,
                                    "采购满足，采购引导，必须填写母机机型，母机数量");
                        }
                        if (StringUtils.isBlank(o.getHostType())) {
                            return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getHostType,
                                    "采购满足，采购引导，必须填写母机机型，母机数量");
                        }
                    }
                    if ((!PplStockSupplyMatchTypeEnum.BUY.getName().equals(o.getMatchTypeName())
                            && !PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getName().equals(o.getMatchTypeName()))
                            && o.getHostNum() != null && o.getHostNum().intValue() != 0) {
                        return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getHostNum,
                                "非采购满足、采购引导方式，无法设置满足母机台数");

                    }
                    return null;
                })
                .checkAndRetError((o) -> {
                    if (NumberUtils.parseBigDecimal(o.getCvmCore()) == null) {
                        return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getCvmCore, "解析数值出错");
                    }
                    return null;
                }).checkAndRetError((o) -> {
                    //2023-06-15 不校验可用区
                    List<String> models = pplDictService.queryInstanceModel("");
                    return o.makeErrorIfNotContain(Ppl13weekCvmSupplyImportExcelDTO::getInstanceModel, models,
                            String.format("%s 可用区不可填写此实例规格, 可选范围【%s】", o.getZoneName(),
                                    Strings.join(",", models)));
                }).checkAndRetError((o) -> {
                    if (o.getMatchDemandDate() == null) {
                        return null;
                    }
                    if (o.getMatchDemandDate().isBefore(versionBeginDate)) {
                        return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getMatchDemandDate,
                                "需求满足日期【" + o.getMatchDemandDate()
                                        + "】不能在版本需求开始日期【" + versionBeginDate + "】之前");
                    }
                    return null;
                }).checkAndRetError((o) -> {
                    PplStockSupplyReqDO reqItem = dbItemsGroup.get(o.getPplId());
                    if (reqItem == null) {
                        return null;
                    }
                    boolean sameModel = Objects.equals(o.getInstanceModel(), reqItem.getInstanceModel());
                    boolean sameZone = Objects.equals(o.getZoneName(), reqItem.getZoneName());
//                    if (!sameModel || !sameZone) {
//                        // 满足资源与需求资源不一致时，必须为 引导满足
//                        if (!PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getName().equals(o.getMatchTypeName())
//                                && !PplStockSupplyMatchTypeEnum.SUGGEST.getName() .equals(o.getMatchTypeName())) {
//                            String diff = "";
//                            if (!sameModel) {
//                                diff = diff + StrUtil.format("需求实例规格【{}】，满足实例规格【{}】。",
//                                        reqItem.getInstanceModel(), o.getInstanceModel());
//                            }
//                            if (!sameZone) {
//                                diff = diff + StrUtil.format("需求可用区【{}】，满足可用区【{}】。",
//                                        reqItem.getZoneName(), o.getZoneName());
//                            }
//                            String msg = StrUtil.format("满足资源与需求资源不一致，不能使用【{}】。{}",
//                                    o.getMatchTypeName(), diff);
//                            return o.makeError(Ppl13weekCvmSupplyImportExcelDTO::getMatchTypeName, msg);
//                        }
//                    }
                    return null;
                }).checkAndAddError(
                        (ColErrorList errorList, Ppl13weekCvmSupplyImportExcelDTO oneData) -> checkExcelData(zones,
                                stockMatchTypeNames, errorList, oneData)).finish();

        List<Ppl13weekCvmSupplyImportExcelDTO> inputData = excelRet.getDataList();
        ErrorList errors = excelRet.getErrorList();

        //ppl-id 聚合求和看看和是不是相同的
        Map<String, List<Ppl13weekCvmSupplyImportExcelDTO>> excelPplIdGroup = ListUtils.groupBy(inputData,
                Ppl13weekCvmSupplyImportExcelDTO::getPplId);

        for (String key : excelPplIdGroup.keySet()) {
//            if (key.startsWith("PE")) {
//                continue;
//            }
            List<Ppl13weekCvmSupplyImportExcelDTO> oneInput = excelPplIdGroup.get(key);
            PplStockSupplyReqDO reqDO = dbItemsGroup.get(key);
            if (reqDO == null) {
                ErrorMessage errorMessage = oneInput.get(0).makeError(null, Ppl13weekCvmSupplyImportExcelDTO::getPplId,
                        String.format("(pplId:%s) 不在本次版本对冲范围内【%s", key,
                                versionCode));
                errors.add(errorMessage);
            } else {
                if (reqDO.getDataSource().equals(PplStockSupplyDataSourceEnum.ORDER_BUY.getCode())){
                    ErrorMessage errorMessage = oneInput.get(0)
                            .makeError(null, Ppl13weekCvmSupplyImportExcelDTO::getPplId,
                                    String.format("不能修正数据来源为 订单已锁定采购 的ppl-id: %s", key));
                    errors.add(errorMessage);
                }
                Integer totalCore = reqDO.getInstanceTotalCore();
                totalCore = totalCore == null ? 0 : totalCore;
                int sum = NumberUtils.sum(oneInput, Ppl13weekCvmSupplyImportExcelDTO::getCvmCore).intValue();
                if (sum != totalCore) {
                    ErrorMessage errorMessage = oneInput.get(0)
                            .makeError(null, Ppl13weekCvmSupplyImportExcelDTO::getPplId,
                                    String.format("(pplId:%s) 的(需求数:%d) 不等于 (满足数量:%d)", key, totalCore,
                                            sum));
                    errors.add(errorMessage);
                }
                for (Ppl13weekCvmSupplyImportExcelDTO o : oneInput) {
                    if (Objects.equals(o.getMatchTypeName(), PplStockSupplyMatchTypeEnum.SUGGEST.getName())
                            || Objects.equals(o.getMatchTypeName(),
                            PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getName())) {
                        // 如果满足方式=库存引导，采购引导；机型、可用区、时间至少存在一个不一样
                        // 输入的满足需求日期和ppl的需求日期是否相同
                        boolean sameDemandDateFlag = o.getMatchDemandDate() == null
                                || o.getMatchDemandDate().equals(reqDO.getBeginBuyDate());
                        if (sameDemandDateFlag && Objects.equals(o.getInstanceModel(), reqDO.getInstanceModel())
                                && Objects.equals(o.getZoneName(), reqDO.getZoneName())) {
                            ErrorMessage errorMessage = oneInput.get(0)
                                    .makeError(null, Ppl13weekCvmSupplyImportExcelDTO::getPplId,
                                            StrUtil.format("(pplId:{}) 库存引导或采购引导时，"
                                                            + "满足可用区【{}】、满足实例规格【{}】、"
                                                            + "满足需求日期字段至少有一项与原可用区【{}】、原实例规格【{}】、"
                                                            + "原需求日期【{}】不同",
                                                    key, o.getZoneName(), o.getInstanceModel(), reqDO.getZoneName(),
                                                    reqDO.getInstanceModel(), reqDO.getBeginBuyDate()));
                            errors.add(errorMessage);
                        }
                    }
                }
            }
        }
        if (Lang.isNotEmpty(errors)) {
            return ImmutableMap.of("errorMsg", errors);
        }
        delAndSaveNew(pplStockSupplyDO, zoneInfos, excelPplIdGroup);
        return ImmutableMap.of("errorMsg", Lang.list(), "data", "success");
    }

    private void delAndSaveNew(PplStockSupplyDO pplStockSupplyDO,
            List<IndustryDemandRegionZoneInstanceTypeDictDO> zoneInfos,
            Map<String, List<Ppl13weekCvmSupplyImportExcelDTO>> excelPplIdGroup) {

        List<String> pplIds = new ArrayList<>(excelPplIdGroup.keySet());
        WhereContent pplItemWhere = new WhereContent();
        pplItemWhere.andIn(PplStockSupplyReqVO::getPplId, pplIds);
        pplItemWhere.andIn(PplStockSupplyReqVO::getSupplyId, pplStockSupplyDO.getId());
        pplItemWhere.andEqual(PplStockSupplyReqDO::getType, "CVM");
        List<PplStockSupplyReqVO> all = ORMUtils.db(demandDBHelper).getAll(PplStockSupplyReqVO.class, pplItemWhere);

        List<PplStockSupplyRspDO> insert = Lang.list();
        List<PplStockSupplyRspDO> del = Lang.list();

        // 去关联资源中台的国内外表
        Map<String, String> landMap = shuttleDBHelper.getRaw(Map.class,
                        "select r.en, ifnull(a.domes_foreig, '未知') land\n" + "from region r\n"
                                + "left join area a on r.area_id = a.id").stream()
                .collect(Collectors.toMap(e -> (String) e.get("en"), e -> (String) e.get("land"), (e1, e2) -> e1));
        for (PplStockSupplyReqVO pplStockSupplyReqVO : all) {

            List<PplStockSupplyRspDO> pplStockSupplyRsp = pplStockSupplyReqVO.getPplStockSupplyRsp();
            del.addAll(pplStockSupplyRsp);

            List<Ppl13weekCvmSupplyImportExcelDTO> excelDto = excelPplIdGroup.get(pplStockSupplyReqVO.getPplId());

            for (Ppl13weekCvmSupplyImportExcelDTO ppl13weekCvmSupplyImportExcelDTO : excelDto) {
                PplStockSupplyRspDO trans = trans(ppl13weekCvmSupplyImportExcelDTO);
                if (trans.getMatchDemandDate() == null) {
                    // 没有给满足日期时，默认就是开始购买日期
                    trans.setMatchDemandDate(pplStockSupplyReqVO.getBeginBuyDate());
                }
                insert.add(trans);
                trans.setSupplyId(pplStockSupplyReqVO.getSupplyId());
                trans.setReqId(pplStockSupplyReqVO.getId());
                List<IndustryDemandRegionZoneInstanceTypeDictDO> zone = ListUtils.filter(zoneInfos,
                        (o) -> Strings.equals(o.getZoneName(), trans.getZoneName()));
                if (Lang.isNotEmpty(zone)) {
                    IndustryDemandRegionZoneInstanceTypeDictDO zoneDTO = zone.get(0);
                    trans.setRegion(zoneDTO.getRegionStrId());
                    trans.setRegionName(zoneDTO.getRegionShortChName());
                    trans.setZone(zoneDTO.getZoneStrId());
                    trans.setCountryName(landMap.getOrDefault(zoneDTO.getRegionStrId(), "未知"));
                }
            }
        }
        demandDBHelper.delete(del);
        demandDBHelper.insertBatchWithoutReturnId(insert);
    }

    PplStockSupplyRspDO trans(Ppl13weekCvmSupplyImportExcelDTO source) {
        PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
        pplStockSupplyRspDO.setZoneName(source.getZoneName());
        pplStockSupplyRspDO.setMatchType(PplStockSupplyMatchTypeEnum.getCodeByName(source.getMatchTypeName()));
        pplStockSupplyRspDO.setMatchInstanceType(source.getInstanceModel());
        pplStockSupplyRspDO.setHostType(source.getHostType());
        pplStockSupplyRspDO.setHostNum(source.getHostNum());
        Integer integer = P2PInstanceModelParse.parseInstanceModel(source.getInstanceModel())._1;
        if (integer == null || integer <= 0) {
            throw BizException.makeThrow("实力规格%s核心数解析出错", source.getInstanceModel());
        }
        pplStockSupplyRspDO.setInstanceNum(source.getCvmCore() / integer);
        pplStockSupplyRspDO.setInstanceTotalCore(source.getCvmCore());
        pplStockSupplyRspDO.setPlanProductName("腾讯云CVM");
        pplStockSupplyRspDO.setRemark(source.getRemark());
        pplStockSupplyRspDO.setMatchDemandDate(source.getMatchDemandDate());
        return pplStockSupplyRspDO;
    }


    private Map<String, PplStockSupplyReqDO> getDbItems(Long supplyId) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, supplyId);
        whereContent.andEqual(PplStockSupplyReqDO::getType, YunxiaoAppRoleEnum.CVM.getCode());
        List<PplStockSupplyReqDO> all = demandDBHelper.getAll(PplStockSupplyReqDO.class, whereContent.getSql(),
                whereContent.getParams());
        return ListUtils.toMap(all, PplStockSupplyReqDO::getPplId, v -> v);
    }

    @Resource
    ErpBaseService erpBaseService;

    @NotNull
    private String getVersionCode(Map<String, String> params) {
        if (params == null || params.get("params") == null) {
            throw new BizException("参数为空");
        }

        Map<?, ?> req = JSON.parse(params.get("params"), Map.class);
        String versionCode = (String) req.get("versionCode");
        if (Strings.isBlank(versionCode)) {
            throw new BizException("versionCode为空");
        }
        return versionCode;
    }

    private void checkExcelData(List<String> zones, List<String> stockMatchTypeNames, ColErrorList errorList,
            Ppl13weekCvmSupplyImportExcelDTO oneData) {
        ColErrorMessage err;

        err = oneData.makeErrorIfNotContain(Ppl13weekCvmSupplyImportExcelDTO::getMatchTypeName, stockMatchTypeNames);
        errorList.add(err);

        err = oneData.makeErrorIfNotContain(Ppl13weekCvmSupplyImportExcelDTO::getZoneName, zones);
        errorList.add(err);

        //   满足方式=采购满足时，满足母机机型、母机台数、原因不能为空
        if ("采购满足".equals(oneData.getMatchTypeName())) {

            err = oneData.makeErrorIfBlank(Ppl13weekCvmSupplyImportExcelDTO::getHostType);
            errorList.add(err);

            if (NumberUtils.parseBigDecimal(oneData.getHostNum()) == null) {
                err = oneData.makeError(Ppl13weekCvmSupplyImportExcelDTO::getHostNum,
                        "采购满足时，满足的母机台数不能为空");
                errorList.add(err);
            }

        }
    }


    @Override
    @Transactional("demandTransactionManager")
    public void syncCvmStockData(Long stockId) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = getStockSupply(stockId, true);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        if (!Strings.equals(stockSupply.getCvmTaskStatus(), PplStockSupplyStatusEnum.SENT.getCode()) && !Strings.equals(
                stockSupply.getCvmTaskStatus(), PplStockSupplyStatusEnum.FAIL.getCode())) {
            return;
        }

        QueryStockSupplyResultRsp ret = null;
        try {
            ret = yunxiaoAPIService.queryStockSupplyResult(stockSupply.getCvmTaskId());
        } catch (Exception e) {
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCvmErrorMsg("同步数据异常： exception：" + e.getMessage());
            demandDBHelper.update(stockSupply);
            return;
        }

        QueryStockSupplyResultRsp.Data data = ret.getData();
        if (data == null) {
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCvmErrorMsg(" CVM 对冲结果返回空值: data = null");
            demandDBHelper.update(stockSupply);
            return;
        }

        String retStatus = data.getStatus();
        if (Strings.isBlank(retStatus)) {
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCvmErrorMsg(" CVM 对冲结果返回空值: retStatus = null");
            demandDBHelper.update(stockSupply);
            return;
        }

        if (!Strings.equals("COMPLETED", retStatus)) { // 其它状态继续轮训
            setAppendInfo(stockSupply, "cvm not ready,retStatus:" + retStatus);
            demandDBHelper.update(stockSupply);

//            log.info("cvm not ready, status: {}", retStatus);
            return;
        }

        List<QueryStockSupplyResultRsp.Result> results = data.getResult();
        if (Lang.isEmpty(results)) {
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCvmErrorMsg(" CVM 对冲结果返回空值: results = null");
            demandDBHelper.update(stockSupply);
            return;
        }

        if (Lang.isNotEmpty(results)) {
            try {

                // 去关联资源中台的国内外表
                Map<String, String> landMap = shuttleDBHelper.getRaw(Map.class,
                                "select r.en, ifnull(a.domes_foreig, '未知') land\n" + "from region r\n"
                                        + "left join area a on r.area_id = a.id").stream()
                        .collect(Collectors.toMap(e -> (String) e.get("en"), e -> (String) e.get("land"), (e1, e2) -> e1));

                Map<String, StaticZoneDO> zoneNameMap = dictService.queryZoneName2StaticZone();
                Map<String, StaticZoneDO> zone2DOMap = dictService.getZone2DOMap();
                WhereContent whereContent = new WhereContent();
                whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, stockId);
                whereContent.andEqual(PplStockSupplyReqDO::getType, "CVM");
                List<PplStockSupplyReqDO> allReq = demandDb().getAll(PplStockSupplyReqDO.class, whereContent);

                Map<String, List<PplStockSupplyReqDO>> pplIdGroupBy = ListUtils.groupBy(allReq,
                        PplStockSupplyReqDO::getPplId);
                List<IdcRiskZoneDO> idcRiskZoneDOS = pplDictService.queryAvailableIdcRiskZone(true);

                List<PplStockSupplyRspDO> insertList = new ArrayList<>();
                for (QueryStockSupplyResultRsp.Result result : results) {
                    PplStockSupplyRspDO pplStockSupplyRspDO = transFrom(result);
                    pplStockSupplyRspDO.setSupplyId(stockId);
                    List<PplStockSupplyReqDO> pplStockSupplyReqDOS = pplIdGroupBy.get(result.getLabel());
                    if (Lang.isNotEmpty(pplStockSupplyReqDOS)) {
                        PplStockSupplyReqDO first = pplStockSupplyReqDOS.get(0);
                        if (pplStockSupplyRspDO.getMatchDemandDate() == null) {
                            // 没有给满足日期时，默认就是开始购买日期
                            pplStockSupplyRspDO.setMatchDemandDate(first.getBeginBuyDate());
                        }
                        pplStockSupplyRspDO.setReqId(first.getId());
                        Integer matchCount = result.getMatchCount();
                        matchCount = matchCount == null ? 0 : matchCount;
                        Tuple2<Integer, Integer> modelInfo = P2PInstanceModelParse.parseInstanceModel(
                                pplStockSupplyReqDOS.get(0).getInstanceModel());
                        pplStockSupplyRspDO.setInstanceTotalCore(modelInfo._1 * matchCount);

                        // 检查总数是否对的上
                        Integer demandInstanceNum = pplStockSupplyReqDOS.get(0).getInstanceNum();
                        List<QueryStockSupplyResultRsp.Result> allSameLabelResult = ListUtils.filter(results,
                                (o) -> Strings.equals(o.getLabel(), result.getLabel()));
                        BigDecimal allSameInstanceNum = ListUtils.sum(allSameLabelResult,
                                QueryStockSupplyResultRsp.Result::getMatchCount);
                        demandInstanceNum = demandInstanceNum == null ? 0 : demandInstanceNum;
                        if (allSameInstanceNum.intValue() != demandInstanceNum) {
                            String tmp = String.format("总匹配实例数不等于需求数: (需求数-%d,总匹配数-%d",
                                    demandInstanceNum,
                                    allSameInstanceNum.intValue());
                            setCvmRemark(pplStockSupplyRspDO, tmp);
                            pplStockSupplyRspDO.setRemark(tmp);
                        }
                    }
                    pplStockSupplyRspDO.setPlanProductName("腾讯云CVM");
                    setCvmRemark(pplStockSupplyRspDO, result.getRemark());
                    setRegionInfo(pplStockSupplyRspDO,landMap,zone2DOMap);
                    adjustMatchZone(pplStockSupplyRspDO,idcRiskZoneDOS,zoneNameMap,landMap);
                    insertList.add(pplStockSupplyRspDO);
                }
                demandDBHelper.insertBatchWithoutReturnId(insertList);

            } catch (Exception e) {
                stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.ERROR.getCode());
                stockSupply.setCvmErrorMsg("处理错误： " + e.getMessage());
                demandDBHelper.update(stockSupply);
                return;
            }
        }

        stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.DONE.getCode());
        callBackOnSupply(stockSupply);
        demandDBHelper.update(stockSupply);

    }

    public void adjustMatchZone(PplStockSupplyRspDO pplStockSupplyRspDO,
            List<IdcRiskZoneDO> idcRiskZoneDOS,Map<String, StaticZoneDO> zoneNameMap,
            Map<String, String> landMap){

        // 过滤出当前在风险园区 且 在风险周期范围内 的
        List<IdcRiskZoneDO> list = idcRiskZoneDOS.stream().filter(v ->
                v.getCurrAvailableCloudZone().equals(pplStockSupplyRspDO.getZoneName())
                        && cloud.demand.app.common.utils.DateUtils.localDateIsGreaterThanOrEqualTo
                        (pplStockSupplyRspDO.getMatchDemandDate(), v.getRiskStartDate())
                        && cloud.demand.app.common.utils.DateUtils.localDateIsLessThanOrEqualTo
                        (pplStockSupplyRspDO.getMatchDemandDate(), v.getRiskEndDate())).collect(Collectors.toList());


        if (ListUtils.isNotEmpty(list)){
            IdcRiskZoneDO idcRiskZoneDO = list.get(0);
            pplStockSupplyRspDO.setZoneName(idcRiskZoneDO.getSuggestAvailableCloudZone());
            pplStockSupplyRspDO.setMatchType(
                    PplStockSupplyMatchTypeEnum.BUY.getCode().equals(pplStockSupplyRspDO.getMatchType()) ?
                            PplStockSupplyMatchTypeEnum.SUGGEST_BUY.getCode() : PplStockSupplyMatchTypeEnum.SUGGEST.getCode());
            StaticZoneDO staticZoneDO = zoneNameMap.get(pplStockSupplyRspDO.getZoneName());
            if (staticZoneDO != null){
                pplStockSupplyRspDO.setZone(staticZoneDO.getZone());
                pplStockSupplyRspDO.setRegion(staticZoneDO.getApiRegion());
                pplStockSupplyRspDO.setRegionName(staticZoneDO.getRegionName());
                String country = landMap.getOrDefault(pplStockSupplyRspDO.getRegion(), "未知");
                pplStockSupplyRspDO.setCountryName(country);
            }
        }

    }

    @Override
    @Transactional("demandTransactionManager")
    public Boolean syncCvmLockBuyData(Long stockId) {
        List<PplStockSupplyReqDO> all = demandDBHelper.getAll(PplStockSupplyReqDO.class,
                " where supply_id = ? and data_source = ?",
                stockId, PplStockSupplyDataSourceEnum.ORDER_BUY.getCode());
        if (ListUtils.isEmpty(all)){
            // 如果没有订单已采购数据 返回已执行完成
            return Boolean.TRUE;
        }
        List<Long> reqIds = ListUtils.transform(all, PplStockSupplyReqDO::getId);
        PplStockSupplyRspDO one = demandDBHelper.getOne(PplStockSupplyRspDO.class, "where req_id in (?)", reqIds);
        if (one != null){
            // 如果数据已生成 返回已执行完成
            return Boolean.TRUE;
        }

        // 1.获取采购信息 获取供应方案
        Map<String,PplStockSupplyReqDO> orderNumberToReq = all.stream().collect(Collectors.toMap(PplStockSupplyReqDO::getOrderNumber,v->v,(v1,v2) -> v1));

        List<OrderSupplyPlanDetailDO> buySupplyDetail = supplyPlanQueryService.querySupplyPlanDetailByOrderNumber(
                PplStockSupplyMatchTypeEnum.BUY.getCode(), new ArrayList<>(orderNumberToReq.keySet()));

        List<PplStockSupplyRspDO> rspList = new ArrayList<>();
        // 2. 没有Q单的数据 这部分数据去映射物理机
        List<OrderSupplyPlanDetailDO> notMatchQuotaNumberList = buySupplyDetail.stream()
                .filter(v -> StringUtils.isBlank(v.getSupplyBizId())).collect(Collectors.toList());
        Map<String, ObsBudgetRollAdjustCvmAppendDeviceDO> cvm2Device = sopUtilCommonService.getCvm2DeviceOriginalRate();
        for (OrderSupplyPlanDetailDO orderSupplyPlanDetailDO : notMatchQuotaNumberList) {
            ObsBudgetRollAdjustCvmAppendDeviceDO obsDeviceDO = cvm2Device.get(
                    orderSupplyPlanDetailDO.getSupplyInstanceModel());
            PplStockSupplyReqDO reqDO = orderNumberToReq.get(orderSupplyPlanDetailDO.getOrderNumber());
            if (obsDeviceDO == null || obsDeviceDO.getPhysicalServerToTransformInstanceNum() == 0 || reqDO == null) {
                continue;
            }
            PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
            pplStockSupplyRspDO.setSupplyId(stockId);
            pplStockSupplyRspDO.setMatchType(PplStockSupplyMatchTypeEnum.BUY.getCode());
            pplStockSupplyRspDO.setPlanProductName("腾讯云CVM");
            pplStockSupplyRspDO.setReqId(reqDO.getId());

            pplStockSupplyRspDO.setRegion(orderSupplyPlanDetailDO.getSupplyRegion());
            pplStockSupplyRspDO.setRegionName(orderSupplyPlanDetailDO.getSupplyRegionName());
            pplStockSupplyRspDO.setZone(orderSupplyPlanDetailDO.getSupplyZone());
            pplStockSupplyRspDO.setZoneName(orderSupplyPlanDetailDO.getSupplyZoneName());
            pplStockSupplyRspDO.setInstanceTotalCore(orderSupplyPlanDetailDO.getSupplyCoreNum());
            pplStockSupplyRspDO.setInstanceNum(orderSupplyPlanDetailDO.getSupplyInstanceNum());
            pplStockSupplyRspDO.setMatchInstanceType(orderSupplyPlanDetailDO.getSupplyInstanceType());

            pplStockSupplyRspDO.setMatchDemandDate(reqDO.getBeginBuyDate());
            pplStockSupplyRspDO.setHostType(obsDeviceDO.getToType());
            // 物理机数量 = 需要供应实例数 / 单物理机可供应实例数
            Integer hostNum = orderSupplyPlanDetailDO.getSupplyInstanceNum() / obsDeviceDO.getPhysicalServerToTransformInstanceNum() +
                    (orderSupplyPlanDetailDO.getSupplyInstanceNum() % obsDeviceDO.getPhysicalServerToTransformInstanceNum() > 0 ? 1 : 0);
            pplStockSupplyRspDO.setHostNum(BigDecimal.valueOf(hostNum));
            pplStockSupplyRspDO.setRemark("【系统生成】： 订单: " + orderSupplyPlanDetailDO.getOrderNumber() + " 该采购供应方案尚未设置Q单,因此母机类型和数量，由系统转换生成");
            rspList.add(pplStockSupplyRspDO);

        }

        // 3. 有Q单的数据， 这部分用Q单的采购数据
        List<OrderSupplyPlanDetailDO> MatchQuotaNumberList = buySupplyDetail.stream()
                .filter(v -> StringUtils.isNotBlank(v.getSupplyBizId())).collect(Collectors.toList());
        List<String> numberList = MatchQuotaNumberList.stream().map(v -> v.getSupplyBizId().split(";"))
                .flatMap(Arrays::stream).distinct().collect(Collectors.toList());

        // 屏蔽主单号和子单号的差异直接查出所有Q单
        List<DeviceApplyDO> deviceApplyDOList = getQuotaNumberList(numberList);
        Map<String, DeviceApplyDO> childNumberToDeviceApply = deviceApplyDOList.stream()
                .collect(Collectors.toMap(DeviceApplyDO::getSubId, v -> v));
        for (OrderSupplyPlanDetailDO orderSupplyPlanDetailDO : MatchQuotaNumberList) {
            PplStockSupplyReqDO reqDO = orderNumberToReq.get(orderSupplyPlanDetailDO.getOrderNumber());
            if (reqDO == null) {
                continue;
            }

            List<String> quotaNumberList =
                    orderSupplyPlanDetailDO.getSupplyBizType() == 1
                            ? Arrays.asList(orderSupplyPlanDetailDO.getSupplyBizId())
                            : shuttleDBHelper.getRaw(String.class, "select sub_id from device_apply where order_type = 1 and id = ?",
                                    orderSupplyPlanDetailDO.getSupplyBizId());

            if (quotaNumberList.isEmpty()){
                continue;
            }

            for (String quotaNumber : quotaNumberList) {
                DeviceApplyDO deviceApplyDO = childNumberToDeviceApply.get(quotaNumber);
                if (deviceApplyDO == null){
                    continue;
                }
                PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
                pplStockSupplyRspDO.setSupplyId(stockId);
                pplStockSupplyRspDO.setMatchType(PplStockSupplyMatchTypeEnum.BUY.getCode());
                pplStockSupplyRspDO.setPlanProductName("腾讯云CVM");
                pplStockSupplyRspDO.setReqId(reqDO.getId());

                pplStockSupplyRspDO.setRegion(orderSupplyPlanDetailDO.getSupplyRegion());
                pplStockSupplyRspDO.setRegionName(orderSupplyPlanDetailDO.getSupplyRegionName());
                pplStockSupplyRspDO.setZone(orderSupplyPlanDetailDO.getSupplyZone());
                pplStockSupplyRspDO.setZoneName(orderSupplyPlanDetailDO.getSupplyZoneName());
                pplStockSupplyRspDO.setInstanceTotalCore(orderSupplyPlanDetailDO.getSupplyCoreNum());
                pplStockSupplyRspDO.setInstanceNum(orderSupplyPlanDetailDO.getSupplyInstanceNum());
                pplStockSupplyRspDO.setMatchInstanceType(orderSupplyPlanDetailDO.getSupplyInstanceType());

                pplStockSupplyRspDO.setHostType(deviceApplyDO.getDeviceType());
                pplStockSupplyRspDO.setHostNum(BigDecimal.valueOf(deviceApplyDO.getTotalNum()));
                pplStockSupplyRspDO.setMatchDemandDate(
                        cloud.demand.app.common.utils.DateUtils.dateToLocalDate(deviceApplyDO.getExpectDeliveryDate()));
                pplStockSupplyRspDO.setObsProjectType(deviceApplyDO.getObsProjectType());
                pplStockSupplyRspDO.setRemark("【系统生成】： 订单: " + orderSupplyPlanDetailDO.getOrderNumber() + " 关联Q单: " + quotaNumber
                + " 该采购供应方案已设置Q单,因一个Q单可能关联多条明细，此处需求核心数和物理机台数转换不准。");
                childNumberToDeviceApply.remove(quotaNumber);
                rspList.add(pplStockSupplyRspDO);
            }
        }

        demandDBHelper.insertBatchWithoutReturnId(rspList);
        return Boolean.TRUE;

    }

    public List<DeviceApplyDO> getQuotaNumberList(List<String> numberList){
        Set<String> quotaNumberSet = new HashSet<>();
        List<String> mainNumberList = numberList.stream().filter(v -> !v.startsWith("Q")).collect(Collectors.toList());
        if (!mainNumberList.isEmpty()) {
            List<String> childQuotaNumber = shuttleDBHelper.getRaw(String.class,
                    "select sub_id from device_apply where order_type = 1 and id in (?)", mainNumberList);
            if (!childQuotaNumber.isEmpty()){
                quotaNumberSet.addAll(childQuotaNumber);
            }
        }

        List<String> quotaNumberList = numberList.stream().filter(v -> v.startsWith("Q")).collect(Collectors.toList());
        if (!quotaNumberList.isEmpty()) {
            quotaNumberSet.addAll(quotaNumberList);
        }

        return shuttleDBHelper.getAll(DeviceApplyDO.class,
                " where order_type = 1 and sub_id in (?)", quotaNumberSet);

    }
    private void setCvmRemark(PplStockSupplyRspDO data, String value) {
        if (Strings.isBlank(data.getRemark())) {
            data.setRemark(value);
            return;
        }
        data.setRemark(data.getRemark() + ";\n" + value);
    }


    private void setRegionInfo(PplStockSupplyRspDO data,Map<String, String> landMap,Map<String,StaticZoneDO> zone2DOMap) {

        StaticZoneDO one = zone2DOMap.get(data.getZone());

        log.info("zone: {}", data.getZone());

        if (one != null) {
            data.setZoneName(one.getZoneName());
            data.setRegionName(one.getRegionName());
            String country = landMap.getOrDefault(one.getRegion(), "未知");
            data.setCountryName(country);
        }

    }

    private PplStockSupplyRspDO transFrom(QueryStockSupplyResultRsp.Result result) {
        PplStockSupplyRspDO pplStockSupplyRspDO = new PplStockSupplyRspDO();
//        pplStockSupplyRspDO.setSupplyId();
//        pplStockSupplyRspDO.setReqId();
//        pplStockSupplyRspDO.setRegionName();
//        pplStockSupplyRspDO.setZoneName();
        PplStockSupplyMatchTypeEnum cvm = PplStockSupplyMatchTypeEnum.getCvm(result.getMatchType());
        if (cvm != null) {
            pplStockSupplyRspDO.setMatchType(cvm.getCode());
        } else {
            pplStockSupplyRspDO.setMatchType(result.getMatchType());
        }

        if (StringUtil.isNotEmpty(result.getMatchInstanceType())) {
            pplStockSupplyRspDO.setMatchInstanceType(result.getMatchInstanceType());
        }
        if (StringUtil.isNotEmpty(result.getHostType())) {
            //物理机规格若为“AAA_BBB”，即下划线带字符串，如果存在，将“_BBB”去除，去除后设备类型应该为“AAA”
            pplStockSupplyRspDO.setHostType(
                    result.getHostType().contains("_") ? result.getHostType().split("_")[0] : result.getHostType());
        }
        pplStockSupplyRspDO.setHostNum(result.getHostCount());
        pplStockSupplyRspDO.setInstanceNum(result.getMatchCount());
//        pplStockSupplyRspDO.setInstanceTotalCore();
//        pplStockSupplyRspDO.setCountryName();
//        pplStockSupplyRspDO.setPlanProductName();
        pplStockSupplyRspDO.setRegion(result.getRegion());
        pplStockSupplyRspDO.setZone(result.getZone());
        return pplStockSupplyRspDO;
    }

    @Override
    @Transactional("demandTransactionManager")
//    @TaskLog(taskName = "stockSupplyCvmSend")
    public void sendCbs(Long stockId) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = getStockSupply(stockId, true);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        if (!Strings.equals(stockSupply.getCbsTaskStatus(), PplStockSupplyStatusEnum.SENDING.getCode())
                && !Strings.equals(stockSupply.getCbsTaskStatus(), PplStockSupplyStatusEnum.FAIL.getCode())) {
            return;
        }

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyReqDO::getType, "CBS");
        whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, stockId);

        List<PplStockSupplyReqDO> all = demandDb().getAll(PplStockSupplyReqDO.class, whereContent);

        Map<String, List<PplStockSupplyReqDO>> pplIdGroupBy = ListUtils.groupBy(all, PplStockSupplyReqDO::getPplId);

        List<SaveCbsStockPlanReq.Item> data = Lang.list();
        for (String key : pplIdGroupBy.keySet()) {
            List<PplStockSupplyReqDO> value = pplIdGroupBy.get(key);

            SaveCbsStockPlanReq.Item one = transCbsForSendFrom(value.get(0));

            if (Strings.isBlank(one.getRegion()) || Strings.isBlank(one.getDemandType()) || Strings.isBlank(
                    one.getZone())) {
                continue;
            }

            List<PplStockSupplyReqDO> disks = value.stream()
                    .filter((o) -> Strings.isNotBlank(o.getDiskType()) && o.getDiskTotalSize() != null)
                    .collect(Collectors.toList());
            Map<String, List<PplStockSupplyReqDO>> diskGroupBy = ListUtils.groupBy(disks,
                    PplStockSupplyReqDO::getDiskType);

            List<SaveCbsStockPlanReq.Disk> disksData = Lang.list();
            int instanceNum = value.get(0).getInstanceNum() == null ? 0 : value.get(0).getInstanceNum();
            for (String diskType : diskGroupBy.keySet()) {
                List<PplStockSupplyReqDO> pplStockSupplyReqDOS = diskGroupBy.get(diskType);
                BigDecimal diskTotalSizeSum = NumberUtils.sum(pplStockSupplyReqDOS,
                        PplStockSupplyReqDO::getDiskTotalSize);
                Disk disk = new Disk(diskType, diskTotalSizeSum.intValue() * instanceNum, 1);
                disksData.add(disk);
            }
            one.setDisks(disksData);
            data.add(one);
        }

        try {
            CbsStockPlanRsq cbsStockPlanRsq = qcbsApiService.saveCbsStockPlan(new SaveCbsStockPlanReq(data));
            log.info("saveCbsStockPlan response : {}", cbsStockPlanRsq);
            if (cbsStockPlanRsq == null || cbsStockPlanRsq.getResponse() == null) {
                setCbsErrorMsg(stockSupply, "CBS接口返回结果为空");
                return;
            }
            Response response = cbsStockPlanRsq.getResponse();

            if (response.getError() != null) {
                setCbsErrorMsg(stockSupply, "云霄接口调用返回错误: " + response.getError().getMessage());
                return;
            }

            if (Strings.isBlank(response.getTaskId())) {
                setCbsErrorMsg(stockSupply, "云霄接口调用返回 taskId 为空");
                return;
            }
            stockSupply.setCbsTaskId(response.getTaskId());
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.SENT.getCode());
        } catch (Exception exception) {
            stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCbsErrorMsg("云霄接口下发调用出错，异常报错：" + exception.getMessage());
        }
        demandDBHelper.update(stockSupply);
    }

    private SaveCbsStockPlanReq.Item transCbsForSendFrom(PplStockSupplyReqDO reqDO) {
        SaveCbsStockPlanReq.Item item = new SaveCbsStockPlanReq.Item();
        item.setUin(reqDO.getCustomerUin());
        item.setRegion(reqDO.getRegion());
        item.setZone(reqDO.getZone());
        item.setDemandDate(DateUtils.format(reqDO.getBeginBuyDate(), "yyyy-MM-dd"));
        item.setDemandType(reqDO.getDemandType());
        item.setPriority(reqDO.getPriority());
        item.setLabel(reqDO.getPplId());
        item.setIo(reqDO.getCbsIo());
        return item;
    }


    @Override
    @Transactional("demandTransactionManager")
//    @TaskLog(taskName = "stockSupplyCvmSend")
    public void sendCvm(Long stockId) {

        demandDBHelper.turnOffFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);
        PplStockSupplyDO stockSupply = getStockSupply(stockId, true);
        demandDBHelper.turnOnFeature(FeatureEnum.LOG_SQL_AT_INFO_LEVEL);

        if (!Strings.equals(stockSupply.getCvmTaskStatus(), PplStockSupplyStatusEnum.SENDING.getCode())
                && !Strings.equals(stockSupply.getCvmTaskStatus(), PplStockSupplyStatusEnum.FAIL.getCode())) {
            return;
        }

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyReqDO::getType, "CVM");
        whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, stockId);
        whereContent.andEqual(PplStockSupplyReqDO::getDataSource, PplStockSupplyDataSourceEnum.STOCK_SUPPLY.getCode());

        List<PplStockSupplyReqDO> all = demandDb().getAll(PplStockSupplyReqDO.class, whereContent);

        List<CreateStockSupplyPlanReq.Item> items = ListUtils.transform(all, this::transform);

        CreateStockSupplyPlanReq req = new CreateStockSupplyPlanReq();
        String operator = stockSupply.getOperator();
        operator = operator == null ? "" : operator;
        req.setCreator(operator);
        req.setDescription("CRP 下发");
        req.setItems(items);
        if (Strings.equals("SYSTEM", stockSupply.getOperator())) {
            req.setDescription("系统自动下发");
        }

        try {
            CreateStockSupplyPlanRsp stockSupplyPlan = yunxiaoAPIService.createStockSupplyPlan(req);
            if (stockSupplyPlan == null || stockSupplyPlan.getData() == null) {
                setErrorMsg(stockSupply, "云霄接口返回 CVM 对冲结果为空: " + stockSupplyPlan);
                return;
            }
            if (!stockSupplyPlan.isSuccess()) {
                setErrorMsg(stockSupply, "云霄接口调用返回失败");
                return;
            }
            Data data = stockSupplyPlan.getData();
            String planId = data.getPlanId();

            if (Strings.isBlank(planId)) {
                setErrorMsg(stockSupply, "云霄接口调用返回 planId 为空");
                return;
            }
            QueryStockSupplyResultRsp submitRes = yunxiaoAPIService.submitStockSupplyResult(planId);
            if (submitRes == null || submitRes.getData() == null) {
                setErrorMsg(stockSupply, "云霄提交接口返回 CVM 对冲结果为空");
                return;
            }

            if (!stockSupplyPlan.isSuccess()) {
                setErrorMsg(stockSupply, "云霄提交接口调用返回失败");
                return;
            }

            stockSupply.setCvmTaskId(planId);
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.SENT.getCode());

        } catch (Exception exception) {
            stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
            stockSupply.setCvmErrorMsg("云霄接口下发调用出错，异常报错：" + exception.getMessage());
        }

        demandDBHelper.update(stockSupply);

    }

    private void setCbsErrorMsg(PplStockSupplyDO stockSupply, String msg) {
        stockSupply.setCbsTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
        stockSupply.setCbsErrorMsg(msg);
        demandDBHelper.update(stockSupply);
    }

    private void setErrorMsg(PplStockSupplyDO stockSupply, String msg) {
        stockSupply.setCvmTaskStatus(PplStockSupplyStatusEnum.FAIL.getCode());
        stockSupply.setCvmErrorMsg(msg);
        demandDBHelper.update(stockSupply);
    }

    private CreateStockSupplyPlanReq.Item transform(PplStockSupplyReqDO reqDO) {
        CreateStockSupplyPlanReq.Item item = new CreateStockSupplyPlanReq.Item();
//        item.setPlanId();
        item.setUin(reqDO.getCustomerUin());
//        item.setAppId();
        item.setRegion(reqDO.getRegion());
        item.setZone(reqDO.getZone());
        item.setInstanceType(reqDO.getInstanceModel());
        item.setOptionalInstanceTypes(reqDO.getAlternativeInstanceType());
        item.setCount(reqDO.getInstanceNum());
        item.setDemandDate(DateUtils.formatDate(reqDO.getBeginBuyDate()));
        item.setDemandEndDate(DateUtils.formatDate(reqDO.getEndBuyDate()));
        item.setDemandType(reqDO.getDemandType());
        item.setPriority(reqDO.getPriority());
        item.setLabel(reqDO.getPplId());
        item.setOrganizationName(reqDO.getIndustryDept());
        item.setAppShortName(reqDO.getCustomerShortName());
        item.setAppRole(reqDO.getAppRole());
        item.setWinRate(reqDO.getWinRate() == null ? 0 : reqDO.getWinRate().intValue());
        item.setRemark(reqDO.getRemark());
        PplConsensusStatusEnum statusEnum = PplConsensusStatusEnum.getByCode(reqDO.getConsensusStatus());
        item.setStatus(statusEnum == null ? null : statusEnum.getName());
        return item;
    }

    private PplStockSupplyDO getStockSupply(Long stockId, boolean throwException) {
        if (stockId == null || stockId < 0) {
            throw BizException.makeThrow("id 为空,或小于0 ");
        }
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplStockSupplyDO::getId, stockId);
        PplStockSupplyDO pplStockSupplyDO = demandDb().getOne(PplStockSupplyDO.class, whereContent);
        if (pplStockSupplyDO == null && !throwException) {
            throw BizException.makeThrow("没有找到对用记录： %d", stockId);
        }
        return pplStockSupplyDO;
    }

    private PplStockSupplyReqDO transCvmForQuery(PplVersionGroupRecordItemWithOrderVO oneItem) {
        oneItem.setFromItem(false);
        return transCvmFrom(oneItem, 0L);
    }

    private List<PplStockSupplyReqDO> transCbsForQuery(PplVersionGroupRecordItemWithOrderVO oneItem) {
        oneItem.setFromItem(false);
        List<PplStockSupplyReqDO> ret = Lang.list();
        String systemDiskTypeName = oneItem.getSystemDiskType();
        String systemDiskType = "";
        Integer systemDiskNum = oneItem.getSystemDiskNum();
        Integer systemDiskStorage = oneItem.getSystemDiskStorage();

        systemDiskType = PplDiskTypeEnum.getCodeByName(systemDiskTypeName);

        int totalSystemDiskNum = getDiskTotalNum(systemDiskType, systemDiskNum, systemDiskStorage);

        String dataDiskTypeName = oneItem.getDataDiskType();
        String dataDiskType = "";
        Integer dataDiskStorage = oneItem.getDataDiskStorage();
        Integer dataDiskNum = oneItem.getDataDiskNum();

        dataDiskType = PplDiskTypeEnum.getCodeByName(dataDiskTypeName);

        int totalDataDiskNum = getDiskTotalNum(dataDiskType, dataDiskStorage, dataDiskNum);

        // 相同的直接聚合
        if (Strings.equals(systemDiskType, dataDiskType) && totalDataDiskNum != 0 && totalSystemDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, 0L, dataDiskType, dataDiskTypeName,
                    totalDataDiskNum + totalSystemDiskNum, dataDiskNum + systemDiskNum);
            ret.add(pplStockSupplyReqDO);
            return ret;
        }

        if (totalDataDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, 0L, dataDiskType, dataDiskTypeName,
                    totalDataDiskNum, dataDiskNum);
            ret.add(pplStockSupplyReqDO);
        }
        if (totalSystemDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, 0L, systemDiskType, systemDiskTypeName,
                    totalSystemDiskNum, systemDiskNum);
            ret.add(pplStockSupplyReqDO);
        }
        return ret;
    }


    private PplStockSupplyReqDO getCVMReq(PplVersionGroupRecordItemWithOrderVO oneItem, Long stockSupplyId,
            int priority) {
        PplStockSupplyReqDO pplStockSupplyReqDO = transCvmFrom(oneItem, stockSupplyId);
        if (pplStockSupplyReqDO == null) {
            return null;
        }
        pplStockSupplyReqDO.setPriority(priority);
        return pplStockSupplyReqDO;
    }

    private List<PplStockSupplyReqDO> getCBSReq(PplVersionGroupRecordItemWithOrderVO oneItem, Long stockSupplyId,
            int priority) {

        List<PplStockSupplyReqDO> ret = Lang.list();
        String systemDiskTypeName = oneItem.getSystemDiskType();
        String systemDiskType = "";
        Integer systemDiskNum = oneItem.getSystemDiskNum();
        Integer systemDiskStorage = oneItem.getSystemDiskStorage();

        systemDiskType = PplDiskTypeEnum.getCodeByName(systemDiskTypeName);
        int totalSystemDiskNum = getDiskTotalNum(systemDiskType, systemDiskNum, systemDiskStorage);

        String dataDiskTypeName = oneItem.getDataDiskType();
        String dataDiskType = "";
        Integer dataDiskStorage = oneItem.getDataDiskStorage();
        Integer dataDiskNum = oneItem.getDataDiskNum();

        dataDiskType = PplDiskTypeEnum.getCodeByName(dataDiskTypeName);
        int totalDataDiskNum = getDiskTotalNum(dataDiskType, dataDiskStorage, dataDiskNum);

        // 相同的直接聚合
        if (Strings.equals(systemDiskType, dataDiskType) && totalDataDiskNum != 0 && totalSystemDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, stockSupplyId, dataDiskType,
                    dataDiskTypeName, totalDataDiskNum + totalSystemDiskNum, dataDiskNum + systemDiskNum);
            pplStockSupplyReqDO.setPriority(priority);
            ret.add(pplStockSupplyReqDO);
            return ret;
        }

        if (totalDataDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, stockSupplyId, dataDiskType,
                    dataDiskTypeName, totalDataDiskNum, dataDiskNum);
            pplStockSupplyReqDO.setPriority(priority);
            ret.add(pplStockSupplyReqDO);
        }
        if (totalSystemDiskNum != 0) {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCbsFrom(oneItem, stockSupplyId, systemDiskType,
                    systemDiskTypeName, totalSystemDiskNum, systemDiskNum);
            pplStockSupplyReqDO.setPriority(priority);
            ret.add(pplStockSupplyReqDO);
        }
        return ret;
    }


    private PplStockSupplyReqDO transCvmFrom(PplVersionGroupRecordItemWithOrderVO oneItem, Long supplyId) {

        if (oneItem.getInstanceModel() == null){
            return null;
        }

        PplStockSupplyReqDO pplStockSupplyReqDO = transFrom(oneItem);

        Tuple2<Integer, Integer> modelInfos = P2PInstanceModelParse.parseInstanceModel(oneItem.getInstanceModel());

        if (modelInfos._1 == 0) {
            throw BizException.makeThrow("instanceModel 为空 或 解析核心数为0");
        }

//        pplStockSupplyReqDO.setInstanceTotalCore(modelInfos._1 * oneItem.getInstanceNum());
        // 2023-03-02 这里替换为 未预约实例
        //        https://iwiki.woa.com/pages/viewpage.action?pageId=4007560688
        //        Max(需求实例-已预约实例,0)

        pplStockSupplyReqDO.setInstanceTotalCore(modelInfos._1 * oneItem.getInstanceNum());
        pplStockSupplyReqDO.setInstanceNum(pplStockSupplyReqDO.getInstanceTotalCore() / modelInfos._1);

        pplStockSupplyReqDO.setSupplyId(supplyId);
        pplStockSupplyReqDO.setType("CVM");
        pplStockSupplyReqDO.setAppRole(yunxiaoAPIService.getYunxiaoAppRoleMapping(oneItem.getProduct()));
        pplStockSupplyReqDO.setDemandScene(oneItem.getDemandScene());
        pplStockSupplyReqDO.setInstanceTypeNamePrefix(parseInstanceTypeName(oneItem.getInstanceType()));
        setRegionInfo(pplStockSupplyReqDO);
        return pplStockSupplyReqDO;
    }

    private int getAnInt(Integer integer) {
        return integer == null ? 0 : integer;
    }

    private String parseInstanceTypeName(String instanceType) {
        Map<String, String> stringStringMap = pplDictService.queryInstanceTypeNamePrefixMap();
        return stringStringMap.getOrDefault(instanceType, "");
    }

    private PplStockSupplyReqDO transCbsFrom(PplVersionGroupRecordItemWithOrderVO oneItem, Long supplyId,
            String diskType, String diskTypeName, Integer totalDisk, Integer diskNum) {
        PplStockSupplyReqDO pplStockSupplyReqDO = transFrom(oneItem);
        pplStockSupplyReqDO.setSupplyId(supplyId);
        pplStockSupplyReqDO.setType("CBS");
        pplStockSupplyReqDO.setDiskType(diskType);
        pplStockSupplyReqDO.setDiskTypeName(diskTypeName);
        pplStockSupplyReqDO.setDiskTotalSize(totalDisk);
        pplStockSupplyReqDO.setDiskNum(diskNum);

        pplStockSupplyReqDO.setInstanceTypeNamePrefix(parseInstanceTypeName(oneItem.getInstanceType()));

        setRegionInfo(pplStockSupplyReqDO);

        return pplStockSupplyReqDO;
    }


    private void setRegionInfo(PplStockSupplyReqDO pplStockSupplyReqDO) {

        String regionName = pplStockSupplyReqDO.getRegionName();
        String zoneName = pplStockSupplyReqDO.getZoneName();
        if (Strings.equals(regionName, "随机") || Strings.equals(regionName, "随机地域")) {
            // todo
            PplConfigStockSupplyDefaultCityZoneDO defaultRegion = demandDBHelper.getOne(
                    PplConfigStockSupplyDefaultCityZoneDO.class, "where type=?", "默认地域");
            if (defaultRegion != null) {
                regionName = defaultRegion.getCityName();
                zoneName = defaultRegion.getZoneName();
            }
        }
        if (Strings.equals(zoneName, "随机") || Strings.equals(zoneName, "随机可用区")) {
            // todo
            // 去除随机可用区判断
//            PplConfigStockSupplyDefaultCityZoneDO defaultRegion = demandDBHelper.getOne(
//                    PplConfigStockSupplyDefaultCityZoneDO.class, "where type=? and city_name=?", "默认可用区",
//                    regionName);
//            if (defaultRegion != null) {
//                regionName = defaultRegion.getCityName();
//                zoneName = defaultRegion.getZoneName();
//            } else {
//                zoneName = "无默认可用区";
//            }
            zoneName = "随机可用区";
        }
        pplStockSupplyReqDO.setRegionName(regionName);
        pplStockSupplyReqDO.setZoneName(zoneName);

        List<RegionZoneDTO> allData = pplDictService.queryRegionZoneInfo();

//        WhereContent whereContent = new WhereContent();
//        whereContent.andEqual(IndustryDemandRegionZoneInstanceTypeDictDO::getRegionShortChName, regionName);
//        IndustryDemandRegionZoneInstanceTypeDictDO one = demandDb().getOne(
//                IndustryDemandRegionZoneInstanceTypeDictDO.class, whereContent);

        String finalRegionName = regionName;
        List<RegionZoneDTO> regionFilter = ListUtils.filter(allData,
                (o) -> Strings.equals(finalRegionName, o.getRegionShortChName()));

//        if (one != null) {
        if (Lang.isNotEmpty(regionFilter)) {
            RegionZoneDTO regionZoneDTO = regionFilter.get(0);
            pplStockSupplyReqDO.setRegion(regionZoneDTO.getRegionStrId());
            // 去关联资源中台的国内外表
            Map<String, String> landMap = pplDictService.queryRegionStrId2Country();
            String country = landMap.getOrDefault(regionZoneDTO.getRegionStrId(), "未知");
            pplStockSupplyReqDO.setCountryName(country);
        }

//        WhereContent zoneWhereContent = new WhereContent();
//        zoneWhereContent.andEqual(IndustryDemandRegionZoneInstanceTypeDictDO::getZoneName, zoneName);
//        IndustryDemandRegionZoneInstanceTypeDictDO oneZone = demandDb().getOne(
//                IndustryDemandRegionZoneInstanceTypeDictDO.class, zoneWhereContent);

        String finalZoneName = zoneName;
        List<RegionZoneDTO> zoneFilter = ListUtils.filter(allData,
                (o) -> Strings.equals(finalZoneName, o.getZoneName()));

//        if (one != null) {
        if (Lang.isNotEmpty(zoneFilter)) {
            RegionZoneDTO regionZoneDTO = zoneFilter.get(0);
//            if (oneZone != null) {
            pplStockSupplyReqDO.setZone(regionZoneDTO.getZoneStrId());
        }

    }


    private PplStockSupplyReqDO transFrom(PplVersionGroupRecordItemWithOrderVO oneItem) {
        PplStockSupplyReqDO pplStockSupplyReqDO = new PplStockSupplyReqDO();
//        pplStockSupplyReqDO.setSupplyId();
//        pplStockSupplyReqDO.setType();
        pplStockSupplyReqDO.setProduct(oneItem.getProduct());
        pplStockSupplyReqDO.setPplOrder(oneItem.getPplOrder());
        pplStockSupplyReqDO.setPplId(oneItem.getPplId());
        pplStockSupplyReqDO.setRecordVersion(oneItem.getRecordVersion());
        pplStockSupplyReqDO.setVersionGroupRecordId(oneItem.getVersionGroupRecordId());
        pplStockSupplyReqDO.setCustomerUin(oneItem.getCustomerUin());
        pplStockSupplyReqDO.setRegionName(oneItem.getRegionName());
        pplStockSupplyReqDO.setZoneName(oneItem.getZoneName());
//        pplStockSupplyReqDO.setRegion();
//        pplStockSupplyReqDO.setZone();
        pplStockSupplyReqDO.setInstanceType(oneItem.getInstanceType());
        pplStockSupplyReqDO.setInstanceModel(oneItem.getInstanceModel());
        pplStockSupplyReqDO.setAlternativeInstanceType(oneItem.getAlternativeInstanceType());
        pplStockSupplyReqDO.setInstanceNum(oneItem.getInstanceNum());
        pplStockSupplyReqDO.setBeginBuyDate(oneItem.getBeginBuyDate());
        pplStockSupplyReqDO.setDemandType(oneItem.getDemandType());
        pplStockSupplyReqDO.setYearMonth(DateUtils.format(oneItem.getBeginBuyDate(), "yyyy-MM"));
//        pplStockSupplyReqDO.setDiskTotalSize();
//        pplStockSupplyReqDO.setDiskType();
//        pplStockSupplyReqDO.setDiskNum();
        pplStockSupplyReqDO.setIndustryDept(oneItem.getIndustryDept());
        pplStockSupplyReqDO.setCustomerType(oneItem.getCustomerType());
        pplStockSupplyReqDO.setCustomerShortName(oneItem.getCustomerShortName());
        pplStockSupplyReqDO.setWarZone(oneItem.getWarZone());
//        pplStockSupplyReqDO.setCountryName();
//        pplStockSupplyReqDO.setPriority();

        if (oneItem.getFromItem()) {
            pplStockSupplyReqDO.setItemId(oneItem.getId());
        } else {
            pplStockSupplyReqDO.setGroupRecordItemId(oneItem.getId());
        }
        pplStockSupplyReqDO.setRecordItemJson(JSON.toJson(oneItem));
//        pplStockSupplyReqDO.setInstanceTotalCore();
        pplStockSupplyReqDO.setDemandScene(oneItem.getDemandScene());
        pplStockSupplyReqDO.setEndBuyDate(oneItem.getEndBuyDate());
        pplStockSupplyReqDO.setWinRate(oneItem.getWinRate());

        pplStockSupplyReqDO.setApplyInstanceNum(
                oneItem.getApplyInstanceNum() != null ? oneItem.getApplyInstanceNum() : 0);
        pplStockSupplyReqDO.setApplyTotalCore(oneItem.getApplyTotalCore() != null ? oneItem.getApplyTotalCore() : 0);
        pplStockSupplyReqDO.setRemark(oneItem.getNote());
        pplStockSupplyReqDO.setConsensusStatus(oneItem.getConsensusStatus());
        pplStockSupplyReqDO.setCbsIo(oneItem.getCbsIo());
        return pplStockSupplyReqDO;
    }

    private int getDiskTotalNum(String systemDiskType, Integer systemDiskNum, Integer systemDiskStorage) {
        int ret = 0;
        if (Strings.isBlank(systemDiskType)) {
            return ret;
        }
        if (systemDiskStorage == null || systemDiskNum == null) {
            return ret;
        }
        return systemDiskNum * systemDiskStorage;
    }


    @Override
    public QueryPplStockSupplyViewRsp queryPplStockSupplyView(QueryPplStockSupplyViewReq req) {

        PplStockSupplyDO latestVersion = getVersionLatestDoneSupply(req.getVersionCode());
        if (latestVersion == null) {
            return new QueryPplStockSupplyViewRsp(Lang.list());
        }

        WhereContent whereContent = new WhereContent();
        {

            whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, latestVersion.getId());
            whereContent.addAnd(getPplIdWhereContent(req.getPplId()));

            if (Strings.isNotBlank(req.getBeginYearMonth())) {
                whereContent.andGTE(PplStockSupplyReqDO::getBeginBuyDate, req.getBeginYearMonth());
            }
            if (Strings.isNotBlank(req.getEndYearMonth())) {
                whereContent.andLTE(PplStockSupplyReqDO::getYearMonth, req.getEndYearMonth());
            }

            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getIndustryDept, req.getIndustryDept());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getCustomerShortName, req.getCustomerShortName());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getWarZone, req.getWarZone());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getCountryName, req.getCountryName());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getRegionName, req.getRegionName());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getZoneName, req.getZoneName());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getInstanceType, req.getInstanceType());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getInstanceModel, req.getInstanceModel());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getDiskType, req.getInstanceModel());

        }
        List<PplStockSupplyReqVO> all = demandDb().getAll(PplStockSupplyReqVO.class, whereContent);

        Map<String, List<PplStockSupplyReqVO>> groupData = ListUtils.groupBy(all, (o) -> {
            if (Lang.isEmpty(req.getDimensions())) {
                return "NO_DIMENSION";
            }
            List<Object> collect = req.getDimensions().stream().map((dimension) -> {
                Field declaredField = ORMUtils.getDeclaredField(PplStockSupplyReqDO.class, dimension);
                return DOInfoReader.getValue(declaredField, o);
            }).collect(Collectors.toList());
            return Strings.join("@", collect);
        });

        List<Item> items = ListUtils.transform(groupData.entrySet(),
                (o) -> QueryPplStockSupplyViewRsp.trans(o.getValue()));

        return new QueryPplStockSupplyViewRsp(items);
    }

    @Override
    public QueryPplStockSupplyViewBuyRsp queryPplStockSupplyBuyView(QueryPplStockSupplyBuyViewReq req) {

        PplStockSupplyDO latestVersion = getVersionLatestDoneSupply(req.getVersionCode());
        if (latestVersion == null) {
            return new QueryPplStockSupplyViewBuyRsp(Lang.list());
        }

        WhereContent whereContent = new WhereContent();
        {
            whereContent.andEqual("t1.supply_id", latestVersion.getId());
            whereContent.andEqual("t1.match_type", "BUY");
            if (Strings.isNotBlank(req.getPplId())) {
                String pplId = req.getPplId();
                List<String> pplIds = Arrays.stream(StringUtils.trim(pplId).split(";")).filter(Strings::isNotBlank)
                        .flatMap((o) -> Arrays.stream(o.split(","))).filter(Strings::isNotBlank)
                        .collect(Collectors.toList());
                whereContent.andInIfValueNotEmpty("ppl_id", pplIds);
            }

            whereContent.andInIfValueNotEmpty("t2", PplStockSupplyReqDO::getIndustryDept, req.getIndustryDept());
            whereContent.andInIfValueNotEmpty("t2", PplStockSupplyReqDO::getCustomerShortName,
                    req.getCustomerShortName());

            whereContent.andInIfValueNotEmpty("t1", PplStockSupplyRspDO::getCountryName, req.getCountryName());
            whereContent.andInIfValueNotEmpty("t1", PplStockSupplyRspDO::getRegionName, req.getRegionName());
            whereContent.andInIfValueNotEmpty("t1", PplStockSupplyRspDO::getZoneName, req.getZoneName());

            whereContent.andInIfValueNotEmpty("t1", PplStockSupplyRspDO::getPlanProductName, req.getPlanProductName());
            whereContent.andInIfValueNotEmpty("t1", PplStockSupplyRspDO::getHostType, req.getDeviceType());

        }

        List<PplStockSupplyRspHostNumSumVO> all = demandDBHelper.getAll(PplStockSupplyRspHostNumSumVO.class);

        Map<String, List<PplStockSupplyRspHostNumSumVO>> groupData = ListUtils.groupBy(all, (o) -> {
            if (Lang.isEmpty(req.getDimensions())) {
                return "NO_DIMENSION";
            }
            List<Object> collect = req.getDimensions().stream().map((dimension) -> {
                Field declaredField = ORMUtils.getDeclaredField(PplStockSupplyReqDO.class, dimension);
                return DOInfoReader.getValue(declaredField, o);
            }).collect(Collectors.toList());
            return Strings.join("@", collect);
        });

        List<QueryPplStockSupplyViewBuyRsp.Item> ret = QueryPplStockSupplyViewBuyRsp.trans(groupData);

        return new QueryPplStockSupplyViewBuyRsp(ret);
    }

    @Override
    public QueryStockSupplyGroupViewRsp queryStockSupplyGroupView(QueryStockSupplyGroupViewReq req) {

        if (req.getGroupId() == null) {
            throw BizException.makeThrow("groupId 为空");
        }
        Long groupId = req.getGroupId();
        PplVersionGroupRecordDO latestRecord = pplDataAccessService.getLatestPplVersionGroupRecordDO(groupId, true);

        String recordIdsSql =
                "select distinct id\n" + "from ppl_version_group_record where version_group_id=? and deleted=0";
        List<String> recordIds = demandDBHelper.getRaw(String.class, recordIdsSql, groupId);

        if (Strings.isBlank(req.getVersionCode())) {
            throw BizException.makeThrow("versionCode 为空");
        }
        PplVersionDO version = pplVersionService.getByVersionCode(req.getVersionCode());

        String resourceTypeTmp = "CVM";
        if (Strings.isNotBlank(req.getResourceType())) {
            List<String> list = Lang.list("CVM", "CBS", "CORE", "GPU");
            if (list.contains(req.getResourceType())) {
                resourceTypeTmp = req.getResourceType();
            }
        }
        String resourceType = resourceTypeTmp;

        QueryStockSupplyGroupViewRsp resp = new QueryStockSupplyGroupViewRsp();
        addDictData(resp, latestRecord.getId());
        resp.setResourceType(resourceType);

        // 这里再打一个补丁， gpu 的特殊处理， 加不进去了

        WhereContent groupWhere = new WhereContent();
        groupWhere.andEqual(PplVersionGroupDO::getId, groupId);
        PplVersionGroupDO groupDO = ORMUtils.db(demandDBHelper).getOne(PplVersionGroupDO.class, groupWhere);

        if (Strings.equals(groupDO.getProduct(), Ppl13weekProductTypeEnum.GPU.getName())) {

            PplStockSupplyGpuDO gpuStockGpuDO = demandDBHelper.getOne(PplStockSupplyGpuDO.class,
                    "where  version_code=? order by id desc", req.getVersionCode());

            if (gpuStockGpuDO == null) {
                resp.setStatusInfo("未对冲");
            } else {
                resp.setStatusInfo(gpuStockGpuDO.getStatus());
                if ("已完成".equals(resp.getStatusInfo())) {
                    resp.setStatusInfo("已对冲");
                }
            }

            List<QueryStockSupplyGroupViewRsp.Item> d1 = getGpuPplGroupRecordItem(req, latestRecord, false);
            List<QueryStockSupplyGroupViewRsp.Item> d2 = getGpuPplGroupRecordItem(req, latestRecord, true);
            d1.addAll(d2);
            resp.setItems(d1);
            fillTotalForDebug(resp, resp.getItems());
            return resp;
        }

        // 这里把需求数据取过来， 这一期先这样优化，可能存在问题
        List<QueryStockSupplyGroupViewRsp.Item> items = getPplGroupRecordItem(req, latestRecord, resourceType, version,
                false);
        List<QueryStockSupplyGroupViewRsp.Item> returnItems = getPplGroupRecordItem(req, latestRecord, resourceType,
                version, true);
        items.addAll(returnItems);
        resp.setItems(items);
        fillTotalForDebug(resp, resp.getItems());

        PplStockSupplyDO latestVersion = getVersionLatestDoneSupply(req.getVersionCode());
        long supplyId;
        if (latestVersion == null) {
            PplStockSupplyDO oneLatest = demandDBHelper.getOne(PplStockSupplyDO.class,
                    "where  version_code=? order by id desc", req.getVersionCode());
            // 没有完成的，但是有在对冲中的
            if (oneLatest == null) {
                resp.setStatusInfo("未开始对冲,列表展示实时需求数据。");
                return resp;
            } else {
                if (Strings.equals(oneLatest.getStatus(), PplStockSupplyStatusEnum.INIT.getCode())) {
                    resp.setStatusInfo("未开始对冲,列表展示实时需求数据。");
                    return resp;
                }
                // 对冲中也
                supplyId = oneLatest.getId();
                resp.setStatusInfo("对冲进行中,后台自动切片需求, 切片完成后展示切片值。");
            }
        } else {
            supplyId = latestVersion.getId();
            resp.setStatusInfo("对冲返回时间: " + DateUtils.format(latestVersion.getFinishTime()));
        }
        HashMap<String, QueryStockSupplyGroupViewRsp.Item> itemMap = new HashMap<>();
        for (QueryStockSupplyGroupViewRsp.Item item : items) {
            itemMap.put(Strings.join("@", item.getDemandType(), item.getDemandCountryName(), item.getDemandRegionName(),
                    item.getDemandInstanceClassName(), item.getDemandInstanceType()), item);
        }

        WhereContent whereContent = getStockReqCommonWhere(req, recordIds, resourceType, supplyId);

        WhereContent newAndElasWhere = new WhereContent(whereContent);
        newAndElasWhere.andNotEqual(PplStockSupplyReqDO::getDemandType, PplDemandTypeEnum.RETURN.getCode());
        List<PplStockSupplyReqVO> allStockData = demandDb().getAll(PplStockSupplyReqVO.class, newAndElasWhere);

//        getDickData(ret, latestVersion);

        Function<PplStockSupplyReqVO, String> cvmGroupKey = (PplStockSupplyReqVO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getInstanceTypeNamePrefix(), o.getInstanceType());

        Function<PplStockSupplyReqVO, String> cbsGroupKey = (PplStockSupplyReqVO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getDiskTypeName());

        Map<String, List<PplStockSupplyReqVO>> groupByData = ListUtils.groupBy(allStockData, (o) -> {
            if (Strings.equals(resourceType, "CBS")) {
                return cbsGroupKey.apply(o);
            }
            return cvmGroupKey.apply(o);
        });

        /**
         * 这三个字段全部放到第一行，第一行最长
         */
        List<String> allYearMonths = getVersionAllYearMonth(version, allStockData);

        Map<String, StockSupplyData> allMatchType = new HashMap<>();

        BaseDataDTO<MainInstanceTypeDTO> mainInstanceType = pplDictService.queryMainInstanceType();
        Map<String, Object> mainInstanceTypeMap = ListUtils.toMap(mainInstanceType.getData(),
                MainInstanceTypeDTO::getInstanceFamily, o -> o);

        List<QueryStockSupplyGroupViewRsp.Item> ret = Lang.list();
        for (String key : groupByData.keySet()) {
            List<PplStockSupplyReqVO> oneData = groupByData.get(key);

            PplStockSupplyReqVO demandData = oneData.get(0);
            String demandInstanceClassName;
            String demandInstanceType;
            if (Strings.equals("CBS", resourceType)) {
                demandInstanceClassName = demandData.getDiskTypeName();
                demandInstanceType = demandData.getDiskTypeName();
            } else {
                demandInstanceClassName = demandData.getInstanceTypeNamePrefix();
                demandInstanceType = demandData.getInstanceType();
            }
            String join = Strings.join("@", demandData.getDemandType(), demandData.getCountryName(),
                    demandData.getRegionName(), demandInstanceClassName, demandInstanceType);
            QueryStockSupplyGroupViewRsp.Item item = itemMap.get(join);
            if (item == null) {
                //如果 item 为空 代表在系统对冲后 版本ppl数据还被人改过并且没有重新发起对冲
                continue;
            }

            Map<String, List<PplStockSupplyReqVO>> yearMonthGroupBy = ListUtils.groupBy(oneData,
                    PplStockSupplyReqDO::getYearMonth);
            List<YearMonthData> stockYearMonthRet = Lang.list();
            List<YearMonthData> applyYearMonthRet = Lang.list();
            List<String> ymTmp = ListUtils.transform(allYearMonths, (o) -> o);
            for (String yearMonth : yearMonthGroupBy.keySet()) {
                ymTmp.remove(yearMonth);
                List<PplStockSupplyReqVO> yearMonthDetail = yearMonthGroupBy.get(yearMonth);
                stockYearMonthRet.add(
                        new YearMonthData(yearMonth, getStockDataByResourceType(resourceType, yearMonthDetail)));
                if (!Objects.equals(resourceType, "CBS")) {
                    applyYearMonthRet.add(
                            new YearMonthData(yearMonth, getApplyDataByResourceType(resourceType, yearMonthDetail)));
                }
            }
            for (String s : ymTmp) {
                stockYearMonthRet.add(new YearMonthData(s, BigDecimal.ZERO));
                applyYearMonthRet.add(new YearMonthData(s, BigDecimal.ZERO));
            }
            ListUtils.sortAscNullLast(stockYearMonthRet, YearMonthData::getName);
            item.setStockTotal(getStockDataByResourceType(resourceType, oneData).intValue());
            item.setStockDetails(stockYearMonthRet);

            if (resourceType != "CBS") {
                // 已预约数据
                item.setApplyTotal(getApplyDataByResourceType(resourceType, oneData).intValue());
                item.setApplyDetails(applyYearMonthRet);
            }

            //  stock_data set
            List<PplStockSupplyRspDO> notEmptyStockData = ListUtils.filter(oneData,
                    (o) -> Lang.isNotEmpty(o.getPplStockSupplyRsp())).stream().map((o) -> {
                // 把req 的数据带过来
                ListUtils.forEach(o.getPplStockSupplyRsp(), (i) -> i.setYearMonth(o.getYearMonth()));
                return o.getPplStockSupplyRsp();
            }).flatMap(Collection::stream).collect(Collectors.toList());
            Map<String, List<PplStockSupplyRspDO>> matchTypeGroupBy = ListUtils.groupBy(notEmptyStockData,
                    PplStockSupplyRspDO::getMatchType);

            mergeMatchType(matchTypeGroupBy);

            //buy 采购
            //move 搬迁
            //satify 大盘满足
            if (!"CBS".equals(resourceType)) {

                List<StockSupplyData> stockSupplyDatas = Lang.list();
                for (String matchTypeCode : matchTypeGroupBy.keySet()) {

                    StockSupplyData oneStockSupplyData = new StockSupplyData();

                    oneStockSupplyData.setCode(matchTypeCode);

                    oneStockSupplyData.setName(
                            PplStockSupplyMatchTypeEnum.getNameOrDefault(matchTypeCode, "未知类型:" + matchTypeCode));
                    String columnName = PplStockSupplyMatchTypeEnum.getCvmColumnName(matchTypeCode);
                    oneStockSupplyData.setInstanceTypeColumnName(columnName);

                    StockSupplyData copyOne = new StockSupplyData();
                    BeanUtils.copyProperties(oneStockSupplyData, copyOne);
                    allMatchType.put(oneStockSupplyData.getName(), copyOne);

                    List<PplStockSupplyRspDO> pplStockSupplyRspDOS = matchTypeGroupBy.get(matchTypeCode);
                    // instanceType yearMonth hostNum/core
                    List<Tuple3<String, String, BigDecimal>> typeYearMonthNum = ListUtils.transform(
                            pplStockSupplyRspDOS, (o) -> {

                                if (Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchTypeCode)) {
                                    o.handleHostType();
                                    if ("CVM".equals(resourceType)) {
                                        return Tuple.of(o.getHostType(), o.getYearMonth(), o.getHostNum());
                                    } else {
                                        return Tuple.of(o.getHostType(), o.getYearMonth(),
                                                getBigDecimal(o.getInstanceTotalCore()));
                                    }
                                }
                                if (Lang.list(PplStockSupplyMatchTypeEnum.MOVE.getCode(),
                                        PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                                        PplStockSupplyMatchTypeEnum.FAIL.getCode()).contains(matchTypeCode)) {
                                    if ("CVM".equals(resourceType)) {
                                        return Tuple.of(o.getMatchInstanceType(), o.getYearMonth(),
                                                getBigDecimal(o.getInstanceNum()));
                                    } else {
                                        return Tuple.of(o.getMatchInstanceType(), o.getYearMonth(),
                                                getBigDecimal(o.getInstanceTotalCore()));
                                    }
                                }

                                if ("CVM".equals(resourceType)) {
                                    return Tuple.of("未知分类", o.getYearMonth(), getBigDecimal(o.getInstanceNum()));
                                } else {
                                    return Tuple.of("未知分类", o.getYearMonth(),
                                            getBigDecimal(o.getInstanceTotalCore()));
                                }
                            });
                    oneStockSupplyData.setDetails(getDetails(typeYearMonthNum, mainInstanceTypeMap, allYearMonths));
                    stockSupplyDatas.add(oneStockSupplyData);
                }

                // 库存满足 和 采购满足 如果没有自动加上
                addColumnIfNotExit(stockSupplyDatas, "CVM");

                item.setStockSupplyData(stockSupplyDatas);
            }

            /**
             * cbs
             *     # 采购         BUY = 'BUY'
             *     # 大盘满足      STOCK = 'STOCK'
             *     # 搬迁满足       MIGRATE = 'MIGRATE'
             *     FAIL
             */
            if ("CBS".equals(resourceType)) {
                List<StockSupplyData> stockSupplyDatas = Lang.list();
                for (String matchTypeCode : matchTypeGroupBy.keySet()) {

                    StockSupplyData oneStockSupplyData = new StockSupplyData();
                    oneStockSupplyData.setCode(matchTypeCode);
                    oneStockSupplyData.setName(
                            PplStockSupplyMatchTypeEnum.getNameOrDefault(matchTypeCode, "未知类型:" + matchTypeCode));
                    if (Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchTypeCode)) {
                        oneStockSupplyData.setInstanceTypeColumnName("采购机型");
                    } else if (Lang.list(PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                            PplStockSupplyMatchTypeEnum.MOVE.getCode()).contains(matchTypeCode)) {
                        oneStockSupplyData.setInstanceTypeColumnName("磁盘类型");
                    }

                    StockSupplyData copyOne = new StockSupplyData();
                    BeanUtils.copyProperties(oneStockSupplyData, copyOne);
                    allMatchType.put(oneStockSupplyData.getName(), copyOne);

                    List<PplStockSupplyRspDO> pplStockSupplyRspDOS = matchTypeGroupBy.get(matchTypeCode);
                    // diskType yearMonth hostNum/diskTotalNum
                    List<Tuple3<String, String, BigDecimal>> typeYearMonthNum = ListUtils.transform(
                            pplStockSupplyRspDOS, (o) -> {
                                if (Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchTypeCode)) {
                                    return Tuple.of(o.getHostType(), o.getYearMonth(), o.getHostNum());
                                }
                                if (Lang.list(PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                                        PplStockSupplyMatchTypeEnum.MOVE.getCode()).contains(matchTypeCode)) {
                                    String matchDiskTypeName = o.getMatchDiskTypeName();
                                    if (Strings.isBlank(matchDiskTypeName)) {
                                        matchDiskTypeName = o.getDiskTypeName();
                                    }
                                    return Tuple.of(matchDiskTypeName, o.getYearMonth(),
                                            getBigDecimal(o.getDiskTotalSize()));
                                }
                                return Tuple.of("未知分类", o.getYearMonth(), getBigDecimal(o.getDiskTotalSize()));
                            });
                    oneStockSupplyData.setDetails(getDetails(typeYearMonthNum, mainInstanceTypeMap, allYearMonths));
                    stockSupplyDatas.add(oneStockSupplyData);
                }

                addColumnIfNotExit(stockSupplyDatas, "CBS");
                item.setStockSupplyData(stockSupplyDatas);
            }
            ret.add(item);

            item.setDemandType(demandData.getDemandType());
            if (Lang.list(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode())
                    .contains(demandData.getDemandType())) {
                item.setDemandTypeShowName("新增/弹性");
            } else if (Strings.equals(PplDemandTypeEnum.RETURN.getCode(), demandData.getDemandType())) {
                item.setDemandTypeShowName("退回");
            } else {
                item.setDemandTypeShowName("未知");
            }

        }

        /**
         *  添加退回数据
         */
        addReturnData(ret, whereContent, resourceType, allYearMonths);
        /**
         * 非空，这里把所有的值都给加上到第一行, 并且排序
         */
        if (Lang.isNotEmpty(ret)) {
            List<StockSupplyData> stockSupplyData = ret.get(0).getStockSupplyData();
            for (String matchType : allMatchType.keySet()) {
                if (!ListUtils.contains(stockSupplyData, (o) -> Strings.equals(matchType, o.getName()))) {
                    stockSupplyData.add(allMatchType.get(matchType));
                }
            }
            SortingField<StockSupplyData, Boolean> first = new SortingField<StockSupplyData, Boolean>(
                    SortingOrderEnum.DESC) {
                @Override
                public Boolean apply(StockSupplyData item) {
                    return Strings.equals(PplStockSupplyMatchTypeEnum.SATISFY.getName(), item.getName());
                }
            };
            SortingField<StockSupplyData, Boolean> second = new SortingField<StockSupplyData, Boolean>(
                    SortingOrderEnum.DESC) {
                @Override
                public Boolean apply(StockSupplyData item) {
                    return Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getName(), item.getName());
                }
            };
            SortingUtils.sort(stockSupplyData, first, second);
        }

        fillTotalForDebug(resp, ret);

        resp.setItems(ret);
        return resp;

    }

    private void mergeMatchType(Map<String, List<PplStockSupplyRspDO>> matchTypeGroupBy) {
        // 把搬迁的合并到库存中
        // 存在搬迁的数据
        if (matchTypeGroupBy.containsKey(PplStockSupplyMatchTypeEnum.MOVE.getCode())) {
            List<PplStockSupplyRspDO> migrateData = matchTypeGroupBy.get(PplStockSupplyMatchTypeEnum.MOVE.getCode());
            if (matchTypeGroupBy.containsKey(PplStockSupplyMatchTypeEnum.SATISFY.getCode())) {
                List<PplStockSupplyRspDO> pplStockSupplyRspDOS1 = matchTypeGroupBy.get(
                        PplStockSupplyMatchTypeEnum.SATISFY.getCode());
                pplStockSupplyRspDOS1.addAll(migrateData);
            } else {
                matchTypeGroupBy.put(PplStockSupplyMatchTypeEnum.SATISFY.getCode(), migrateData);
            }
            matchTypeGroupBy.remove(PplStockSupplyMatchTypeEnum.MOVE.getCode());
        }

        //  把驳回的合并到无法满足中
//        if (matchTypeGroupBy.containsKey(PplStockSupplyMatchTypeEnum.REJECT.getCode())) {
//            List<PplStockSupplyRspDO> rejectData = matchTypeGroupBy.get(
//                    PplStockSupplyMatchTypeEnum.REJECT.getCode());
//            if (matchTypeGroupBy.containsKey(PplStockSupplyMatchTypeEnum.FAIL.getCode())) {
//                List<PplStockSupplyRspDO> pplStockSupplyRspDOS1 = matchTypeGroupBy.get(
//                        PplStockSupplyMatchTypeEnum.FAIL.getCode());
//                pplStockSupplyRspDOS1.addAll(rejectData);
//            } else {
//                matchTypeGroupBy.put(PplStockSupplyMatchTypeEnum.FAIL.getCode(), rejectData);
//            }
//            matchTypeGroupBy.remove(PplStockSupplyMatchTypeEnum.REJECT.getCode());
//        }
    }

    @NotNull
    private List<String> getVersionAllYearMonth(PplVersionDO version, List<PplStockSupplyReqVO> all) {
        List<String> allYearMonths = ListUtils.transform(all, PplStockSupplyReqDO::getYearMonth).stream().distinct()
                .collect(Collectors.toList());
        // 获取所有的年月
        List<cloud.demand.app.common.utils.DateUtils.YearMonth> yearMonths = cloud.demand.app.common.utils.DateUtils.listYearMonth(
                version.getDemandBeginYear(), version.getDemandBeginMonth(), version.getDemandEndYear(),
                version.getDemandEndMonth());
        allYearMonths.addAll(new ArrayList<>(
                ListUtils.transform(yearMonths, (o) -> String.format("%d-%02d", o.getYear(), o.getMonth()))));
        allYearMonths = allYearMonths.stream().distinct().sorted().collect(Collectors.toList());
        return allYearMonths;
    }

    @NotNull
    private WhereContent getStockReqCommonWhere(QueryStockSupplyGroupViewReq req, List<String> recordIds,
            String resourceType, long supplyId) {
        WhereContent whereContent = new WhereContent();
        {
            whereContent.andEqual(PplStockSupplyReqDO::getSupplyId, supplyId);
            whereContent.andIn(PplStockSupplyReqDO::getVersionGroupRecordId, recordIds);
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getRegionName, req.getRegionNames());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getWarZone, req.getWarZoneNames());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getInstanceType, req.getInstanceTypes());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getDemandScene, req.getDemandScenes());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getCustomerUin, req.getCustomerUins());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getCustomerShortName, req.getCustomerShortNames());
//            whereContent.andNotEqual(PplStockSupplyReqDO::getInstanceNum, 0);
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getInstanceTypeNamePrefix,
                    req.getInstanceTypeNamePrefixs());
//            whereContent.andNotEqual(PplStockSupplyReqDO::getDemandType, PplDemandTypeEnum.RETURN.getCode());
            if (!"CBS".equals(resourceType)) {
                whereContent.andEqual(PplStockSupplyReqDO::getType, "CVM");
            } else {
                whereContent.andEqual(PplStockSupplyReqDO::getType, "CBS");
            }
        }
        return whereContent;
    }


    /**
     * cvm 的导出
     */
    @Override
    public FileNameAndBytesDTO exportPplItemExcel(ExportVersionPplOrStockReq req) {
        VersionGroupItemResp versionGroupItemResp = pplVersionGroupService.queryVersionGroupItemByVersionCode(
                req.getVersionCode());
        ExportPplVersionItemReq exportReq = new ExportPplVersionItemReq();
        exportReq.setData(versionGroupItemResp.getPplItems());
        exportReq.setFileName(req.getVersionCode() + "-13周PPL数据导出");
        exportReq.setExcelTemplateName("version_ppl_export.xlsx");
        // 对冲时数据导出暂时先不导出GPU类型的
        exportReq.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        return pplVersionGroupService.exportPplVersionItemExcel(exportReq);
    }

    /**
     * cvm 的导出
     */
    @Override
    public FileNameAndBytesDTO exportStockItemExcel(ExportVersionPplOrStockReq req) {
        VersionGroupItemResp versionGroupItemResp = pplVersionGroupService.queryVersionGroupItemByVersionCode(
                req.getVersionCode());
        ExportPplVersionItemReq exportReq = new ExportPplVersionItemReq();
        exportReq.setData(versionGroupItemResp.getPplItems());
        exportReq.setFileName(req.getVersionCode() + "-13周供应方案数据导出");
        exportReq.setExcelTemplateName("version_stock_export.xlsx");
        // 对冲时数据导出暂时先不导出GPU类型的
        exportReq.setProduct(Ppl13weekProductTypeEnum.CVM.getName());
        return pplVersionGroupService.exportPplVersionItemExcelWithStockSupply(exportReq);
    }

    @Override
    public List<PplStockSupplyReqDO> queryStockReq(VersionGroupStatReq req, Long groupId, PplStockSupplyDO supplyDO) {
        if (supplyDO == null) {
            return new ArrayList<>();
        }
        //查询每个group关联的pplId
        List<String> pplIds = queryPplIdList(groupId);
        if (CollectionUtils.isEmpty(pplIds)) {
            return new ArrayList<>();
        }
        //再根据supplyId查询出关联的所有req
        WhereContent reqWhereContent = new WhereContent();
        reqWhereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getPplId, pplIds);
        reqWhereContent.andEqual(PplStockSupplyReqDO::getSupplyId, supplyDO.getId());
        if (!CollectionUtils.isEmpty(req.getCustomerShortNames())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getCustomerShortName, req.getCustomerShortNames());
        }
        if (!CollectionUtils.isEmpty(req.getRegionNames())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getRegionName, req.getRegionNames());
        }
        if (!CollectionUtils.isEmpty(req.getWarZoneNames())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getWarZone, req.getWarZoneNames());
        }
        if (!CollectionUtils.isEmpty(req.getDemandScenes())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getDemandScene, req.getDemandScenes());
        }
        if (!CollectionUtils.isEmpty(req.getInstanceTypes())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getInstanceType, req.getInstanceTypes());
        }
        if (!CollectionUtils.isEmpty(req.getCustomerUins())) {
            reqWhereContent.andIn(PplStockSupplyReqDO::getCustomerUin, req.getCustomerUins());
        }
        return demandDBHelper.getAll(PplStockSupplyReqDO.class, reqWhereContent.getSql(), reqWhereContent.getParams());
    }

    @Override
//    @TaskLog(taskName = "campusCheck")
    public void campusCheck() {
        String sql = ORMUtils.getSql("/sql/ppl13week/campus_check.sql");
        List<String> nullCampusList = demandDBHelper.getRaw(String.class, sql);
        if (ListUtils.isNotEmpty(nullCampusList)) {
            BizException e = BizException.makeThrow("部分地域无默认可用区配置,region_name: %s",
                    JSON.toJson(nullCampusList));
            alert.sendMail("<EMAIL>;<EMAIL>;<EMAIL>",
                    "默认可用区配置缺失提示",
                    e.getMessage());
            throw e;
        }
    }

    @Override
    public QueryPplStockSupplySummaryRsp querySupplySummary(QueryPplSupplyReq req) {

        PplVersionDO versionDO = pplVersionService.getByVersionCode(req.getVersionCode());
        if (versionDO == null) {
            throw BizException.makeThrow("版本不存在 {}", req.getVersionCode());
        }

        List<PplSummaryDto> summaryDtos = new ArrayList<>();
        val versionBeginDate = LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);
        val versionEndDate = LocalDate.of(versionDO.getDemandEndYear(), versionDO.getDemandEndMonth(), 1).plusMonths(1);
        WhereContent cnd = new WhereContent();
        List<Integer> ids = getVersionGroupRecord(req.getVersionCode());
        cnd.andInIfValueNotEmpty("version_group_record_id", ids);
        cnd.andInIfValueNotEmpty("product", req.getProduct());
        cnd.addAnd("instance_num > 0"); // 干预过的数据会被改成0
        cnd.addAnd("deleted =0");
        cnd.addAnd("is_comd=0");
        cnd.addAnd("begin_buy_date >= ?", versionBeginDate);

        //需求数据
        String pplSql = "select  DATE_FORMAT(begin_buy_date ,'%Y-%m') as `year_month`,\n"
                + "instance_type,region_name,demand_type,sum(total_core) as forecast_demand, \n"
                + "sum(CASE WHEN status = 'APPLIED' THEN total_core ELSE 0 END) as forecast_applied, \n"
                + "sum(CASE WHEN status = 'VALID' THEN total_core ELSE 0 END) as forecast_valid, \n"
                + "SUM(CASE WHEN is_lock = 1 THEN total_core ELSE 0 END) as apply_lock_core, \n"
                + "(sum(CASE WHEN status = 'APPLIED' THEN total_core ELSE 0 END) - "
                + " sum(CASE WHEN is_lock = 1 THEN total_core ELSE 0 END)) as apply_gap \n"
                + "from  ppl_version_group_record_item \n"
                + cnd.getSql()
                + "\ngroup by DATE_FORMAT(begin_buy_date ,'%Y-%m') ,instance_type,region_name,demand_type;";
        val ls = demandDBHelper.getRaw(PplSummaryDto.class, pplSql, cnd.getParams());

        log.info("ppl :{}", ls);

        summaryDtos.addAll(ls);
        //预约数据
//        String applySql = "select `year_month` ,instance_type,region_name,demand_type, \n"
//                + "apply_core, \n"
//                + "apply_lock_core, \n"
//                + "apply_core - apply_lock_core as apply_gap  \n"
//                + "from  ppl_applied_supply \n"
//                + "where apply_gap_status=1 and  version_code=? and product in (?) \n"
//                + "group by `year_month` ,instance_type,region_name,demand_type";
//        val apply = demandDBHelper.getRaw(PplSummaryDto.class, applySql, req.getVersionCode(), req.getProduct());
//        log.info("apply :{}", apply);
//        summaryDtos.addAll(apply);

        Integer tk = demandDBHelper.getRawOne(Integer.class, "select id from  ppl_stock_supply "
                + " where deleted =0 and version_code =?  order by id desc limit 1", req.getVersionCode());
        if (tk != null) {
            //对冲数据-这个表全是CVM数据
            String supplySql = "select a.`year_month`,a.region_name ,a.instance_type ,demand_type ,\n"
                    + "SUM(if(match_type='SATISFY',p.instance_total_core,0)) satisfy,\n"
                    + "SUM(if(match_type='MOVE',p.instance_total_core,0)) move,\n"
                    + "SUM(if(match_type='BUY',p.instance_total_core,0)) buy,\n"
                    + "SUM(if(match_type='FAIL',p.instance_total_core,0)) fail,\n"
                    + "SUM(if(match_type='SUGGEST',p.instance_total_core,0)) suggest\n"
                    + "from ppl_stock_supply_req  a \n"
                    + "left join ppl_stock_supply_rsp p on a.id =p.req_id \n"
                    + "where a.supply_id =? and a.deleted =0   and p.deleted=0\n"
                    + "and a.`type` ='CVM'\n"
                    + "group by a.`year_month`,a.region_name ,a.instance_type ,a.demand_type";
            val supply = demandDBHelper.getRaw(PplSummaryDto.class, supplySql, tk);
            log.info("supply {}", supply);
            summaryDtos.addAll(supply);

            // 查询实际对冲量
            String deployDemandSql = "select a.`year_month`,a.region_name ,a.instance_type ,demand_type ,\n"
                    + "SUM(a.instance_total_core) deploy_demand \n"
                    + "from ppl_stock_supply_req  a \n"
                    + "where a.supply_id =? and a.deleted =0  \n"
                    + "and a.`type` ='CVM'\n"
                    + "group by a.`year_month`,a.region_name ,a.instance_type ,a.demand_type";
            val deployDemandData = demandDBHelper.getRaw(PplSummaryDto.class, deployDemandSql, tk);
            summaryDtos.addAll(deployDemandData);
        }

        //summaryDtos  按照 yearMonth regionName instanceType 聚合 并转化为 QueryPplStockSupplySummaryRsp

        val items = summaryDtos.stream()
                .collect(Collectors.groupingBy(
                        s -> new Tuple4<>(s.getYearMonth(),
                                s.getRegionName(),
                                s.getInstanceType(),
                                s.getDemandType())))
                .entrySet().stream().map(tuple3ListEntry -> {
                    SummaryItem item = new SummaryItem();
                    item.setYearMonth(tuple3ListEntry.getKey()._1);
                    item.setRegionName(tuple3ListEntry.getKey()._2);
                    item.setInstanceType(tuple3ListEntry.getKey()._3);
                    item.setDemandType(tuple3ListEntry.getKey()._4);
                    item.setForecastDemand(
                            NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getForecastDemand));
                    item.setForecastApplied(
                            NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getForecastApplied));
                    item.setForecastValid(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getForecastValid));
                    // 等于预测的预约量
                    item.setAppliedDemand(
                            NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getForecastApplied));
                    item.setAppliedLock(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getAppliedLock));
                    item.setAppliedGap(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getAppliedGap));
                    item.setDeployDemand(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getDeployDemand));
                    item.setStockBuy(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getStockBuy));
                    item.setStockFail(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getStockFail));
                    item.setStockMove(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getStockMove));
                    item.setStockSatisfy(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getStockSatisfy));
                    item.setStockSuggest(NumberUtils.sum(tuple3ListEntry.getValue(), PplSummaryDto::getStockSuggest));
                    return item;
                }).collect(Collectors.toList());

        return new QueryPplStockSupplySummaryRsp(items);
    }


    @Override
    public PplStockSupplyDemandRsp querySupplyDemand(QueryPplSupplyReq req) {

        PplVersionDO versionDO = pplVersionService.getByVersionCode(req.getVersionCode());
        if (versionDO == null) {
            throw BizException.makeThrow("版本不存在 {}", req.getVersionCode());
        }
        val versionBeginDate = LocalDate.of(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth(), 1);
        val versionEndDate = LocalDate.of(versionDO.getDemandEndYear(), versionDO.getDemandEndMonth(), 1).plusMonths(1);
        WhereContent cnd = new WhereContent();
        List<Integer> ids = getVersionGroupRecord(req.getVersionCode());
        cnd.andInIfValueNotEmpty("version_group_record_id", ids);
        cnd.andInIfValueNotEmpty("product", req.getProduct());
        cnd.addAnd("instance_num > 0"); // 干预过的数据会被改成0
        cnd.addAnd("is_comd=0");
        cnd.addAnd("begin_buy_date >= ? ", versionBeginDate);

        val ls = demandDBHelper.getAll(PplRecordItemJoinOrderVO.class, cnd.getSql(), cnd.getParams());

        return PplStockSupplyDemandRsp.trans(ls);

    }

    @Override
    public PplStockSupplyApplyRsp querySupplyApplied(QueryPplSupplyReq req) {
        WhereContent cnd = new WhereContent();
        cnd.andEqual("version_code", req.getVersionCode());
        cnd.andIn("product", req.getProduct());
        List<String> notDisplay = Lists.newArrayList(
                YunxiaoOrderStatusEnum.CREATED.getName(),
                YunxiaoOrderStatusEnum.FINISHED.getName(),
                YunxiaoOrderStatusEnum.CANCELED.getName(),
                YunxiaoOrderStatusEnum.BAD_CANCELED.getName(),
                YunxiaoOrderStatusEnum.REJECTED.getName()
        );
        cnd.andNotIn("apply_status", notDisplay);
        val ls = demandDBHelper.getAll(PplAppliedSupplylDO.class, cnd.getSql(), cnd.getParams());
        PplStockSupplyApplyRsp result = PplStockSupplyApplyRsp.trans(ls);

        String where = "where id in ("
                + "select MAX(a.id)  from ppl_version_group_record_item a\n"
                + "left join ppl_version_group b on a.version_group_id = b.id \n"
                + "where a.deleted = 0 and b.deleted = 0 and b.version_code = ? and a.product in (?) \n"
                + "group by ppl_id"
                + ")";
        List<PplVersionGroupRecordItemDO> datas = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                where, req.getVersionCode(), req.getProduct());
        if (datas == null) {
            datas = new ArrayList<>();
        }
        Map<String, PplVersionGroupRecordItemDO> pplMap = ListUtils.toMap(datas,
                PplVersionGroupRecordItemDO::getPplId, item -> item);
        for (PplStockSupplyApplyRsp.ApplyItem datum : result.getData()) {
            if (datum == null) {
                continue;
            }
            PplVersionGroupRecordItemDO ppl = pplMap.get(datum.getPplId());
            int occupy = ppl == null || ppl.getOccupyOthersCoreNum() == null ? 0 : ppl.getOccupyOthersCoreNum();
            datum.setOccupyOthersNum(occupy);
            datum.setNoForecastCoreNum(datum.getApplyCore() == null ? 0 : datum.getApplyCore() - occupy);
        }
        return result;
    }

    @Override
    public PplStockSupplyResultRsp querySupplyResult(QueryPplSupplyReq req) {

        Integer tk = demandDBHelper.getRawOne(Integer.class, "select id from  ppl_stock_supply "
                + " where deleted = 0 and version_code = ?   order by id desc limit 1", req.getVersionCode());
        WhereContent pplItemWhere = new WhereContent();
        pplItemWhere.andIn(PplStockSupplyReqVO::getType, "CVM");
        pplItemWhere.andIn(PplStockSupplyReqVO::getSupplyId, tk);
        pplItemWhere.andIn(PplStockSupplyReqVO::getProduct, req.getProduct());

        List<Integer> ids = getVersionGroupRecord(req.getVersionCode());

        List<PplVersionGroupRecordItemDO> versionGroupRecordItemDOList =
                demandDBHelper
                        .getAll(PplVersionGroupRecordItemDO.class, "where version_group_record_id in (?)", ids);

        List<PplStockSupplyReqVO> all = ORMUtils.db(demandDBHelper).getAll(PplStockSupplyReqVO.class, pplItemWhere);
        return PplStockSupplyResultRsp.trans(all, versionGroupRecordItemDOList);
    }

    /**
     * CVM任务对冲完成回调
     *
     * @param stockSupply
     */
    public void callBackOnSupply(PplStockSupplyDO stockSupply) {
        try {
            WhereContent apply = new WhereContent();
            apply.andEqual("version_code", stockSupply.getVersionCode());
            val ls = demandDBHelper.getAll(PplAppliedSupplylDO.class, apply.getSql(), apply.getParams());
            if (!ls.isEmpty()) {
                WhereContent cnd = new WhereContent();
                cnd.andEqual("supply_id", stockSupply.getId());
                Map<String, List<PplStockSupplyReqVO>> rsMap = demandDBHelper.getAll(PplStockSupplyReqVO.class,
                                cnd.getSql(), cnd.getParams())
                        .stream().collect(Collectors.groupingBy(PplStockSupplyReqVO::getPplId));

                List<PplAppliedSupplylDO> callBackLs =
                        ls.stream().filter(item -> rsMap.containsKey(item.getPplId())).collect(Collectors.toList());

                callBackLs.forEach(item -> {
                    List<PplStockSupplyReqVO> rspLs = rsMap.get(item.getPplId());
                    if (rspLs != null) {
                        rspLs.stream()
                                .flatMap(l -> l.getPplStockSupplyRsp().stream())
                                .collect(Collectors.groupingBy(PplStockSupplyRspDO::getMatchType))
                                .entrySet()
                                .forEach(rt -> {
                                    Integer total = NumberUtils.sum(rt.getValue(),
                                            PplStockSupplyRspDO::getInstanceTotalCore).intValue();
                                    Integer num = NumberUtils.sum(rt.getValue(),
                                            PplStockSupplyRspDO::getInstanceNum).intValue();
                                    switch (PplStockSupplyMatchTypeEnum.getByCode(rt.getKey())) {
                                        case SATISFY:
                                            item.setMatchSatisfyCore(total);
                                            item.setMatchSatisfyNum(num);
                                            break;
                                        case MOVE:
                                            item.setMatchMoveCore(total);
                                            item.setMatchMoveNum(num);
                                            break;
                                        case BUY:
                                            item.setMatchBuyCore(total);
                                            item.setMatchBuyNum(num);
                                            break;
                                        case FAIL:
                                            item.setMatchFailCore(total);
                                            item.setMatchFailNum(num);
                                            break;
                                        case SUGGEST:
                                            item.setMatchSuggestCore(total);
                                            item.setMatchSuggestNum(num);
                                            break;
                                        default:
                                    }
                                });
                    }
                    item.setDeployFlag(2);
                });
                demandDBHelper.update(callBackLs);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            alert.sendRtx("illyasu", "cvm对冲完成回写失败", ex.getMessage());
        }
    }

    public List<String> queryPplIdList(Long groupId) {
        WhereContent orderWhereContent = new WhereContent();
        orderWhereContent.andEqual(PplVersionGroupRecordItemDO::getVersionGroupId, groupId);
        List<PplVersionGroupRecordItemDO> recordItemDOS = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                orderWhereContent.getSql(), orderWhereContent.getParams());
        if (CollectionUtils.isEmpty(recordItemDOS)) {
            return Collections.EMPTY_LIST;
        }
        PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO = recordItemDOS.stream()
                .max(Comparator.comparing(PplVersionGroupRecordItemDO::getRecordVersion)).get();
        return recordItemDOS.stream()
                .filter(r -> Objects.equals(r.getRecordVersion(), pplVersionGroupRecordItemDO.getRecordVersion()))
                .map(v -> v.getPplId()).collect(Collectors.toList());
    }

    private void addDictData(QueryStockSupplyGroupViewRsp resp, Long latestRecordId) {
        // 3.1 字典接口
        String dictSql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_item_dict.sql");
        demandDBHelper.executeRaw("SET SESSION group_concat_max_len = 1024000");
        PplVersionGroupItemDictVO dict = demandDBHelper.getRawOne(PplVersionGroupItemDictVO.class, dictSql,
                latestRecordId);
        resp.setRegionName(dict.getRegionNameDict());
        resp.setWarZone(dict.getWarZoneDict());
        resp.setInstanceType(dict.getInstanceTypeDict());
        resp.setDemandScene(dict.getDemandSceneDict());
        resp.setCustomerShortName(dict.getCustomerShortNameDict());
        resp.setCustomerUin(dict.getCustomerUinDict());
    }

    private void fillTotalForDebug(QueryStockSupplyGroupViewRsp resp, List<QueryStockSupplyGroupViewRsp.Item> ret) {
        resp.setTotalDemand(ListUtils.sum(ret, QueryStockSupplyGroupViewRsp.Item::getDemandTotal));

        resp.setNewDemandTotal(ListUtils.sum(ret, (o) -> {
            if (Strings.equals(PplDemandTypeEnum.NEW.getCode(), o.getDemandType())) {
                return o.getDemandTotal();
            }
            return 0;
        }));
        resp.setElasticDemandTotal(ListUtils.sum(ret, (o) -> {
            if (Strings.equals(PplDemandTypeEnum.ELASTIC.getCode(), o.getDemandType())) {
                return o.getDemandTotal();
            }
            return 0;
        }));
        resp.setReturnDemandTotal(ListUtils.sum(ret, (o) -> {
            if (Strings.equals(PplDemandTypeEnum.RETURN.getCode(), o.getDemandType())) {
                return o.getDemandTotal();
            }
            return 0;
        }));
    }

    private void addReturnData(List<QueryStockSupplyGroupViewRsp.Item> ret, WhereContent whereContent,
            String resourceType, List<String> allYearMonths) {

        WhereContent retWhere = new WhereContent(whereContent);
        retWhere.andEqual(PplStockSupplyReqDO::getDemandType, PplDemandTypeEnum.RETURN.getCode());
        List<PplStockSupplyReqVO> all = demandDb().getAll(PplStockSupplyReqVO.class, retWhere);

//        getDickData(ret, latestVersion);

        Function<PplStockSupplyReqVO, String> cvmGroupKey = (PplStockSupplyReqVO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getInstanceTypeNamePrefix(), o.getInstanceType());

        Function<PplStockSupplyReqVO, String> cbsGroupKey = (PplStockSupplyReqVO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getDiskTypeName());

        Map<String, List<PplStockSupplyReqVO>> groupByData = ListUtils.groupBy(all, (o) -> {
            if (Strings.equals(resourceType, "CBS")) {
                return cbsGroupKey.apply(o);
            }
            return cvmGroupKey.apply(o);
        });

        /**
         * 这2个字段全部放到第一行，第一行最长
         */

        BaseDataDTO<MainInstanceTypeDTO> mainInstanceType = pplDictService.queryMainInstanceType();
        Map<String, Object> mainInstanceTypeMap = ListUtils.toMap(mainInstanceType.getData(),
                MainInstanceTypeDTO::getInstanceFamily, o -> o);

        for (String key : groupByData.keySet()) {
            List<PplStockSupplyReqVO> oneData = groupByData.get(key);

            PplStockSupplyReqVO demandData = oneData.get(0);

            QueryStockSupplyGroupViewRsp.Item item = new QueryStockSupplyGroupViewRsp.Item();
            item.setDemandRegionName(demandData.getRegionName());
            item.setDemandCountryName(demandData.getCountryName());
            if (Strings.equals("CBS", resourceType)) {
                item.setDemandInstanceClassName(demandData.getDiskTypeName());
                item.setDemandInstanceType(demandData.getDiskTypeName());
            } else {
                item.setDemandInstanceClassName(demandData.getInstanceTypeNamePrefix());
                item.setDemandInstanceType(demandData.getInstanceType());
                item.setDemandIsRecommendedInstanceType(mainInstanceTypeMap.containsKey(item.getDemandInstanceType()));
            }
            item.setDemandTotal(-getStockDataByResourceType(resourceType, oneData).intValue());

            Map<String, List<PplStockSupplyReqVO>> yearMonthGroupBy = ListUtils.groupBy(oneData,
                    PplStockSupplyReqDO::getYearMonth);
            List<YearMonthData> yearMonthRet = Lang.list();
            List<String> ymTmp = ListUtils.transform(allYearMonths, (o) -> o);
            for (String yearMonth : yearMonthGroupBy.keySet()) {
                ymTmp.remove(yearMonth);
                List<PplStockSupplyReqVO> yearMonthDetail = yearMonthGroupBy.get(yearMonth);
                yearMonthRet.add(new YearMonthData(yearMonth,
                        BigDecimal.ZERO.subtract(getStockDataByResourceType(resourceType, yearMonthDetail))));
            }
            for (String s : ymTmp) {
                yearMonthRet.add(new YearMonthData(s, BigDecimal.ZERO));
            }
            ListUtils.sortAscNullLast(yearMonthRet, YearMonthData::getName);
            item.setDemandDetails(yearMonthRet);

            ret.add(item);
            //变成负数
//            item.setDemandTotal(-item.getDemandTotal());

            if (Lang.list(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode())
                    .contains(demandData.getDemandType())) {
                item.setDemandTypeShowName("新增/弹性");
            } else if (Strings.equals(PplDemandTypeEnum.RETURN.getCode(), demandData.getDemandType())) {
                item.setDemandTypeShowName("退回");
            } else {
                item.setDemandTypeShowName("未知");
            }
            item.setDemandType(demandData.getDemandType());
        }

    }


    private List<QueryStockSupplyGroupViewRsp.Item> getGpuPplGroupRecordItem(QueryStockSupplyGroupViewReq req,
            PplVersionGroupRecordDO latestRecord, boolean isReturn) {

        List<PplGpuGroupViewDTO> allItemData = getItemData(req, latestRecord, isReturn);

        Map<String, List<PplStockSupplyGpuDetailDO>> pplId2Stock = getStockData(req, allItemData);

        Function<PplGpuGroupViewDTO, String> cvmGroupKey = (PplGpuGroupViewDTO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getInstanceTypeNamePrefix(), o.getInstanceType());

        Map<String, List<PplGpuGroupViewDTO>> itemGroupData = ListUtils.groupBy(allItemData, cvmGroupKey);

        List<String> allYearMonths = getAllYearMonth(req, allItemData);
        Map<String, StockSupplyData> allMatchType = new HashMap<>();

        BaseDataDTO<MainInstanceTypeDTO> mainInstanceType = pplDictService.queryMainInstanceType();
        Map<String, Object> mainInstanceTypeMap = ListUtils.toMap(mainInstanceType.getData(),
                MainInstanceTypeDTO::getInstanceFamily, o -> o);

        List<QueryStockSupplyGroupViewRsp.Item> ret = Lang.list();
        for (String key : itemGroupData.keySet()) {
            List<PplGpuGroupViewDTO> oneItemGroupData = itemGroupData.get(key);

            PplGpuGroupViewDTO demandData = oneItemGroupData.get(0);

            QueryStockSupplyGroupViewRsp.Item item = new QueryStockSupplyGroupViewRsp.Item();
            ret.add(item);

            setDemandColumn(mainInstanceTypeMap, demandData, item);

            int demandTotal = NumberUtils.sum(oneItemGroupData, PplGpuGroupViewDTO::getTotalGpuNum).intValue();
            item.setDemandTotal(isReturn ? -demandTotal : demandTotal);

            setDemandDetail(isReturn, allYearMonths, oneItemGroupData, item);
            setDeamandType(demandData, item);

            // 加上对冲数据
            List<String> allPplIds = ListUtils.transform(oneItemGroupData, PplGpuGroupViewDTO::getPplId);
            Map<String, PplGpuGroupViewDTO> pplId2Item = ListUtils.toMap(oneItemGroupData, (o) -> o.getPplId(),
                    Function.identity());

            // 从 item 里面去 yearMonth
//            List<PplStockSupplyGpuDetailDO> stockData = allPplIds.stream().map((o) -> {
//                PplGpuGroupViewDTO pplGpuGroupViewDTO = pplId2Item.get(o);
//                List<PplStockSupplyGpuDetailDO> i = pplId2Stock.get(o);
//                for (PplStockSupplyGpuDetailDO e : i) {
//                    e.setYearMonth(pplGpuGroupViewDTO.getYearMonth());
//                }
//                return i;
//            }).flatMap(Collection::stream).collect(Collectors.toList());

            List<PplStockSupplyGpuDetailDO> stockData = transform(allPplIds, (o) -> {
                PplGpuGroupViewDTO pplGpuGroupViewDTO = pplId2Item.get(o);
                List<PplStockSupplyGpuDetailDO> i = pplId2Stock.get(o);
                //可能有一些没有对冲数据
                if (i == null) {
                    return Lang.list();
                }
                for (PplStockSupplyGpuDetailDO e : i) {
                    e.setYearMonth(pplGpuGroupViewDTO.getYearMonth());
                }
                return i;
            });

            Map<String, List<PplStockSupplyGpuDetailDO>> matchTypeGroupBy = ListUtils.groupBy(stockData,
                    PplStockSupplyGpuDetailDO::getMatchType);

            List<StockSupplyData> stockSupplyDatas = Lang.list();
            for (String matchTypeCode : matchTypeGroupBy.keySet()) {

                StockSupplyData oneStockSupplyData = new StockSupplyData();
                oneStockSupplyData.setCode(matchTypeCode);
                oneStockSupplyData.setName(
                        PplStockSupplyMatchTypeEnum.getNameOrDefault(matchTypeCode, "未知类型:" + matchTypeCode));
                String columnName = PplStockSupplyMatchTypeEnum.getCvmColumnName(matchTypeCode);
                oneStockSupplyData.setInstanceTypeColumnName(columnName);

                StockSupplyData copyOne = new StockSupplyData();
                BeanUtils.copyProperties(oneStockSupplyData, copyOne);
                allMatchType.put(oneStockSupplyData.getName(), copyOne);

                List<PplStockSupplyGpuDetailDO> pplStockSupplyRspDOS = matchTypeGroupBy.get(matchTypeCode);
                // instanceType yearMonth hostNum/core
                List<Tuple3<String, String, BigDecimal>> typeYearMonthNum = ListUtils.transform(pplStockSupplyRspDOS,
                        (o) -> {
                            if (Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getCode(), matchTypeCode)) {
                                o.handleHostType();
                                return Tuple.of(o.getHostType(), o.getYearMonth(), o.getGpuNum());

                            }
                            if (Lang.list(PplStockSupplyMatchTypeEnum.MOVE.getCode(),
                                    PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                                    PplStockSupplyMatchTypeEnum.FAIL.getCode()).contains(matchTypeCode)) {
                                return Tuple.of(o.getMatchInstanceType(), o.getYearMonth(), o.getGpuNum());
                            }

                            return Tuple.of("未知分类", o.getYearMonth(), o.getGpuNum());

                        });
                oneStockSupplyData.setDetails(getDetails(typeYearMonthNum, mainInstanceTypeMap, allYearMonths));
                stockSupplyDatas.add(oneStockSupplyData);
            }

            // 库存满足 和 采购满足 如果没有自动加上
            addColumnIfNotExit(stockSupplyDatas, "CVM");
            item.setStockSupplyData(stockSupplyDatas);
        }

        if (Lang.isNotEmpty(ret)) {
            List<StockSupplyData> stockSupplyData = ret.get(0).getStockSupplyData();
            for (String matchType : allMatchType.keySet()) {
                if (!ListUtils.contains(stockSupplyData, (o) -> Strings.equals(matchType, o.getName()))) {
                    stockSupplyData.add(allMatchType.get(matchType));
                }
            }
            SortingField<StockSupplyData, Boolean> first = new SortingField<StockSupplyData, Boolean>(
                    SortingOrderEnum.DESC) {
                @Override
                public Boolean apply(StockSupplyData item) {
                    return Strings.equals(PplStockSupplyMatchTypeEnum.SATISFY.getName(), item.getName());
                }
            };
            SortingField<StockSupplyData, Boolean> second = new SortingField<StockSupplyData, Boolean>(
                    SortingOrderEnum.DESC) {
                @Override
                public Boolean apply(StockSupplyData item) {
                    return Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getName(), item.getName());
                }
            };
            SortingUtils.sort(stockSupplyData, first, second);
        }

        return ret;
    }

    public static <T, R> List<R> transform(Collection<T> list,
            Function<? super T, ? extends Collection<? extends R>> mapper) {
        return list == null ? new ArrayList<>()
                : list.stream().flatMap(item -> mapper.apply(item).stream()).collect(Collectors.toList());
    }

    private void setDemandDetail(boolean isReturn, List<String> allYearMonths,
            List<PplGpuGroupViewDTO> oneItemGroupData, QueryStockSupplyGroupViewRsp.Item item) {
        Map<String, List<PplGpuGroupViewDTO>> yearMonthGroupBy = ListUtils.groupBy(oneItemGroupData,
                PplGpuGroupViewDTO::getYearMonth);
        List<YearMonthData> yearMonthRet = Lang.list();
        List<String> ymTmp = ListUtils.transform(allYearMonths, (o) -> o);
        for (String yearMonth : yearMonthGroupBy.keySet()) {
            ymTmp.remove(yearMonth);
            List<PplGpuGroupViewDTO> yearMonthDetail = yearMonthGroupBy.get(yearMonth);
            BigDecimal oneDemandNum = NumberUtils.sum(yearMonthDetail, PplGpuGroupViewDTO::getTotalGpuNum);
            oneDemandNum = isReturn ? BigDecimal.ZERO.subtract(oneDemandNum) : oneDemandNum;
            yearMonthRet.add(new YearMonthData(yearMonth, oneDemandNum));
        }
        for (String s : ymTmp) {
            yearMonthRet.add(new YearMonthData(s, BigDecimal.ZERO));
        }
        ListUtils.sortAscNullLast(yearMonthRet, YearMonthData::getName);
        item.setDemandDetails(yearMonthRet);
    }

    private void setDeamandType(PplGpuGroupViewDTO demandData, QueryStockSupplyGroupViewRsp.Item item) {
        if (Lang.list(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode())
                .contains(demandData.getDemandType())) {
            item.setDemandTypeShowName("新增/弹性");
        } else if (Strings.equals(PplDemandTypeEnum.RETURN.getCode(), demandData.getDemandType())) {
            item.setDemandTypeShowName("退回");
        } else {
            item.setDemandTypeShowName("未知");
        }
        item.setDemandType(demandData.getDemandType());
    }

    private void setDemandColumn(Map<String, Object> mainInstanceTypeMap, PplGpuGroupViewDTO demandData,
            QueryStockSupplyGroupViewRsp.Item item) {
        item.setDemandRegionName(demandData.getRegionName());
        item.setDemandCountryName(demandData.getCountryName());

        item.setDemandInstanceClassName(demandData.getInstanceTypeNamePrefix());
        item.setDemandInstanceType(demandData.getInstanceType());
        item.setDemandIsRecommendedInstanceType(mainInstanceTypeMap.containsKey(item.getDemandInstanceType()));
    }

    @NotNull
    private List<String> getAllYearMonth(QueryStockSupplyGroupViewReq req, List<PplGpuGroupViewDTO> allData) {
        List<String> allYearMonths = ListUtils.transform(allData, PplGpuGroupViewDTO::getYearMonth).stream().distinct()
                .collect(Collectors.toList());

        // 获取所有的年月
        PplVersionDO version = demandDBHelper.getOne(PplVersionDO.class, "where version_code=?", req.getVersionCode());
        if (version == null) {
            throw BizException.makeThrow("version 未找到");
        }
        List<cloud.demand.app.common.utils.DateUtils.YearMonth> yearMonths = cloud.demand.app.common.utils.DateUtils.listYearMonth(
                version.getDemandBeginYear(), version.getDemandBeginMonth(), version.getDemandEndYear(),
                version.getDemandEndMonth());
        allYearMonths.addAll(new ArrayList<>(
                ListUtils.transform(yearMonths, (o) -> String.format("%d-%02d", o.getYear(), o.getMonth()))));
        allYearMonths = allYearMonths.stream().distinct().sorted().collect(Collectors.toList());
        return allYearMonths;
    }

    private Map<String, List<PplStockSupplyGpuDetailDO>> getStockData(QueryStockSupplyGroupViewReq req,
            List<PplGpuGroupViewDTO> allData) {
        // 这里拿到所有的 pplId 去关联 对冲数据
        List<String> pplIds = ListUtils.transform(allData, PplGpuGroupViewDTO::getPplId);

        PplStockSupplyGpuDO gpuStockGpuDO = demandDBHelper.getOne(PplStockSupplyGpuDO.class,
                "where  version_code=? order by id desc", req.getVersionCode());

        if (gpuStockGpuDO == null) {
            return new HashMap<>();
        }
        WhereContent detailWhere = new WhereContent();
        detailWhere.andEqual(PplStockSupplyGpuDetailDO::getSupplyGpuId, gpuStockGpuDO.getId());
        detailWhere.andIn(PplStockSupplyGpuDetailDO::getPplId, pplIds);
        List<PplStockSupplyGpuDetailDO> allStockData = demandDb().getAll(PplStockSupplyGpuDetailDO.class, detailWhere);

        return ListUtils.groupBy(allStockData, PplStockSupplyGpuDetailDO::getPplId);
    }

    private List<PplGpuGroupViewDTO> getItemData(QueryStockSupplyGroupViewReq req, PplVersionGroupRecordDO latestRecord,
            boolean isReturn) {
        WhereContent whereContent = new WhereContent();
        {
            whereContent.andIn(PplVersionGroupRecordItemDO::getVersionGroupRecordId, latestRecord.getId());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getRegionName, req.getRegionNames());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getWarZone, req.getWarZoneNames());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getInstanceType, req.getInstanceTypes());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getDemandScene, req.getDemandScenes());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getCustomerUin, req.getCustomerUins());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getCustomerShortName, req.getCustomerShortNames());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getInstanceTypeNamePrefix,
//                    req.getInstanceTypeNamePrefixs());
            // 增加退回数据
            // not equal 0
            whereContent.andNotEqual(PplVersionGroupRecordItemDO::getInstanceNum, 0);
            if (isReturn) {
                whereContent.andEqual(PplVersionGroupRecordItemDO::getDemandType, PplDemandTypeEnum.RETURN.getCode());
            } else {
                whereContent.andNotEqual(PplVersionGroupRecordItemDO::getDemandType,
                        PplDemandTypeEnum.RETURN.getCode());
            }

            // 只查干预后的数据
            whereContent.andEqual(PplVersionGroupRecordItemDO::getIsComd, Boolean.FALSE);

        }
        List<PplVersionGroupRecordItemWithOrderVO> all = demandDb().getAll(PplVersionGroupRecordItemWithOrderVO.class,
                whereContent);
        // 过滤， sql 不支持的在内存中过滤
        all = all.stream().filter((o) -> {
            if (Lang.isNotEmpty(req.getWarZoneNames())) {
                return req.getWarZoneNames().contains(o.getWarZone());
            }
            return true;
        }).filter((o) -> {
            if (Lang.isNotEmpty(req.getCustomerShortNames())) {
                return req.getCustomerShortNames().contains(o.getCustomerShortName());
            }
            return true;
        }).filter((o) -> {
            if (Lang.isNotEmpty(req.getCustomerUins())) {
                return req.getCustomerUins().contains(o.getCustomerUin());
            }
            return true;
        }).collect(Collectors.toList());

        List<PplGpuGroupViewDTO> allDataFinal = Lang.list();
        /*
         *  这里复用之前的逻辑
         */
        all.forEach((o) -> {
            PplStockSupplyReqDO pplStockSupplyReqDO = transCvmForQuery(o);
            if (pplStockSupplyReqDO == null){
                return;
            }
            // 多一层适配
            PplGpuGroupViewDTO pplGpuGroupViewDTO = transGpuForQuery(pplStockSupplyReqDO);
            pplGpuGroupViewDTO.setTotalGpuNum(o.getTotalGpuNum());
            allDataFinal.add(pplGpuGroupViewDTO);
        });

        List<PplGpuGroupViewDTO> allData = allDataFinal.stream().filter((o) -> {
            if (Lang.isNotEmpty(req.getInstanceTypeNamePrefixs())) {
                return req.getInstanceTypeNamePrefixs().contains(o.getInstanceTypeNamePrefix());
            }
            return true;
        }).collect(Collectors.toList());
        return allData;
    }

    private PplGpuGroupViewDTO transGpuForQuery(PplStockSupplyReqDO o) {
        PplGpuGroupViewDTO pplGpuGroupViewDTO = new PplGpuGroupViewDTO();
        pplGpuGroupViewDTO.setPplOrder(o.getPplOrder());
        pplGpuGroupViewDTO.setPplId(o.getPplId());
        pplGpuGroupViewDTO.setRecordVersion(o.getRecordVersion());
        pplGpuGroupViewDTO.setVersionGroupRecordId(o.getVersionGroupRecordId());
        pplGpuGroupViewDTO.setCustomerUin(o.getCustomerUin());
        pplGpuGroupViewDTO.setRegionName(o.getRegionName());
        pplGpuGroupViewDTO.setZoneName(o.getZoneName());
        pplGpuGroupViewDTO.setRegion(o.getRegion());
        pplGpuGroupViewDTO.setZone(o.getZone());
        pplGpuGroupViewDTO.setInstanceType(o.getInstanceType());
        pplGpuGroupViewDTO.setInstanceModel(o.getInstanceModel());
        pplGpuGroupViewDTO.setAlternativeInstanceType(o.getAlternativeInstanceType());
        pplGpuGroupViewDTO.setInstanceNum(o.getInstanceNum());
        pplGpuGroupViewDTO.setBeginBuyDate(o.getBeginBuyDate());
        pplGpuGroupViewDTO.setDemandType(o.getDemandType());
        pplGpuGroupViewDTO.setYearMonth(o.getYearMonth());
        pplGpuGroupViewDTO.setIndustryDept(o.getIndustryDept());
        pplGpuGroupViewDTO.setCustomerType(o.getCustomerType());
        pplGpuGroupViewDTO.setCustomerShortName(o.getCustomerShortName());
        pplGpuGroupViewDTO.setWarZone(o.getWarZone());
        pplGpuGroupViewDTO.setCountryName(o.getCountryName());
        pplGpuGroupViewDTO.setCampus(o.getCampus());
        pplGpuGroupViewDTO.setItemId(o.getItemId());
        pplGpuGroupViewDTO.setGroupRecordItemId(o.getGroupRecordItemId());
        pplGpuGroupViewDTO.setRecordItemJson(o.getRecordItemJson());
        pplGpuGroupViewDTO.setInstanceTypeNamePrefix(o.getInstanceTypeNamePrefix());
        pplGpuGroupViewDTO.setDemandScene(o.getDemandScene());
        pplGpuGroupViewDTO.setEndBuyDate(o.getEndBuyDate());
        pplGpuGroupViewDTO.setWinRate(o.getWinRate());
        pplGpuGroupViewDTO.setAppRole(o.getAppRole());
        return pplGpuGroupViewDTO;
    }


    private List<QueryStockSupplyGroupViewRsp.Item> getPplGroupRecordItem(QueryStockSupplyGroupViewReq req,
            PplVersionGroupRecordDO latestRecord, String resourceType, PplVersionDO version, boolean isReturn) {

        WhereContent whereContent = new WhereContent();
        {
            whereContent.andIn(PplVersionGroupRecordItemDO::getVersionGroupRecordId, latestRecord.getId());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getRegionName, req.getRegionNames());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getWarZone, req.getWarZoneNames());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getInstanceType, req.getInstanceTypes());
            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getDemandScene, req.getDemandScenes());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getCustomerUin, req.getCustomerUins());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getCustomerShortName, req.getCustomerShortNames());
//            whereContent.andInIfValueNotEmpty(PplVersionGroupRecordItemDO::getInstanceTypeNamePrefix,
//                    req.getInstanceTypeNamePrefixs());
            Date versionEndDate = cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonth(
                    cloud.demand.app.common.utils.DateUtils.getNextYearMonth(version.getDemandEndYear(),
                            version.getDemandEndMonth()));
            whereContent.andLT(PplVersionGroupRecordItemWithOrderVO::getBeginBuyDate, versionEndDate);
            // 增加退回数据
            // not equal 0
            whereContent.andNotEqual(PplVersionGroupRecordItemDO::getInstanceNum, 0);
            if (isReturn) {
                whereContent.andEqual(PplVersionGroupRecordItemDO::getDemandType, PplDemandTypeEnum.RETURN.getCode());
            } else {
                whereContent.andNotEqual(PplVersionGroupRecordItemDO::getDemandType,
                        PplDemandTypeEnum.RETURN.getCode());
            }
            // 只查干预后的数据
            whereContent.andEqual(PplVersionGroupRecordItemDO::getIsComd, Boolean.FALSE);

        }
        List<PplVersionGroupRecordItemWithOrderVO> all = demandDb().getAll(PplVersionGroupRecordItemWithOrderVO.class,
                whereContent);
        // 过滤， sql 不支持的在内存中过滤
        all = all.stream().filter((o) -> {
            if (Lang.isNotEmpty(req.getWarZoneNames())) {
                return req.getWarZoneNames().contains(o.getWarZone());
            }
            return true;
        }).filter((o) -> {
            if (Lang.isNotEmpty(req.getCustomerShortNames())) {
                return req.getCustomerShortNames().contains(o.getCustomerShortName());
            }
            return true;
        }).filter((o) -> {
            if (Lang.isNotEmpty(req.getCustomerUins())) {
                return req.getCustomerUins().contains(o.getCustomerUin());
            }
            return true;
        }).collect(Collectors.toList());

        List<PplStockSupplyReqDO> allDataFinal = Lang.list();

        if (!"CBS".equals(resourceType)) {
            all.forEach((o) -> {
                PplStockSupplyReqDO reqDO = transCvmForQuery(o);
                if (reqDO == null) {
                    return;
                }
                allDataFinal.add(transCvmForQuery(o));
            });
        } else {
            all.forEach((o) -> allDataFinal.addAll(transCbsForQuery(o)));
        }

        List<PplStockSupplyReqDO> allData = allDataFinal.stream().filter((o) -> {
            if (Lang.isNotEmpty(req.getInstanceTypeNamePrefixs())) {
                return req.getInstanceTypeNamePrefixs().contains(o.getInstanceTypeNamePrefix());
            }
            return true;
        }).collect(Collectors.toList());

        Function<PplStockSupplyReqDO, String> cvmGroupKey = (PplStockSupplyReqDO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getInstanceTypeNamePrefix(), o.getInstanceType());

        Function<PplStockSupplyReqDO, String> cbsGroupKey = (PplStockSupplyReqDO o) -> Strings.join("@",
                o.getCountryName(), o.getRegionName(), o.getDiskTypeName());

        Map<String, List<PplStockSupplyReqDO>> groupByData = ListUtils.groupBy(allData, (o) -> {
            if (Strings.equals(resourceType, "CBS")) {
                return cbsGroupKey.apply(o);
            }
            return cvmGroupKey.apply(o);
        });

        List<String> allYearMonths = ListUtils.transform(allData, PplStockSupplyReqDO::getYearMonth).stream().distinct()
                .collect(Collectors.toList());
        // 获取所有的年月
        List<cloud.demand.app.common.utils.DateUtils.YearMonth> yearMonths = cloud.demand.app.common.utils.DateUtils.listYearMonth(
                version.getDemandBeginYear(), version.getDemandBeginMonth(), version.getDemandEndYear(),
                version.getDemandEndMonth());
        allYearMonths.addAll(new ArrayList<>(
                ListUtils.transform(yearMonths, (o) -> String.format("%d-%02d", o.getYear(), o.getMonth()))));
        allYearMonths = allYearMonths.stream().distinct().sorted().collect(Collectors.toList());

        BaseDataDTO<MainInstanceTypeDTO> mainInstanceType = pplDictService.queryMainInstanceType();
        Map<String, Object> mainInstanceTypeMap = ListUtils.toMap(mainInstanceType.getData(),
                MainInstanceTypeDTO::getInstanceFamily, o -> o);

        List<QueryStockSupplyGroupViewRsp.Item> ret = Lang.list();
        for (String key : groupByData.keySet()) {
            List<PplStockSupplyReqDO> oneData = groupByData.get(key);

            PplStockSupplyReqDO demandData = oneData.get(0);

            QueryStockSupplyGroupViewRsp.Item item = new QueryStockSupplyGroupViewRsp.Item();
            item.setDemandRegionName(demandData.getRegionName());
            item.setDemandCountryName(demandData.getCountryName());
            if (Strings.equals("CBS", resourceType)) {
                item.setDemandInstanceClassName(demandData.getDiskTypeName());
                item.setDemandInstanceType(demandData.getDiskTypeName());
            } else {
                item.setDemandInstanceClassName(demandData.getInstanceTypeNamePrefix());
                item.setDemandInstanceType(demandData.getInstanceType());
                item.setDemandIsRecommendedInstanceType(mainInstanceTypeMap.containsKey(item.getDemandInstanceType()));
            }
            int demandTotal = getPredictDataByResourceType(resourceType, oneData).intValue();
            item.setDemandTotal(isReturn ? -demandTotal : demandTotal);

            Map<String, List<PplStockSupplyReqDO>> yearMonthGroupBy = ListUtils.groupBy(oneData,
                    PplStockSupplyReqDO::getYearMonth);
            List<YearMonthData> yearMonthRet = Lang.list();
            List<String> ymTmp = ListUtils.transform(allYearMonths, (o) -> o);
            for (String yearMonth : yearMonthGroupBy.keySet()) {
                ymTmp.remove(yearMonth);
                List<PplStockSupplyReqDO> yearMonthDetail = yearMonthGroupBy.get(yearMonth);

                BigDecimal oneDemandNum = getPredictDataByResourceType(resourceType, yearMonthDetail);
                oneDemandNum = isReturn ? BigDecimal.ZERO.subtract(oneDemandNum) : oneDemandNum;
                yearMonthRet.add(new YearMonthData(yearMonth, oneDemandNum));
            }
            for (String s : ymTmp) {
                yearMonthRet.add(new YearMonthData(s, BigDecimal.ZERO));
            }
            ListUtils.sortAscNullLast(yearMonthRet, YearMonthData::getName);
            item.setDemandDetails(yearMonthRet);

            ret.add(item);
            if (Lang.list(PplDemandTypeEnum.NEW.getCode(), PplDemandTypeEnum.ELASTIC.getCode())
                    .contains(demandData.getDemandType())) {
                item.setDemandTypeShowName("新增/弹性");
            } else if (Strings.equals(PplDemandTypeEnum.RETURN.getCode(), demandData.getDemandType())) {
                item.setDemandTypeShowName("退回");
            } else {
                item.setDemandTypeShowName("未知");
            }
            item.setDemandType(demandData.getDemandType());
        }
        return ret;
    }

    private void addColumnIfNotExit(List<StockSupplyData> stockSupplyDatas, String type) {

        for (String stockCode : Lang.list(PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                PplStockSupplyMatchTypeEnum.BUY.getCode())) {
            boolean contains = ListUtils.contains(stockSupplyDatas,
                    (o) -> Strings.equals(o.getName(), PplStockSupplyMatchTypeEnum.getNameByCode(stockCode)));
            if (!contains) {
                StockSupplyData oneStockSupplyData = new StockSupplyData();
                oneStockSupplyData.setCode(stockCode);
                oneStockSupplyData.setName(PplStockSupplyMatchTypeEnum.getNameByCode(stockCode));
                if (Strings.equals("CVM", type)) {
                    oneStockSupplyData.setInstanceTypeColumnName(
                            PplStockSupplyMatchTypeEnum.getCvmColumnName(stockCode));
                } else {
                    if (Strings.equals(PplStockSupplyMatchTypeEnum.BUY.getCode(), stockCode)) {
                        oneStockSupplyData.setInstanceTypeColumnName("采购机型");
                    } else if (Lang.list(PplStockSupplyMatchTypeEnum.SATISFY.getCode(),
                            PplStockSupplyMatchTypeEnum.MOVE.getCode()).contains(stockCode)) {
                        oneStockSupplyData.setInstanceTypeColumnName("磁盘类型");
                    }
                }
                stockSupplyDatas.add(oneStockSupplyData);
            }
        }
    }

    private List<Detail> getDetails(List<Tuple3<String, String, BigDecimal>> typeYearMonthNum,
            Map<String, Object> mainInstanceTypeMap, List<String> yearMonths) {

        Map<String, Map<String, List<Tuple3<String, String, BigDecimal>>>> typeYearMonthNumGroupBy = groupBy(
                typeYearMonthNum, (o) -> o._1, (o) -> o._2);
        List<Detail> details = Lang.list();
        for (String instanceTypeKey : typeYearMonthNumGroupBy.keySet()) {

            Detail oneDetail = new Detail();
            oneDetail.setInstanceType(instanceTypeKey);
            oneDetail.setRecommendedInstanceType(mainInstanceTypeMap.containsKey(instanceTypeKey));
            oneDetail.setRecommendedInstanceType(false);
            Map<String, List<Tuple3<String, String, BigDecimal>>> yearMonthDetails = typeYearMonthNumGroupBy.get(
                    instanceTypeKey);
            List<String> ymTmp = ListUtils.transform(yearMonths, (o) -> o);
            List<YearMonthData> oneYearMonthData = ListUtils.transform(yearMonthDetails.keySet(), (o) -> {
                ymTmp.remove(o);
                return new YearMonthData(o, NumberUtils.sum(yearMonthDetails.get(o), (i) -> i._3));
            });
            for (String s : ymTmp) {
                oneYearMonthData.add(new YearMonthData(s, BigDecimal.ZERO));
            }
            oneDetail.setYearMonthDetails(oneYearMonthData);
            oneDetail.setTotal(NumberUtils.sum(oneYearMonthData, YearMonthData::getValue));
            details.add(oneDetail);
        }
        return details;
    }


    public static <T, k1, K2> Map<k1, Map<K2, List<T>>> groupBy(Collection<T> list,
            Function<? super T, ? extends k1> keyMapper1, Function<? super T, ? extends K2> keyMapper2) {
        Map<k1, Map<K2, List<T>>> ret = new HashMap<>();
        Map<? extends k1, List<T>> listMap = ListUtils.groupBy(list, keyMapper1);
        for (k1 k : listMap.keySet()) {
            Map<K2, List<T>> listMap1 = ListUtils.groupBy(listMap.get(k), keyMapper2);
            ret.put(k, listMap1);
        }
        return ret;
    }

    private BigDecimal getBigDecimal(Integer a) {
        return a == null ? BigDecimal.ZERO : new BigDecimal(a);
    }


    private BigDecimal getStockDataByResourceType(String type, List<PplStockSupplyReqVO> data) {
        return NumberUtils.sum(data, (o) -> {
            if ("CVM".equals(type)) {
                return o.getInstanceNum();
            } else if ("CORE".equals(type)) {
                return (o.getInstanceNum() == 0 || o.getInstanceNum() == null) ? 0 : o.getInstanceTotalCore();
            } else {
                return o.getDiskTotalSize();
            }
        });
    }

    private BigDecimal getApplyDataByResourceType(String type, List<PplStockSupplyReqVO> data) {
        return NumberUtils.sum(data, (o) -> {
            if ("CVM".equals(type)) {
                return o.getApplyInstanceNum();
            } else if ("CORE".equals(type)) {
                return o.getApplyTotalCore();
            } else {
                return null;
            }
        });
    }

    private BigDecimal getPredictDataByResourceType(String type, List<PplStockSupplyReqDO> data) {
        return NumberUtils.sum(data, (o) -> {
            if ("CVM".equals(type)) {
                return o.getInstanceNum();
            } else if ("CORE".equals(type)) {
                return (o.getInstanceNum() == null || o.getInstanceNum() == 0) ? 0
                        : o.getInstanceTotalCore();
            } else {
                return o.getDiskTotalSize();
            }
        });
    }

    private WhereContent getPplIdWhereContent(String pplIdStr) {
        WhereContent whereContent = new WhereContent();
        if (Strings.isNotBlank(pplIdStr)) {
            List<String> pplIds = Arrays.stream(StringUtils.trim(pplIdStr).split(";")).filter(Strings::isNotBlank)
                    .flatMap((o) -> Arrays.stream(o.split(","))).filter(Strings::isNotBlank)
                    .collect(Collectors.toList());
            whereContent.andInIfValueNotEmpty(PplStockSupplyReqDO::getPplId, pplIds);
        }
        return whereContent;
    }


}
