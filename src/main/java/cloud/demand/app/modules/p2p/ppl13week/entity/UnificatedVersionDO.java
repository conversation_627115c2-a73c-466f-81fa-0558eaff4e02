package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.json.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Data
@ToString
@Table("unificated_version")
@AllArgsConstructor
@NoArgsConstructor
public class UnificatedVersionDO extends BaseDO {

    @Column(value = "version_code")
    private String versionCode;

    @Column(value = "version_year_month")
    private String versionYearMonth;

    @Column(value = "version_week")
    private Integer versionWeek;

    @Column(value = "status")
    private String status;

    @Column(value = "create_user")
    private String createUser;

    @Column(value = "update_user")
    private String updateUser;

    @Column(value = "ppl_demand_year_month")
    private String pplDemandYearMonth;

    @Column(value = "product_demand_year_month")
    private String productDemandYearMonth;

    @Column(value = "year_demand_year_month")
    private String yearDemandYearMonth;

    @Column(value = "is_last_month_week")
    private Boolean isLastMonthWeek;

    @Column(value = "is_holiday_week")
    private Boolean isHolidayWeek;

    @Column(value = "is_three_model")
    private Boolean isThreeModel;

    @Column(value = "holiday_name")
    private String holidayName;

//    @Column(value = "version_start_time")
//    private Date versionStartTime;
//
//    @Column(value = "ppl_enter_deadline")
//    private Date pplEnterDeadline;
//
//    @Column(value = "stock_supply_deadline")
//    private Date stockSupplyDeadline;
//
//    @Column(value = "physical_enter_deadline")
//    private Date physicalEnterDeadline;
//
//    @Column(value = "year_demand_enter_deadline")
//    private Date yearDemandEnterDeadline;
//
//    @Column(value = "physical_pass_deadline")
//    private Date physicalPassDeadline;
//
//    @Column(value = "year_demand_pass_deadline")
//    private Date yearDemandPassDeadline;
//
//    @Column(value = "version_close_time")
//    private Date versionCloseTime;

    @Column(value = "remark")
    private String remark;

    public static DemandYearMonth parse2DemandYearMonth(String demandYearMonthStr) {
        return JSON.parse(demandYearMonthStr, DemandYearMonth.class);
    }


    @Data
    @ToString
    public static class DemandYearMonth {

        Integer beginYear;
        Integer beginMonth;
        Integer endYear;
        Integer endMonth;

        Integer overseasBeginYear;   //海外需求开始年

        Integer overseasBeginMonth;  //海外需求开始月

        public String getBeginYearMonth() {
            return this.beginYear + (this.beginMonth < 10 ? "-0" : "-") + this.beginMonth;
        }

        public String getOverseasBeginYearMonth() {
            if (this.getOverseasBeginYear() == null || this.getOverseasBeginMonth() == null){
                return getBeginYearMonth();
            }
            return this.getOverseasBeginYear() + (this.getOverseasBeginMonth() < 10 ? "-0" : "-") + this.getOverseasBeginMonth();
        }

        /**
         * 返回2024年6月～2024年12月
         *
         * @return
         */
        public String getChineseYearMonth() {
            return this.beginYear + "年" + this.beginMonth + "月 ～ " + this.endYear + "年" + this.endMonth + "月";
        }

        public String getEndYearMonth() {
            return this.endYear + (this.endMonth < 10 ? "-0" : "-") + this.endMonth;
        }

        public List<String> buildDemandYearMonth() {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
            YearMonth startYearMonth = YearMonth.parse(getBeginYearMonth(), formatter);
            YearMonth endYearMonth = YearMonth.parse(getEndYearMonth(), formatter);

            List<String> yearMonths = new ArrayList<>();
            while (!startYearMonth.isAfter(endYearMonth)) {
                yearMonths.add(startYearMonth.format(formatter));
                startYearMonth = startYearMonth.plusMonths(1);
            }
            return yearMonths;
        }

    }
}
