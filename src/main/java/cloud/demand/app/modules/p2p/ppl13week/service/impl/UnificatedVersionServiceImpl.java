package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.entity.erp_resplan.NebulaToErpForecastTaskInfoDO;
import cloud.demand.app.modules.common.enums.CrpEventEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.longterm.entity.LongTermUnifiedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionProgressResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.unificated_version.UnificatedVersionReq;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpBizTimeConfigDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.CrpCommonHolidayWeekDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionDO.DemandYearMonth;
import cloud.demand.app.modules.p2p.ppl13week.entity.UnificatedVersionEventDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.BizTimeGroupEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.UnificatedVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStdTableService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.UnificatedVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.http.DeviceDemandVersionService;
import cloud.demand.app.modules.p2p.product_demand.entity.ProductDemandVersionDO;
import cloud.demand.app.modules.p2p.product_demand.service.ProductDemandVersionService;
import cloud.demand.app.modules.physical_device.entity.ImportForecastTaskOrderDO;
import cloud.demand.app.modules.rrp_new_feature.service.AutoDeliveryDemandForecastService;
import cloud.demand.app.modules.rrp_new_feature.service.RealTimeDemandService;
import cloud.demand.app.modules.rrp_remake.entity.RrpConfigDO;
import cloud.demand.app.modules.rrp_remake.service.VersionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.tencent.rainbow.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnificatedVersionServiceImpl implements UnificatedVersionService {

    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper resplanDBHelper;
    @Resource
    private DictService dictService;
    @Resource
    private Alert alert;
    @Resource
    private PplStockSupplyService stockSupplyService;
    @Resource
    private PplVersionService pplVersionService;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private RealTimeDemandService realTimeDemandService;
    @Resource
    private PplVersionGroupService pplVersionGroupService;
    @Resource
    private VersionService versionService;
    @Resource
    private ProductDemandVersionService productDemandVersionService;
    @Resource
    private AutoDeliveryDemandForecastService autoDeliveryDemandForecastService;
    @Resource
    private PplStdTableService pplStdTableService;
    @Resource
    private DeviceDemandVersionService deviceDemandVersionService;

    static ExecutorService executor = Executors.newFixedThreadPool(1);

    private static Supplier<String> domainSupplier = DynamicProperty.create("app.product.domain", "");

    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    @Override
    public CrpCommonHolidayWeekDO queryNextWeek(LocalDate localDate) {
        CrpCommonHolidayWeekDO one = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class,
                "where start > ? order by id asc", localDate.toString());
        return one;
    }

    public CrpCommonHolidayWeekDO queryCurrentWeek(LocalDate localDate) {
        CrpCommonHolidayWeekDO one = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class,
                "where start <= ? and end >= ? ", localDate.toString(), localDate.toString());
        return one;
    }

    @Override
    public List<UnificatedVersionDTO> queryUnificatedVersionList(UnificatedVersionReq req) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqualIfValueNotEmpty(UnificatedVersionDTO::getVersionCode, req.getVersionCode());
        whereContent.andInIfValueNotEmpty(UnificatedVersionDTO::getStatus, req.getStatus());
        whereContent.andEqualIfValueNotEmpty(UnificatedVersionDTO::getUpdateUser, req.getUpdateUser());
        whereContent.orderDesc("id");
        List<UnificatedVersionDTO> all = demandDBHelper.getAll(UnificatedVersionDTO.class, whereContent.getSql(),
                whereContent.getParams());
        for (UnificatedVersionDTO unificatedVersionDTO : all) {
            // 将没有结束时间的事件过滤掉。
            if (ListUtils.isNotEmpty(unificatedVersionDTO.getEventList())) {
                unificatedVersionDTO.setEventList(
                        unificatedVersionDTO.getEventList().stream().filter(v -> v.getDeadline() != null)
                                .collect(Collectors.toList()));
            }
            unificatedVersionDTO.parseJsonDemand();
        }
        return all;
    }

    @Override
    public UnificatedVersionProgressResp queryUnificatedVersionProgress(Long id) {
        UnificatedVersionDTO unificatedVersionDTO = demandDBHelper.getByKey(UnificatedVersionDTO.class, id);
        if (unificatedVersionDTO == null) {
            throw new BizException("找不到当前版本");
        }
        UnificatedVersionProgressResp resp = new UnificatedVersionProgressResp();
        resp.setLastMonthWeek(unificatedVersionDTO.getIsLastMonthWeek());
        resp.buildPpl13WeekProgress(unificatedVersionDTO.getEventList());
        resp.buildDeviceDemandProgress(unificatedVersionDTO.getEventList());
//        resp.buildProduct13WeekProgress(unificatedVersionDTO.getEventList());
//        resp.buildProductFullYearProgress(unificatedVersionDTO.getEventList());
        return resp;
    }

    @Override
    public UnificatedVersionDTO getUnificatedVersionDTOById(Long id) {
        return demandDBHelper.getByKey(UnificatedVersionDTO.class, id);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void createUnificatedVersion() {
        // 查出最后一个版本 要生成的是最后一个版本时间后的版本。
        UnificatedVersionDTO lastVersion = demandDBHelper.getOne(UnificatedVersionDTO.class,
                "order by id desc limit 1");
        // 获取需求预测业务用户时间配置
        List<CrpBizTimeConfigDO> crpBizTimeConfigDOS = dictService.queryBizTimeConfigByGroup(
                BizTimeGroupEnum.DEMAND_FORECAST.getCode(), false);
        Map<String, CrpBizTimeConfigDO> nameToTimeConfig = crpBizTimeConfigDOS.stream()
                .collect(Collectors.toMap(CrpBizTimeConfigDO::getCode, v -> v));
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        // 查出本周
        CrpCommonHolidayWeekDO currentWeek = queryCurrentWeek(
                DateUtils.dateToLocalDate(lastVersion.getDeadlineByCode(CrpEventEnum.VERSION_CLOSE_TIME.getCode())));

        // 查出下周
        CrpCommonHolidayWeekDO nextWeek = queryNextWeek(
                DateUtils.dateToLocalDate(lastVersion.getDeadlineByCode(CrpEventEnum.VERSION_CLOSE_TIME.getCode())));
        Boolean isLastWeek = isLastWeekOfMonth(nextWeek);
        Boolean isOverseasCrossMonth = isOverseasCrossMonth(nextWeek);
        UnificatedVersionDO newVersion = new UnificatedVersionDO();
        newVersion.setIsLastMonthWeek(isLastWeek);
        newVersion.setIsThreeModel(false);
        newVersion.setVersionYearMonth(nextWeek.getYear() + "年" + DateUtils.fillZeroMonthOrDay(nextWeek.getMonth()) + "月");
        newVersion.setVersionWeek(nextWeek.getWeek());
        newVersion.setIsHolidayWeek(nextWeek.getIsHoliday());
        newVersion.setStatus(UnificatedVersionStatusEnum.NEW.getCode());
        newVersion.setCreateUser(userNameWithSystem);
        newVersion.setUpdateUser(userNameWithSystem);
        newVersion.setRemark(nextWeek.getReamrk());
        // 版本编码 取每个版本“物理机需求预测版本生效”所属的日期生成
        LocalDate physicalEnterDeadline = DateUtils.dateToLocalDate(dictService.getLocalDateByBizTimeWeek(currentWeek,
                nameToTimeConfig.get(CrpEventEnum.DEVICE_DEMAND_VERSION_PASS_DEADLINE.getCode())));

        newVersion.setVersionCode("V_" + physicalEnterDeadline.getYear()
                + DateUtils.fillZeroMonthOrDay(physicalEnterDeadline.getMonthValue())
                + DateUtils.fillZeroMonthOrDay(physicalEnterDeadline.getDayOfMonth()));
        UnificatedVersionDO one = demandDBHelper.getOne(UnificatedVersionDO.class, "where version_code = ?",
                newVersion.getVersionCode());
        if (one != null) {
            throw new BizException("已经存在版本号为 " + newVersion.getVersionCode(),
                    " 的版本，请联系 kaijiazhang 确认");
        }

        // 设置需求年月
        setDemandYearMonth(newVersion, nextWeek, isLastWeek,isOverseasCrossMonth);

        demandDBHelper.insert(newVersion);

        // 获取业务配置时间，并生成实际周时间
        List<UnificatedVersionEventDO> eventList = new ArrayList<>();
        try {
            eventList = buildEventList(newVersion, crpBizTimeConfigDOS, currentWeek);
        } catch (Exception e) {
            alert.sendRtx("oliverychen", "统一版本", "统一版本-获取业务配置时间失败，请检查。");
            throw new BizException("统一版本-获取业务配置时间失败，请检查。");
        }
        if (ListUtils.isNotEmpty(eventList)) {
            demandDBHelper.insertBatchWithoutReturnId(eventList);
        }
        if (newVersion.getIsHolidayWeek()) {
            dictService.eventNotice(CrpEventEnum.CHECK_HOLIDAY_VERSION.getCode(), null, null);
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void deleteUnificatedVersion(Long id) {
        UnificatedVersionDO unificatedVersionDO = demandDBHelper.getByKey(UnificatedVersionDO.class, id);
        LongTermUnifiedVersionDO longTermVersion = demandDBHelper.getOne(LongTermUnifiedVersionDO.class,
                " where relevant_ppl_version = ?", unificatedVersionDO.getVersionCode());
        if (longTermVersion != null){
            throw new BizException("当前版本已经被中长期版本 " + longTermVersion.getVersionCode() +" 关联 无法删除；"
                    + "如需删除，请先解除关联。");
        }

        if (!unificatedVersionDO.getStatus().equals(UnificatedVersionStatusEnum.NEW.getCode())) {
            throw new BizException("当前版本已经被使用，无法删除");
        }

        demandDBHelper.executeRaw("update unificated_version set deleted = 1 where deleted = 0 and id = ?", id);

        demandDBHelper.executeRaw(
                "update unificated_version_event set deleted = 1 where deleted = 0 and version_id = ?", id);
    }

    private Boolean isLastWeekOfMonth(CrpCommonHolidayWeekDO weekDO) {
        CrpCommonHolidayWeekDO nextWeek = demandDBHelper.getOne(CrpCommonHolidayWeekDO.class,
                "where id > ? order by id asc limit 1", weekDO.getId());
        if (!weekDO.getMonth().equals(nextWeek.getMonth())) {
            // 如果所属月份不相同，则当周为最后一周
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    private Boolean isOverseasCrossMonth(CrpCommonHolidayWeekDO weekDO) {
        List<CrpCommonHolidayWeekDO> all = demandDBHelper.getAll(CrpCommonHolidayWeekDO.class,
                "where id > ? order by id asc limit 3", weekDO.getId());
        if (!weekDO.getMonth().equals(all.get(0).getMonth())) {
            // 如果是当月最后一周 则海外和境内一起跨月， 海外不用单独跨月
            return Boolean.FALSE;
        }
        // 如果当前周和3周后周所属月份不相同，则当周为海外跨月周
        if (!weekDO.getMonth().equals(all.get(all.size()-1).getMonth())) {
            // 如果所属月份不相同，则当周为最后一周
            return Boolean.TRUE;
        }
        return Boolean.FALSE;

    }

    private void setDemandYearMonth(UnificatedVersionDO newVersion, CrpCommonHolidayWeekDO nextWeek,
            Boolean isLastWeek,Boolean isOverseasCrossMonth) {
        DemandYearMonth productYearMonth = new DemandYearMonth();
        DemandYearMonth pplYearMonth = new DemandYearMonth();
        // 设置步长 若不为最后一周 M+1 ～ M+3  若最后一周则为 M+2 ～ M+4
        Integer startFlag = isLastWeek ? 2 : 1;
        Integer endFlag = isLastWeek ? 4 : 3;
        int currentYear = nextWeek.getYear();
        int currentMonth = nextWeek.getMonth();
        Integer beginYear = (currentMonth + startFlag) <= 12 ? currentYear : currentYear + 1;
        Integer beginMonth =
                (currentMonth + startFlag) <= 12 ? (currentMonth + startFlag) : (currentMonth + startFlag) - 12;
        Integer endYear = (currentMonth + endFlag) <= 12 ? currentYear : currentYear + 1;
        Integer endMonth =
                (currentMonth + endFlag) <= 12 ? (currentMonth + endFlag) : (currentMonth + endFlag) - 12;
        productYearMonth.setBeginYear(beginYear);
        productYearMonth.setBeginMonth(beginMonth);
        productYearMonth.setEndYear(endYear);
        productYearMonth.setEndMonth(endMonth);
        productYearMonth.setOverseasBeginYear(beginYear);
        productYearMonth.setOverseasBeginMonth(beginMonth);
        newVersion.setProductDemandYearMonth(JsonUtil.prettyToString(productYearMonth));

        // 设置PPL需求年月
        BeanUtils.copyProperties(productYearMonth,pplYearMonth);
        if (isOverseasCrossMonth) {
            String nextYearMonth = DateUtils.getNextYearMonth(beginYear, beginMonth);
            pplYearMonth.setOverseasBeginYear(Integer.parseInt(nextYearMonth.split("-")[0]));
            pplYearMonth.setOverseasBeginMonth(Integer.parseInt(nextYearMonth.split("-")[1]));
        }
        newVersion.setPplDemandYearMonth(JsonUtil.prettyToString(pplYearMonth));
        //设置全年需求年月
        DemandYearMonth yearMonthForFullYear = new DemandYearMonth();
        yearMonthForFullYear.setBeginYear(beginYear);
        yearMonthForFullYear.setBeginMonth(1);
        yearMonthForFullYear.setEndYear(endYear + 1);
        yearMonthForFullYear.setEndMonth(12);
        yearMonthForFullYear.setOverseasBeginYear(beginYear);
        yearMonthForFullYear.setOverseasBeginMonth(1);
        newVersion.setYearDemandYearMonth(JsonUtil.prettyToString(yearMonthForFullYear));
    }

    private List<UnificatedVersionEventDO> buildEventList(UnificatedVersionDO newVersion,
            List<CrpBizTimeConfigDO> crpBizTimeConfigDOS,
            CrpCommonHolidayWeekDO currentWeek) {

        List<UnificatedVersionEventDO> list = new ArrayList<>();
        crpBizTimeConfigDOS.forEach(v -> {
            UnificatedVersionEventDO unificatedVersionEventDO = new UnificatedVersionEventDO();
            unificatedVersionEventDO.setVersionId(newVersion.getId());
            unificatedVersionEventDO.setEventCode(v.getCode());
            unificatedVersionEventDO.setEventName(v.getName());
            if (v.getTime() != null && v.getDayOfWeek() != null) {
                unificatedVersionEventDO.setDeadline(dictService.getLocalDateByBizTimeWeek(currentWeek, v));
            }
            unificatedVersionEventDO.setIsDone(Boolean.FALSE);
            list.add(unificatedVersionEventDO);
        });
        UnificatedVersionEventDO cvmStockSupply = new UnificatedVersionEventDO();
        cvmStockSupply.setVersionId(newVersion.getId());
        cvmStockSupply.setEventCode(CrpEventEnum.CVM_STOCK_SUPPLY_DONE.getCode());
        cvmStockSupply.setEventName(CrpEventEnum.CVM_STOCK_SUPPLY_DONE.getName());
        UnificatedVersionEventDO stockSupply = new UnificatedVersionEventDO();
        stockSupply.setVersionId(newVersion.getId());
        stockSupply.setEventCode(CrpEventEnum.STOCK_SUPPLY_DONE.getCode());
        stockSupply.setEventName(CrpEventEnum.STOCK_SUPPLY_DONE.getName());
        list.add(cvmStockSupply);
        list.add(stockSupply);
        return list;
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void saveUnificatedVersion(UnificatedVersionDTO unificatedVersionDTO) {
        checkAuthAndParams(unificatedVersionDTO);

        // 查一下数据库更新前对应的大版本
        UnificatedVersionDO oldVersion = demandDBHelper.getByKey(UnificatedVersionDO.class,
                unificatedVersionDTO.getId());
        // 如果不是未启动，不允许修改是否当月最后一周，因为会决定是否有全年的版本
        // 启动了和完成了都不允许修改这个字段
        if (!UnificatedVersionStatusEnum.NEW.getCode().equals(oldVersion.getStatus())) {
            if (oldVersion.getIsLastMonthWeek() != unificatedVersionDTO.getIsLastMonthWeek()) {
                throw new BizException("大版本已经启动，不允许修改是否为当月最后一周");
            }
            if (oldVersion.getIsThreeModel() != unificatedVersionDTO.getIsThreeModel()) {
                 throw new BizException("大版本已经启动，不允许修改是否为三观版本");
            }
        }

        demandDBHelper.update(unificatedVersionDTO);
        demandDBHelper.update(unificatedVersionDTO.getEventList());

        if (unificatedVersionDTO.getStatus().equals(UnificatedVersionStatusEnum.PROCESS.getCode())) {
            // 如果是进行中的版本 需要同步更新版本
            pplVersionService.updateVersionByUnificatedVersion(unificatedVersionDTO);
        }
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Transactional(value = "rrpTransactionManager")
    public void startUnificatedVersion(UnificatedVersionDTO version, UnificatedVersionEventDO event) {

//        // 将3个子版本启动
//        // 1.启动产品13周版本
//        RrpConfigDO rrpConfig = versionService.getRrpConfig(version.getVersionCode());
//        if (rrpConfig == null) {
//            throw new BizException(String.format("未找到版本编码%s对应的产品13周需求版本", version.getVersionCode()));
//        }
//        // 开启版本的用户需要有管理员权限
//        versionService.openOrCloseRrpConfig(rrpConfig.getId(), true, "AUTO_VERSION");

        // 2.开启demand库的事务 进行demand库的相关处理
        SpringUtil.getBean(UnificatedVersionService.class).startVersionForDemandVersion(version, event);

        // 3.发送消息通知
        sendOpenVersionMsg(version);
    }

    public void sendOpenVersionMsg(UnificatedVersionDTO unificatedVersionDTO) {
        String baseUrl = domainSupplier.get();

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        unificatedVersionDTO.parseJsonDemand();
        String pplDemand = unificatedVersionDTO.getPplDemand().getChineseYearMonth();
        String week = unificatedVersionDTO.getIsHolidayWeek() ? "常规周" : "节假周";
        Date pplDeadline = unificatedVersionDTO.getDeadlineByCode(CrpEventEnum.PPL_ENTER_DEADLINE.getCode());
        String pplDate = formatter.format(pplDeadline);
        String pplUrl = baseUrl + "/13ppl/view";
        String pplMsg =
                "您好，本周预测已开启，本周为" + week + "，13周范围为" + pplDemand + "，" + pplDate
                        + "将截止录入。(<a href='" + pplUrl + "'>更多信息请点击前往版本管理页面查看。</a>)";
        dictService.eventNotice(CrpEventEnum.PPL_VERSION_START_TIME.getCode(), null, pplMsg);

        // 通知业务全年版本开启，注：产品13周在开启版本的底层已经实现
//        String yearVersionDemand = unificatedVersionDTO.getYearDemand().getChineseYearMonth();
//        Date yearVersionDeadline = unificatedVersionDTO.getDeadlineByCode(
//                CrpEventEnum.YEAR_DEMAND_ENTET_DEADLINE.getCode());
//        String yearVersionDate = formatter.format(yearVersionDeadline);
//        String yearVersionUrl = baseUrl + "/product/create";
//        String yearVersionMsg =
//                "您好，本周预测已开启，本次全年预测填写范围为" + yearVersionDemand + "，" + yearVersionDate
//                        + "将截止录入。(<a href='" + yearVersionUrl + "'>点击前往预测录入页面。</a>)";
//        dictService.eventNotice(CrpEventEnum.YEAR_VERSION_START_TIME.getCode(), null, yearVersionMsg);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void startVersionForDemandVersion(UnificatedVersionDO version, UnificatedVersionEventDO event) {
        // 1.启动行业13周版本
        pplVersionService.startApproveByVersionCode(version.getVersionCode());

//        // 2.启动产品全年版本
//        ProductDemandVersionDO fullYearVersion = productDemandVersionService.getVersion(version.getVersionCode());
//        if (fullYearVersion == null) {
//            throw new BizException(
//                    String.format("未找到版本编码%s对应的产品全年需求版本", version.getVersionCode()));
//        }
//        productDemandVersionService.openOrCloseVersion(fullYearVersion.getId(), true);

        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(version.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_START.getCode()));

        event.setIsDone(Boolean.TRUE);
        event.setOperateUser(LoginUtils.getUserNameWithSystem());
        demandDBHelper.update(event);
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Transactional(value = "demandTransactionManager")
    public void ppl13WeekEnterDeadline(UnificatedVersionEventDO event) {
        pplVersionGroupService.checkVersionDeadline();

        // 正常走完的话 打上完成的标记
        event.setIsDone(Boolean.TRUE);
        event.setOperateUser(LoginUtils.getUserNameWithSystem());
        demandDBHelper.update(event);
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Transactional(value = "demandTransactionManager")
    public void startStockSupply(UnificatedVersionDO version, UnificatedVersionEventDO event) {
        // 检查当前版本是否有调用过
        stockSupplyService.startAllNewStockSupply(version.getVersionCode());

        // 正常走完的话 打上完成的标记
        event.setIsDone(Boolean.TRUE);
        event.setOperateUser(LoginUtils.getUserNameWithSystem());
        demandDBHelper.update(event);

        // 触发拼接宽表同步任务
        executor.execute(() -> {
            try {
                pplStdTableService.syncPplJoinOrderTaskByVersionEvent(version.getVersionCode());
            } catch (Exception e) {
                if (e instanceof InterruptedException) {
                    return;
                }else {
                    alert.sendRtx("oliverychen", "拼接宽表同步任务失败", e.getMessage());
                }
            }
        });

    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @Transactional(value = "rrpTransactionManager")
    public void closeUnificatedVersion(UnificatedVersionDTO version, UnificatedVersionEventDO event) {
        // 检查当前版本是否有调用过

        // 1.检查新版本
        UnificatedVersionDTO nextVersion = demandDBHelper.getOne(UnificatedVersionDTO.class,
                "where id > ? order by id asc limit 1", version.getId());
        if (ObjectUtils.isEmpty(nextVersion)) {
            throw new BizException("未初始化新版本");
        }

        // 2.检查所有事件是不是都完成了，如果没完成报错。
        List<String> closeEventList = Arrays.asList(CrpEventEnum.VERSION_CLOSE_TIME.getCode(),
                CrpEventEnum.DEVICE_DEMAND_VERSION_CLOSE_TIME.getCode());
        List<UnificatedVersionEventDO> notDoneEventList = version.getEventList().stream()
                .filter(v -> !closeEventList.contains(v.getEventCode()) && !v.getIsDone())
                        .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(notDoneEventList)) {
            throw new BizException("当前版本有流程节点没完成，无法自动化关闭版本");
        }

//        // 2.关闭产品13周版本
//        RrpConfigDO rrpConfig = versionService.getRrpConfig(version.getVersionCode());
//        if (rrpConfig == null) {
//            throw new BizException(String.format("未找到版本编码%s对应的产品13周需求版本", version.getVersionCode()));
//        }
//        // 关闭版本的用户需要有管理员权限
//        versionService.openOrCloseRrpConfig(rrpConfig.getId(), false, "AUTO_VERSION");
//
//        // 3.创建产品13周版本
//        versionService.createRrpConfig(nextVersion);

        // 4.开启demand库的事务 进行demand库的相关处理
        SpringUtil.getBean(UnificatedVersionService.class).closeVersionForDemandVersion(version, nextVersion, event);
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void closeVersionForDemandVersion(UnificatedVersionDO version, UnificatedVersionDTO nextVersion,
            UnificatedVersionEventDO event) {
        // 1.关闭大版本
        version.setStatus(UnificatedVersionStatusEnum.DONE.getCode());
        demandDBHelper.update(version);

        // 2.关闭行业13周版本
        pplVersionService.finishApproveByVersionCode(version.getVersionCode());

//        // 3.关闭产品全年版本
//        ProductDemandVersionDO fullYearVersion = productDemandVersionService.getVersion(version.getVersionCode());
//        if (fullYearVersion == null) {
//            throw new BizException(
//                    String.format("未找到版本编码%s对应的产品全年需求版本", version.getVersionCode()));
//        }
//        productDemandVersionService.openOrCloseVersion(fullYearVersion.getId(), false);

        nextVersion.setStatus(UnificatedVersionStatusEnum.PROCESS.getCode());
        demandDBHelper.update(nextVersion);

        // 4.创建行业13周版本
        pplVersionService.createVersionByUnificatedVersion(nextVersion);

//        // 5.创建产品全年版本
//        productDemandVersionService.insertVersion(nextVersion);
//
//        // 刷新产品13周需求月份对应版本id的实时表
//        realTimeDemandService.refreshVersionTable();

        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(version.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_CLOSE_TIME.getCode()));

        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(nextVersion.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_CREATE.getCode()));

        // 正常走完的话 打上完成的标记
        event.setIsDone(Boolean.TRUE);
        event.setOperateUser(LoginUtils.getUserNameWithSystem());
        demandDBHelper.update(event);

    }

    @Override
    public void checkUnificatedVersionInit() {
        List<UnificatedVersionDO> all = demandDBHelper.getAll(UnificatedVersionDO.class, "where status = ?",
                UnificatedVersionStatusEnum.NEW.getCode());
        int versionCount = all.size();
        if (versionCount > 3) {
            return;
        }
        // 起码需要初始化8个待启动的版本
        for (int i = versionCount + 1; i < 9; i++) {
            createUnificatedVersion();
        }
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void product13WeekEnterDeadline(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO) {
//        RrpConfigDO rrpConfig = versionService.getRrpConfig(versionDO.getVersionCode());
//        if (rrpConfig == null) {
//            throw new BizException(String.format("未找到版本编码%s对应的产品13周需求版本", versionDO.getVersionCode()));
//        }
//        versionService.product13WeekEnterDeadline(rrpConfig.getId());
//        String formUrl = domainSupplier.get() + "/rrp/demands/list";
//        String content = "您好，本周录入已截止，请留意各产品审批进度。\n" +
//                " 点击链接前往版本数据列表：\n" +
//                "<a href='" + formUrl + "'>" + formUrl + "</a>";
//        dictService.eventNotice(CrpEventEnum.PHYSICAL_ENTET_DEADLINE.getCode(), null, content);
//        // 正常走完的话 打上完成的标记
//        eventDO.setIsDone(Boolean.TRUE);
//        eventDO.setOperateUser(LoginUtils.getUserNameWithSystem());
//        demandDBHelper.update(eventDO);
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void productFullYearEnterDeadline(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO) {
//        ProductDemandVersionDO fullYearVersion = productDemandVersionService.getVersion(versionDO.getVersionCode());
//        if (fullYearVersion == null) {
//            throw new BizException(
//                    String.format("未找到版本编码%s对应的产品全年需求版本", versionDO.getVersionCode()));
//        }
//        String url = domainSupplier.get() + "/product/list";
//        String content = "您好，本周录入已截止，请留意各产品审批进度。\n" +
//                " 点击链接前往版本数据列表：\n" +
//                "<a href='" + url + "'>" + url + "</a>";
//        dictService.eventNotice(CrpEventEnum.YEAR_DEMAND_ENTET_DEADLINE.getCode(), null, content);
//        // 正常走完的话 打上完成的标记
//        eventDO.setOperateUser(LoginUtils.getUserNameWithSystem());
//        eventDO.setIsDone(Boolean.TRUE);
//        demandDBHelper.update(eventDO);

    }

    @Override
    public void firstInit() {
        UnificatedVersionDTO firstVersion = demandDBHelper.getOne(UnificatedVersionDTO.class,
                "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());

        // 创建产品13周版本
        versionService.createRrpConfig(firstVersion);

        // 创建行业13周版本
        pplVersionService.createVersionByUnificatedVersion(firstVersion);

        // 创建产品全年版本
        productDemandVersionService.insertVersion(firstVersion);

    }

    private Integer longToInteger(long value) {
        if (value >= Integer.MIN_VALUE && value <= Integer.MAX_VALUE) {
            return (int) value;
        } else {
            throw new BizException("The long value is out of range for an Integer.");
        }
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void passFullYearDemandForecastToErp(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO)
            throws InterruptedException {

        UnificatedVersionEventDO dbDo = demandDBHelper.getByKey(UnificatedVersionEventDO.class, eventDO.getId());
        if (dbDo.getIsDone()) {
            return;
        }

        UnificatedVersionDTO version = demandDBHelper.getByKey(UnificatedVersionDTO.class, eventDO.getVersionId());
        ProductDemandVersionDO fullYearVersion = productDemandVersionService.getVersion(version.getVersionCode());
        if (fullYearVersion == null) {
            throw new BizException(
                    String.format("未找到版本编码%s对应的产品全年需求版本", version.getVersionCode()));
        }
        // 查看上一个任务
        NebulaToErpForecastTaskInfoDO lastTaskInfoDO = resplanDBHelper.getOne(NebulaToErpForecastTaskInfoDO.class,
                "where nebula_version = ? and pass_type='全年预测' order by id desc",
                fullYearVersion.getDemandVersion());

        if (lastTaskInfoDO != null && "传递中".equals(lastTaskInfoDO.getTaskStatus())) {
            log.info("全年需求需求递到erp有任务在传递中！");
            return;
        } else {
            autoDeliveryDemandForecastService.passDemandForecastToErp(Boolean.TRUE,
                    longToInteger(fullYearVersion.getId()),
                    "AUTO_VERSION", null);

            long startTime = System.currentTimeMillis();
            long thirtyMinutes = 30 * 60 * 1000;  // 30 minutes in milliseconds
            // 需要消息通知持续传递中的时间
            long messageTime = startTime + 1000 * 60;
            // 轮询查传递记录
            while (true) {
                Thread.sleep(3000);
                long now = System.currentTimeMillis();
                NebulaToErpForecastTaskInfoDO newTaskInfoDO = resplanDBHelper.getOne(
                        NebulaToErpForecastTaskInfoDO.class,
                        "where nebula_version = ? and pass_type='全年预测' and id > ? order by id desc",
                        fullYearVersion.getDemandVersion(), lastTaskInfoDO == null ? -1L : lastTaskInfoDO.getId());
                if (newTaskInfoDO != null) {
                    if ("传递成功".equals(newTaskInfoDO.getTaskStatus()) || "传递完成".equals(newTaskInfoDO.getTaskStatus())) {
                        break;
                    }
                    if ("传递失败".equals(newTaskInfoDO.getTaskStatus())) {
                        throw new BizException("传递到ERP失败!");
                    }
                    if (now - messageTime >= 0) {
                        // 通知持续关注正在传递需求
                        alert.sendRtx("jackycjchen;oliverychen;kaijiazhang",
                                "物理机全年需求预测传递ERP持续性通知",
                                String.format("物理机全年需求预测传递中，已经持续传递：%s，请关注", newTaskInfoDO.getPassingTime()));
                        // 下次通知是一分钟后
                        messageTime = now + 1000 * 60;
                    }
                }
                if (now - startTime >= thirtyMinutes) {
                    throw new BizException("获取传递到ERP结果超时");
                }
            }
        }

        // 正常走完的话 打上完成的标记
        eventDO.setOperateUser(LoginUtils.getUserNameWithSystem());
        eventDO.setIsDone(Boolean.TRUE);
        demandDBHelper.update(eventDO);
        // 通知业务
        UnificatedVersionDO.DemandYearMonth yearMonth = UnificatedVersionDO.parse2DemandYearMonth(
                versionDO.getYearDemandYearMonth());
        String formUrl = domainSupplier.get() + "/rrp/demands/demandTransfer";
        String content =
                "您好，本周全年预测已成功传递到ERP。传递范围：" + yearMonth.getBeginYearMonth() + "～"
                        + yearMonth.getEndYearMonth() + "，请进行人工对账确保数据正常。\n" +
                        "更多信息点击链接前往：\n" +
                        "<a href='" + formUrl + "'>" + formUrl + "</a>";
        // 用配置的标题
        dictService.eventNotice(eventDO.getEventCode(), null, content);
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void pass13WeeksDemandForecastToErp(UnificatedVersionEventDO eventDO, UnificatedVersionDO versionDO)
            throws InterruptedException {

        UnificatedVersionEventDO dbDo = demandDBHelper.getByKey(UnificatedVersionEventDO.class, eventDO.getId());
        if (dbDo.getIsDone()) {
            return;
        }

        RrpConfigDO rrpConfigDO = versionService.getRrpConfig(versionDO.getVersionCode());
        if (rrpConfigDO == null) {
            throw new BizException(
                    String.format("未找到版本编码%s对应的产品13周需求版本", versionDO.getVersionCode()));
        }
        // 查看上一个任务
        NebulaToErpForecastTaskInfoDO lastTaskInfoDO = resplanDBHelper.getOne(NebulaToErpForecastTaskInfoDO.class,
                "where nebula_version = ? and pass_type='13周预测' order by id desc", rrpConfigDO.getPlanVersion());

        if (lastTaskInfoDO != null && "传递中".equals(lastTaskInfoDO.getTaskStatus())) {
            log.info("13周需求递到erp有任务在传递中！");
            return;
        } else {
            autoDeliveryDemandForecastService.passDemandForecastToErp(Boolean.FALSE, longToInteger(rrpConfigDO.getId()),
                    "AUTO_VERSION", null);

            long startTime = System.currentTimeMillis();
            long thirtyMinutes = 30 * 60 * 1000;  // 30 minutes in milliseconds
            // 需要消息通知持续传递中的时间
            long messageTime = startTime + 1000 * 60;
            // 轮询查传递记录
            while (true) {
                Thread.sleep(3000);
                long now = System.currentTimeMillis();
                NebulaToErpForecastTaskInfoDO newTaskInfoDO = resplanDBHelper.getOne(
                        NebulaToErpForecastTaskInfoDO.class,
                        "where nebula_version = ? and pass_type='13周预测' and id > ? order by id desc",
                        rrpConfigDO.getPlanVersion(), lastTaskInfoDO == null ? -1L : lastTaskInfoDO.getId());
                if (newTaskInfoDO != null) {
                    if ("传递成功".equals(newTaskInfoDO.getTaskStatus()) || "传递完成".equals(
                            newTaskInfoDO.getTaskStatus())) {
                        break;
                    }
                    if ("传递失败".equals(newTaskInfoDO.getTaskStatus())) {
                        throw new BizException("传递到ERP失败!");
                    }
                    if (now - messageTime >= 0) {
                        // 通知持续关注正在传递需求
                        alert.sendRtx("jackycjchen;oliverychen;kaijiazhang",
                                "物理机13周需求预测传递ERP持续性通知",
                                String.format("物理机13周需求预测传递中，已经持续传递：%s，请关注", newTaskInfoDO.getPassingTime()));
                        // 下次通知是一分钟后
                        messageTime = now + 1000 * 60;
                    }
                }
                if (now - startTime >= thirtyMinutes) {
                    throw new BizException("获取传递到ERP结果超时");
                }
            }
        }

        // 正常走完的话 打上完成的标记
        eventDO.setOperateUser(LoginUtils.getUserNameWithSystem());
        eventDO.setIsDone(Boolean.TRUE);
        demandDBHelper.update(eventDO);
        // 通知业务
        UnificatedVersionDO.DemandYearMonth yearMonth = UnificatedVersionDO.parse2DemandYearMonth(
                versionDO.getProductDemandYearMonth());
        String formUrl = domainSupplier.get() + "/rrp/demands/demandTransfer";
        String content =
                "您好，本周产品13周预测已成功传递到ERP。传递范围：" + yearMonth.getBeginYearMonth() + "～"
                        + yearMonth.getEndYearMonth() + "，请进行人工对账确保数据正常。\n" +
                        "更多信息点击链接前往：\n" +
                        "<a href='" + formUrl + "'>" + formUrl + "</a>";
        // 用配置的标题
        dictService.eventNotice(eventDO.getEventCode(), null, content);
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    public void forceCloseUnificatedVersion(Long id) throws InterruptedException {
        Boolean isTest = Strings.isNotBlank(testEnv.get());
        if (isTest.equals(Boolean.FALSE)) {
            // 如果是生产环境 不允许调用此方法。
            throw new BizException("该方法仅允许在测试环境调用");
        }
        UnificatedVersionDTO unificatedVersionDTO = demandDBHelper.getOne(UnificatedVersionDTO.class, "where id = ?",
                id);
        if (unificatedVersionDTO == null) {
            throw new BizException("版本Id不存在");
        }
        // 1、完成所有事件
        demandDBHelper.executeRaw("update unificated_version_event set is_done = 1 "
                + "where deleted = 0 and version_id = ?", id);

        // 2、完成所有未完成的group
        demandDBHelper.executeRaw("update ppl_version_group set status = 'DONE' where deleted = 0 and version_code = ?",
                unificatedVersionDTO.getVersionCode());
        Thread.sleep(3000);

        UnificatedVersionEventDO closeEvent = demandDBHelper.getOne(UnificatedVersionEventDO.class,
                "where version_id = ? and event_code = ?",
                id, CrpEventEnum.VERSION_CLOSE_TIME.getCode());

        // 开启demand库的事务 进行demand库的相关处理
        SpringUtil.getBean(UnificatedVersionService.class).closeUnificatedVersion(unificatedVersionDTO, closeEvent);
    }

    @Override
    public void dataCheck(UnificatedVersionEventDO currentEvent, UnificatedVersionDTO versionDTO) {
        Map<String, UnificatedVersionEventDO> codeToEvent = versionDTO.getEventList().stream()
                .collect(Collectors.toMap(UnificatedVersionEventDO::getEventCode, v -> v));

        if (currentEvent.getEventCode().equals(CrpEventEnum.DATA_CHECK_STOCK_SUPPLY_DONE.getCode())
                && codeToEvent.get(CrpEventEnum.STOCK_SUPPLY_DONE.getCode()).getIsDone()) {
            // 行业PPL对冲完成检查事件

        }


    }

    @Override
    public UnificatedVersionDTO queryProcessingUnificatedVersion() {
        UnificatedVersionDTO one = demandDBHelper.getOne(UnificatedVersionDTO.class, "where status = ?",
                UnificatedVersionStatusEnum.PROCESS.getCode());
        one.parseJsonDemand();
        return one;
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false, waitLockMillisecond = 0)
    public void deviceEnterDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO) {
        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(unificatedVersionDTO.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_ENTER_DEADLINE.getCode()));
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false, waitLockMillisecond = 0)
    public void deviceApproveDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO) {
        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(unificatedVersionDTO.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_APPROVE_DEADLINE.getCode()));
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false, waitLockMillisecond = 0)
    public void devicePassDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO) throws InterruptedException {

        UnificatedVersionEventDO dbDo = demandDBHelper.getByKey(UnificatedVersionEventDO.class, eventDO.getId());
        if (dbDo.getIsDone()) {
            return;
        }

        // 查看上一个任务
        ImportForecastTaskOrderDO lastTaskInfoDO = resplanDBHelper.getOne(ImportForecastTaskOrderDO.class,
                "where create_user = ? and task_code like ? order by id desc",
                "CRP", unificatedVersionDTO.getVersionCode()+"%");

        if (lastTaskInfoDO != null && "执行中".equals(lastTaskInfoDO.getTaskStatus())) {
            log.info("需求递到erp有任务在传递中！");
            return;
        } else {
            log.info("开启传递任务！");
            DeviceDemandVersionService.ReceiveVersionEventResp resp = deviceDemandVersionService.receiveVersionEvent(
                    new DeviceDemandVersionService.ReceiveVersionEventReq(unificatedVersionDTO.getVersionCode(),
                            CrpEventEnum.DEVICE_DEMAND_VERSION_PASS_DEADLINE.getCode()));

            if (resp == null || StringUtils.isBlank(resp.getResult())) {
                throw new BizException("发起传递到ERP失败，未收到传递批次号！");
            }
            long startTime = System.currentTimeMillis();
            long lastLogTime = startTime;
            // 轮询查传递记录
            log.info("已成功开启传递任务，现在轮询传递状态！");
            try {
                while (true) {
                    log.info("查询传递编码为{}的传递状态", resp.getResult());
                    ImportForecastTaskOrderDO task = resplanDBHelper.getOne(
                            ImportForecastTaskOrderDO.class,
                            "where task_code = ?",
                            resp.getResult());

                    if (task == null) {
                        throw new BizException(String.format("传递到ERP失败，未找到传递记录！传递批次号：%s", resp.getResult()));
                    }

                    long currentTime = System.currentTimeMillis();
                    if (currentTime - lastLogTime >= 60 * 1000) {
                        alert.sendRtx("jackycjchen;oliverychen;kaijiazhang",
                                "物理机需求预测传递ERP持续性通知",
                                String.format("物理机需求预测传递中，已经持续传递：%s，请关注", currentTime-startTime));
                        lastLogTime = currentTime;
                    }

                    switch (task.getTaskStatus()) {
                        case "成功":
                            alert.sendRtx("jackycjchen;oliverychen;kaijiazhang",
                                    "物理机需求预测传递ERP完成",
                                    "物理机需求预测传递ERP完成，请关注，传递批次号：" + resp.getResult());
                            eventDO.setOperateUser(LoginUtils.getUserNameWithSystem());
                            eventDO.setIsDone(Boolean.TRUE);
                            demandDBHelper.update(eventDO);
                            return; // 成功退出
                        case "失败":
                            throw new BizException("传递到ERP失败，传递批次号：" + resp.getResult());

                        case "部分成功":
                            throw new BizException("传递到ERP部分成功，传递批次号：" + resp.getResult());

                        case "未知":
                            throw new BizException("传递到ERP结果：未知，传递批次号：" + resp.getResult());

                        default:
                            if (currentTime - startTime >= 1000 * 60 * 30) {
                                throw new BizException("获取传递到ERP结果超时，传递批次号：" + resp.getResult());
                            }
                            Thread.sleep(1000 * 3);
                    }
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                throw new BizException("轮询查询传递状态被中断");
            }
        }
    }

    @Override
    public void deviceReviewDeadLine(UnificatedVersionDTO unificatedVersionDTO, UnificatedVersionEventDO eventDO) {
        deviceDemandVersionService.receiveVersionEvent(
                new DeviceDemandVersionService.ReceiveVersionEventReq(unificatedVersionDTO.getVersionCode(), CrpEventEnum.DEVICE_DEMAND_VERSION_REVIEW_DEADLINE.getCode()));
    }

    private void checkAuthAndParams(UnificatedVersionDTO unificatedVersionDTO) {
        pplCommonService.requireRole(IndustryDemandAuthRoleEnum.VERSION_OWNER);
        if (ObjectUtils.isEmpty(unificatedVersionDTO.getPplDemand())) {
            throw new BizException("行业需求年月不能为空");
        }
        if (ObjectUtils.isEmpty(unificatedVersionDTO.getProductDemand())) {
            throw new BizException("行业需求年月不能为空");
        }
        if (ObjectUtils.isEmpty(unificatedVersionDTO.getYearDemand())) {
            throw new BizException("行业需求年月不能为空");
        }

        unificatedVersionDTO.setPplDemandYearMonth(JsonUtil.prettyToString(unificatedVersionDTO.getPplDemand()));
        unificatedVersionDTO.setProductDemandYearMonth(
                JsonUtil.prettyToString(unificatedVersionDTO.getProductDemand()));
        unificatedVersionDTO.setYearDemandYearMonth(JsonUtil.prettyToString(unificatedVersionDTO.getYearDemand()));
    }

}
