package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplItemBaseDO;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

/**
 * 云霄预约数据同步表，这个表的性质是相对临时的
 */
@Data
@ToString
@Table("ppl_item_applied")
public class PplItemAppliedDO extends PplItemBaseDO {

    /** 架构师，实际上就是预约人<br/>Column: [submit_user] */
    @Column(value = "submit_user")
    private String submitUser;

    /** 预约人<br/>Column: [apply_user] */
    @Column(value = "apply_user")
    private String applyUser;

    /** 第二个ppl id，最多2个<br/>Column: [ppl_id2] */
    @Column(value = "ppl_id2")
    private String pplId2;

    /** 第二个ppl order，最多2个<br/>Column: [ppl_order2] */
    @Column(value = "ppl_order2")
    private String pplOrder2;

    /** ppl单来源<br/>Column: [ppl_order_source] */
    @Column(value = "ppl_order_source")
    private String pplOrderSource;

    /** ppl id核心数<br/>Column: [ppl_id_total_core] */
    @Column(value = "ppl_id_total_core")
    private Integer pplIdTotalCore;

    /** 第二个ppl单来源<br/>Column: [ppl_order2_source] */
    @Column(value = "ppl_order2_source")
    private String pplOrder2Source;

    /** 第二个ppl id核心数<br/>Column: [ppl_id2_total_core] */
    @Column(value = "ppl_id2_total_core")
    private Integer pplId2TotalCore;

    /** 提预约单时的预测版本id*/
    @Column(value = "inner_version_id")
    private Long innerVersionId;

    /** 提预约单时的预测版本日期（yyyy-MM-dd）*/
    @Column(value = "inner_version_date")
    private LocalDate innerVersionDate;

    // 以下字段是客户信息

    /** 需求年份<br/>Column: [year] */
    @Column(value = "year")
    private Integer year;

    /** 需求月份<br/>Column: [month] */
    @Column(value = "month")
    private Integer month;

    /** 行业<br/>Column: [industry] */
    @Column(value = "industry")
    private String industry;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 客户uin<br/>Column: [customer_uin] */
    @Column(value = "customer_uin")
    private String customerUin;

    /** 客户类型,存量客户,winback客户<br/>Column: [customer_type] */
    @Column(value = "customer_type")
    private String customerType;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 客户名称<br/>Column: [customer_name] */
    @Column(value = "customer_name")
    private String customerName;

    /** 战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 客户来源<br/>Column: [customer_source] */
    @Column(value = "customer_source")
    private String customerSource;

    /** 云霄提单时间<br/>Column: [] */
    @Column(value = "yunxiao_order_create_time")
    private String yunxiaoOrderCreateTime;

    /** 预约单提单人 */
    @Column(value = "apply_submit_user")
    private String applySubmitUser;

    /** 预约单架构师 */
    @Column(value = "apply_architect")
    private String applyArchitect;

    /** 预约单原因类型 */
    @Column(value = "apply_reason_type")
    private String applyReasonType;

    /** 预约单行业类型 */
    @Column(value = "apply_industry_type")
    private String applyIndustryType;

    /** 预约单APPID */
    @Column(value = "apply_appId")
    private String applyAppId;

    /** 预约单需求详情 */
    @Column(value = "apply_reason")
    private String applyReason;

    /** 预约单单据类型 */
    @Column(value = "apply_order_type")
    private String applyOrderType;
}
