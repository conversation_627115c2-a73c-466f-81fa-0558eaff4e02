package cloud.demand.app.modules.p2p.ppl13week.entity;

// package a.b.c;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyStatusEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.util.Date;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_stock_supply")
public class PplStockSupplyDO extends BaseDO {

    /**
     * 版本group id<br/>Column: [version_code]
     */
    @Column(value = "version_code")
    private String versionCode;

    /**
     * 当前对冲的状态<br/>Column: [status]
     *
     * @see PplStockSupplyStatusEnum
     */
    @Column(value = "status")
    private String status;


    /**
     * boolean 改成 integer
     * 0 未下发到星云
     * 1 成功下发到星云
     * 2 下发中
     * 3 下发失败
     * 下发到物理机供应方案上，这里不可以加状态，很多是取最新完成的，会导致错误
     */
    @Column(value = "issue_status")
    private Integer issueStatus;

    /**
     * 下发时间
     */
    @Column(value = "issue_time")
    private Date issueTime;

    /**
     * 轮训的时间间隔<br/>Column: [interval_time]
     */
    @Column(value = "interval_time")
    private Integer intervalTime;

    /**
     * 每次轮训的时间记录， log 用<br/>Column: [rotation_time_append]
     */
    @Column(value = "rotation_time_append", maxStringLength = 16777000)
    private String rotationTimeAppend;

    @Column(value = "cvm_task_id")
    private String cvmTaskId;

    @Column(value = "cbs_task_id")
    private String cbsTaskId;

    @Column(value = "cvm_task_status")
    private String cvmTaskStatus;

    @Column(value = "cbs_task_status")
    private String cbsTaskStatus;

    @Column(value = "operator")
    private String operator;

    /**
     * 是否为最后一次对冲<br/>Column: [is_version_latest_supply]
     */
    @Column(value = "is_version_latest_supply")
    private Boolean isVersionLatestSupply;

    @Column(value = "is_version_latest_success_supply")
    private Boolean isVersionLatestSuccessSupply;


    /**
     * 请求体<br/>Column: [req]
     */
    @Column(value = "cvm_error_msg", maxStringLength = 16777000)
    private String cvmErrorMsg;

    /**
     * 返回体<br/>Column: [resp]
     */
    @Column(value = "cbs_error_msg", maxStringLength = 16777000)
    private String cbsErrorMsg;

    @Column(value = "finish_time")
    private Date finishTime;

}