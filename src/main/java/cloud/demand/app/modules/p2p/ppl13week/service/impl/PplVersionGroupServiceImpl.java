package cloud.demand.app.modules.p2p.ppl13week.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week.enums.SpecialCustomerNameEnum.FORECAST_INDUSTRY_CUSTOMER_NAME;
import static cloud.demand.app.modules.p2p.ppl13week.enums.SpecialCustomerNameEnum.FORECAST_INNER_CUSTOMER_NAME;
import static cloud.demand.app.modules.p2p.ppl13week.enums.SpecialCustomerNameEnum.isForecast;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.AlarmRobotUtil;
import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.DateUtils;
import cloud.demand.app.common.utils.DateUtils.YearMonth;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.IGetter;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.common.utils.ObjUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.industry_report.entity.IndustryReportAppidInfoLatestWithoutJsonDO;
import cloud.demand.app.modules.order.dto.req.OrderQueryReq;
import cloud.demand.app.modules.order.dto.resp.OrderDetailResp;
import cloud.demand.app.modules.order.dto.resp.OrderItemDTO;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderSourceEnum;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.service.OrderCommonService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.industry_demand.dto.FileNameAndBytesDTO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandRegionZoneInstanceTypeDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.industry_demand.service.IndustryDemandDictService;
import cloud.demand.app.modules.p2p.industry_demand.service.TodoService;
import cloud.demand.app.modules.p2p.ppl13week.dto.PplItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.dto.dict.DictCommonReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.dict.InstanceModelInfoDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.ChangeItemEventDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.item.PplExportWriteHandler;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.CreateOrderDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.order.rsp.QueryInfoByUinRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ApproveVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ExportPplVersionItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.ImportVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.InitNewVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplCvmImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplGpuImportExcelDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportExcelNumberExportDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemImportRsp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemStockSupplyExportCbsDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.PplItemStockSupplyExportCvmDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.QueryVersionGroupView;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.SaveVersionGroupItemReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupApproveResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupForecastResultViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupForecastResultViewResp.DimKey;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupForecastResultViewResp.DimValue;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupInfoResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.GroupItemDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupItemResp.SatisfySupplyDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSpikeViewResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupStatResp.SummaryDTO;
import cloud.demand.app.modules.p2p.ppl13week.dto.version.VersionGroupSummaryResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.wave_analysis.detail.InstanceTypeRelateDTO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplAppliedSupplylDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplAuditItemQueueDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplGroupStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplInnerProcessVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplOrderDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyBMDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyGpuDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyGpuDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyReqDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplStockSupplyRspDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplSupplyConsensusDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.PplVersionGroupRecordItemDO;
import cloud.demand.app.modules.p2p.ppl13week.entity.base.PplOrderBaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.IndustryDeptEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.OperateTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDiskTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemEventTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplItemStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplOrderSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplProjectTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupApproveResultEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionGroupStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplVersionStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.stock_supply.PplStockSupplyMatchTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoOrderStatusEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplBMStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplCommonService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplConvertPhysicalServerService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplImportService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplItemService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplStockSupplyService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionGroupService;
import cloud.demand.app.modules.p2p.ppl13week.service.PplVersionService;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplImportServiceImpl.MyList;
import cloud.demand.app.modules.p2p.ppl13week.service.impl.PplVersionServiceImpl.GroupRecordAndItem;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplGroupJoinVersionVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemForecastApplyMatchVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplItemWithOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplRecordItemJoinOrderVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplStockSupplyReqWithRspVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupInfoVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemDictVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemSummaryVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupItemWithSourceItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordItemVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.PplVersionGroupRecordWithGroupVO;
import cloud.demand.app.modules.p2p.ppl13week.vo.VersionGroupSpikeViewDetail;
import cloud.demand.app.modules.p2p.ppl13week.vo.VersionGroupSpikeViewDetailKey;
import cloud.demand.app.modules.p2p.ppl13week.vo.VersionGroupSummaryVO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDetailDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictResultSplitMiddleDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.DailyZoneAppidGinstypePaymodeApproleDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.DailyZoneAppidGinstypePaymodeApproleWithCustomerDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastGroupDimsEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekInputService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekQueryService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.PplForecastNewMiddleService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13WeekInputServiceImpl;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13WeekInputServiceImpl.ForSpikeParams;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl.Ppl13weekQueryServiceImpl.QuerySplitMiddleResultParams;
import cloud.demand.app.modules.yunti_demand.model.publicPoolReq;
import cloud.demand.app.modules.yunti_demand.service.YuntiDemand2CrpService;
import cloud.demand.app.web.model.common.Page;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisStopException;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import com.tencent.rainbow.util.JsonUtil;
import com.tencent.rainbow.util.StringUtil;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.Period;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.config.DynamicProperty;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class PplVersionGroupServiceImpl implements PplVersionGroupService {

    @Resource
    PplForecastNewMiddleService pplForecastNewMiddleService;
    @Resource
    private DBHelper demandDBHelper;
    @Resource
    private DBHelper ckcldStdCrpDBHelper;
    @Resource
    private PplCommonService pplCommonService;
    @Resource
    private PplItemService pplItemService;
    @Resource
    private DictService dictService;
    @Resource
    private PplVersionService pplVersionService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private PplStockSupplyService pplStockSupplyService;
    @Resource
    private PplImportService pplImportService;
    @Resource
    private PplConvertPhysicalServerService pplConvertPhysicalServerService;
    @Resource
    private IndustryDemandDictService industryDemandDictService;
    @Resource
    private PplBMStockSupplyService pplBMStockSupplyService;
    @Resource
    private Ppl13weekQueryService ppl13weekQueryService;
    @Resource
    private Ppl13weekInputService ppl13weekInputService;
    @Resource
    private YuntiDemand2CrpService yuntiDemand2CrpService;
    @Resource
    private Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    Alert alert;
    private static DynamicProperty<String> testEnv = DynamicProperty.create("test_env", "");

    /**
     * 是否强制用保留客户维度作为模型预测量
     */
    private static DynamicProperty<String> isForecastForceKeepUin =
            DynamicProperty.create("ppl13week.forecast.forcekeepuin", "false");

    @Override
    public void checkGroupViewPermission(PplVersionGroupInfoVO groupInfoVO) {
        String userName = LoginUtils.getUserName();
        if ("no".equalsIgnoreCase(userName)) {
            return;
        }

        // 当前及过往处理人，可以看
        String currentProcessor = groupInfoVO.getCurrentProcessor();
        if (StringTools.isNotBlank(currentProcessor) && currentProcessor.contains(userName)) {
            return;
        }
        List<PplVersionGroupRecordDO> records = groupInfoVO.getRecords();
        if (records != null) {
            for (PplVersionGroupRecordDO record : records) {
                if (StringTools.isNotBlank(record.getOperateUser()) && record.getOperateUser().contains(userName)) {
                    return;
                }
            }
        }

        // 管理员可以看
        String approveUser = pplCommonService.getApproveUser(Lang.list(IndustryDemandAuthRoleEnum.ADMIN));
        if (approveUser != null && approveUser.contains(userName)) {
            return;
        }

        // 同OA部门
        List<String> industryDept = pplDictService.queryIndustryDept(userName);
        if (!Lang.isEmpty(industryDept) && Strings.isNotBlank(industryDept.get(0))) {
            if (Objects.equals(industryDept.get(0), groupInfoVO.getIndustryDept())) {
                return;
            }
        }

        // PPL角色按部门可以看
        List<IndustryDemandAuthDO> authRole =
                pplCommonService.getAuthRole(LoginUtils.getUserName());
        ArrayList<IndustryDemandAuthRoleEnum> roleList = Lang.list(
                IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE, IndustryDemandAuthRoleEnum.PPL_INDUSTRY_APPROVE,
                IndustryDemandAuthRoleEnum.PPL_PRODUCT_APPROVE, IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);
        List<String> roleCodeList = ListUtils.transform(roleList, IndustryDemandAuthRoleEnum::getCode);
        for (IndustryDemandAuthDO industryDemandAuthDO : authRole) {
            if (Strings.isNotBlank(industryDemandAuthDO.getIndustry()) &&
                    roleCodeList.contains(industryDemandAuthDO.getRole())) {
                String industrys = industryDemandAuthDO.getIndustry();
                if (Strings.isNotBlank(industrys)) {
                    List<String> deptIndustrys = Lang.list(industrys.split(";"));
                    for (String deptIndustry : deptIndustrys) {
                        if (Objects.equals(deptIndustry, groupInfoVO.getIndustryDept())) {
                            return;
                        }
                    }
                }
            }
        }

        throw new BizException("您没有权限查看");
    }

    @Override
    public List<String> queryVersionCode() {
        return demandDBHelper.getRaw(String.class,
                "select distinct version_code from ppl_version_group where deleted=0 order by id desc");
    }

    @Override
    public QueryVersionGroupResp queryVersionGroup(QueryVersionGroupReq req) {
        if (req == null) {
            req = new QueryVersionGroupReq();
        }
        if (req.getPage() == null) {
            req.setPage(new Page());
        }

        WhereContent where = new WhereContent();
        if (ListUtils.isNotEmpty(req.getVersionCodes())) {
            where.addAnd("t1.version_code in (?)", req.getVersionCodes());
        }
        if (StringTools.isNotBlank(req.getVersionName())) {
            where.addAnd("t2.version_name like ?", "%" + req.getVersionName() + "%");
        }
        if (ListUtils.isNotEmpty(req.getStatus())) {
            where.addAnd("t1.status in (?)", req.getStatus());
        }
        if (ListUtils.isNotEmpty(req.getIndustryDepts())) {
            where.addAnd("t1.industry_dept in (?)", req.getIndustryDepts());
        }
        if (ListUtils.isNotEmpty(req.getProducts())) {
            where.addAnd("t1.product in (?)", req.getProducts());
        }
        where.addAnd("t1.product not in (?)", Ppl13weekProductTypeEnum.getNotSupport13WeekGroupList());
        where.order("t1.id desc");

        getPplAuth(LoginUtils.getUserName(), where);

        PageData<PplGroupJoinVersionVO> pageData = demandDBHelper.getPage(PplGroupJoinVersionVO.class,
                req.getPage().getPageIndex(), req.getPage().getSize(), where.getSql(), where.getParams());

        QueryVersionGroupResp resp = new QueryVersionGroupResp();
        resp.setTotal((int) pageData.getTotal());
        resp.setData(ListUtils.transform(pageData.getData(), o -> QueryVersionGroupResp.VersionGroupDTO.from(o)));

        return resp;
    }

    private void getPplAuth(String userName, WhereContent whereContent) {

        // 管理员不健权
        String approveUser = pplCommonService.getApproveUser(Lang.list(
                IndustryDemandAuthRoleEnum.ADMIN));
        if (approveUser.contains(userName)) {
            return;
        }

        //1、登陆人=录单人；
        //2、登陆人所在OA部门，与PPL单“行业部门”一致
        //1和2任意满足一个都可
        WhereContent auth = new WhereContent("1=0");
        auth.addOr("t2.creator =?", userName);

        List<String> industryDept = pplDictService.queryIndustryDept(userName);
        if (!Lang.isEmpty(industryDept) && Strings.isNotBlank(industryDept.get(0))) {
            auth.addOr("t1.industry_dept=?", industryDept.get(0));

        }

        List<IndustryDemandAuthDO> authRole =
                pplCommonService.getAuthRole(LoginUtils.getUserName());

        ArrayList<IndustryDemandAuthRoleEnum> roleList = Lang.list(
                IndustryDemandAuthRoleEnum.PPL_INDUSTRY_MEDDLE, IndustryDemandAuthRoleEnum.PPL_INDUSTRY_APPROVE,
                IndustryDemandAuthRoleEnum.PPL_PRODUCT_APPROVE, IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE);
        List<String> roleCodeList = ListUtils.transform(roleList, IndustryDemandAuthRoleEnum::getCode);
        for (IndustryDemandAuthDO industryDemandAuthDO : authRole) {
            if (Strings.isNotBlank(industryDemandAuthDO.getIndustry()) &&
                    roleCodeList.contains(industryDemandAuthDO.getRole())) {
                String industrys = industryDemandAuthDO.getIndustry();
                if (Strings.isNotBlank(industrys)) {
                    List<String> deptIndustrys = Lang.list(industrys.split(";"));
                    if (Lang.isNotEmpty(deptIndustrys)) {
                        auth.orIn("t1.industry_dept", deptIndustrys);
                    }
                }
            }
        }

        log.info("login_user: {}, auth where: {}", userName, auth.toString());

        whereContent.addAnd(auth);
    }

    @Override
    public VersionGroupInfoResp queryVersionGroupInfo(Long groupId) {
        PplVersionGroupInfoVO vo = getGroup(groupId);
        checkGroupViewPermission(vo);
        return VersionGroupInfoResp.from(vo);
    }

    @Override
    public VersionGroupApproveResp queryVersionGroupApproveLog(Long groupId) {
        PplVersionGroupInfoVO vo = getGroup(groupId);
        checkGroupViewPermission(vo);

        // 2022年12月13日 11:48:36 增加特性：当用户有角色但还不是在审批人列表中时，自动加进去
        String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                vo.getStatus(), vo.getIndustryDept(), vo.getProduct());
        String newProcessor = isAllIn(processor, vo.getCurrentProcessor());
        if (StringTools.isNotBlank(newProcessor)) {
            vo.setCurrentProcessor(
                    (StringTools.isBlank(vo.getCurrentProcessor()) ? "" : (vo.getCurrentProcessor() + ";"))
                            + newProcessor);
            demandDBHelper.update(vo);
        }

        VersionGroupApproveResp resp = new VersionGroupApproveResp();
        resp.setCurStatus(vo.getStatus());
        resp.setHandler(vo.getCurrentProcessor());
        //判断是否有撤回权限
        if (vo.getStatus().equals(PplVersionGroupStatusEnum.PAAS_SUBMIT.getCode())) {
            String inSubmitProcessor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode(),
                    vo.getIndustryDept(), vo.getProduct());
            String userName = LoginUtils.getUserName();
            if (inSubmitProcessor.contains(userName)) {
                resp.setRevocable(Boolean.TRUE);
            }
        }

        PplVersionGroupRecordDO firstRecord = vo.getRecords().get(0);
        boolean hasItemData = Strings.equals(firstRecord.getStatus(), PplVersionGroupStatusEnum.CREATE.getCode());

        List<PplVersionGroupRecordDO> filterInSubmit = ListUtils.filter(vo.getRecords(),
                (o) -> Strings.equals(o.getStatus(), PplVersionGroupStatusEnum.IN_SUBMIT.getCode()));
        boolean containInSubmit = Lang.isNotEmpty(filterInSubmit);

        resp.setFlow(ListUtils.transform(
                PplVersionGroupStatusEnum.getNormalFlow(hasItemData, vo.getProduct(), containInSubmit),
                o -> VersionGroupApproveResp.FlowStatusDTO.from(o)));

        // 构造历史审批记录

        resp.setLogs(ListUtils.transform(vo.getRecords(), o -> VersionGroupApproveResp.StatusLogDTO.from(o)));
        return resp;
    }

    /**
     * 判断newProcessor是否全在oldProcessor里面，如果是返回空字符串，否则返回不在里面的用户的列表，分号隔开
     */
    private static String isAllIn(String newProcessor, String oldProcessor) {
        if (StringTools.isBlank(newProcessor)) {
            return "";
        }
        String[] users = newProcessor.trim().split(";");
        StringBuilder sb = new StringBuilder();
        for (String user : users) {
            if (oldProcessor == null || !oldProcessor.contains(user)) {
                if (sb.length() > 0) {
                    sb.append(";");
                }
                sb.append(user);
            }
        }
        return sb.toString();
    }

    @Override
    public VersionGroupStatResp queryVersionGroupStat(VersionGroupStatReq req) {
        if (StringUtil.isEmpty(req.getResourceType())) {
            throw BizException.makeThrow("请先选择资源类型");
        }
        VersionGroupStatResp resp = new VersionGroupStatResp();
        PplVersionGroupInfoVO group = getGroup(req.getGroupId());
        checkGroupViewPermission(group);

        {
            // 1. 确定比较版本：默认已经审核完毕且距离group的创建时间最近的版本（这里可能没有上一个版本）
            if (group.getPplVersionDO() == null) {
                log.error("groupId:{} related versionDO(versionCode:{}) is null",
                        req.getGroupId(), group.getVersionCode());
                throw new BizException(
                        "分组对应的版本" + group.getVersionCode() + "已经不存在，属于数据异常，请联系开发排查");
            }
        }

        // 2.1 确定上一版本的group_record_id
        Long v1GroupRecordId = getLastVersionGroupRecordId(req, resp, group);
        // 2.2 确定当前版本的group_record_id
        PplVersionGroupRecordDO lastRecord = group.getLastRecord();
        if (lastRecord == null) {
            throw new BizException("当前版本的record信息为空，属于系统异常，请联系开发排查");
        }
        Long v2GroupRecordId = lastRecord.getId();

        // 3.1 字典接口
        setDictValue(resp, v2GroupRecordId);

        List<DateUtils.YearMonth> yearMonths = DateUtils.listYearMonth(group.getPplVersionDO().getDemandBeginYear(),
                group.getPplVersionDO().getDemandBeginMonth(),
                group.getPplVersionDO().getDemandEndYear(), group.getPplVersionDO().getDemandEndMonth());

        // 3.2 统计比较v2和v1，汇总
        Map<String, Object> param = new HashMap<>();
        StringBuilder sb = getFilterWhere(req, param, group.getPplVersionDO());
        param.put("v1GroupRecordId", v1GroupRecordId);
        param.put("v2GroupRecordId", v2GroupRecordId);

        getSummaryData(req, resp, group, yearMonths, param, sb);

        // 3.3 统计比较v2和v1，满足方式, 废弃  ⚠️ ⚠️ ⚠️️
//        getSatisfyData(req, resp, group, yearMonths, param, filterWhere);

        return resp;
    }

    @SneakyThrows
    @Override
    public VersionGroupSpikeViewResp queryVersionGroupSpikeView(VersionGroupStatReq req) {

        Long groupId = req.getGroupId();
        PplVersionGroupInfoVO vo = getGroup(groupId);
        checkGroupViewPermission(vo);
        PplVersionGroupRecordDO latestRecord = vo.getLastRecord();
        Assert.notNull(latestRecord, "分组ID:" + groupId + "不存在明细记录");

        // 毛刺的执行量, 这个是历史实际的执行量
        QuerySplitMiddleResultParams params = new QuerySplitMiddleResultParams();
        params.setIndustryDept(Lang.list(vo.getIndustryDept()));
        params.setRegionNames(req.getRegionNames());
        params.setInstanceTypes(req.getInstanceTypes());

        PplForecastPredictTaskDO lastTask = getLastestTask(req);

        boolean isIndustry = Objects.equals("外部业务", req.getBizRangeType());
        //PplForecastPredictTaskDO lastWholeLongtailPredictTask = pplForecastNewMiddleService.getLastWholeLongtailPredictTask(
        //        isIndustry);

        PplForecastPredictTaskDO lastTaskForFutureForecast =
                pplForecastNewMiddleService.getLastHeadCustomerWithSpikePredictTask(isIndustry);

        // 特别说明：2024年5月31日 切换到未来阶段之后，毛刺要取全部中长尾的范围下的毛刺
        // 2024年6月12日17:49:51 更新为头部+毛刺的执行量
        CompletableFuture<List<PplForecastInputExcludedSpikeDO>> forecastSpikeTask =
                CompletableFuture.supplyAsync(
                        () -> SpringUtil.getBean(PplVersionGroupServiceImpl.class)
                                .getSpikeData(params, lastTaskForFutureForecast /*lastWholeLongtailPredictTask*/, req.getIsShowHeaderCustomerDetail()));
        // 特别说明：对于常规项目，则还是用原来651的方案范围，这是因为按之前选到，行业只需要录入非31个大客户+2023年非中长尾之外的270个客户的PPL
        CompletableFuture<List<DailyZoneAppidGinstypePaymodeApproleDTO>> forecastNotSpikeTask =
                CompletableFuture.supplyAsync(() ->
                        SpringUtil.getBean(PplVersionGroupServiceImpl.class).getMiddleFromCk(params, lastTask));

        // 当前最新
        CompletableFuture<List<VersionGroupSpikeViewDetail>> futurePplTask =
                CompletableFuture.supplyAsync(() -> getPplForecast(req, vo));
        // 查询指定时间之前(历史)的最新版的PPL数据，并进行毛刺划分打标
        CompletableFuture<List<VersionGroupSpikeViewDetail>> historyNewestPplTask =
                CompletableFuture.supplyAsync(() -> this.getHistoryNewestPpl(vo, params, req));

        CompletableFuture.allOf(futurePplTask, forecastSpikeTask, forecastNotSpikeTask).join();

        List<PplForecastInputExcludedSpikeDO> forecastSpikeData = forecastSpikeTask.get();
        List<DailyZoneAppidGinstypePaymodeApproleDTO> forecastNotSpikeData = forecastNotSpikeTask.get();

        List<VersionGroupSpikeViewDetail> versionLatestRecordWithFilter = futurePplTask.get();
        List<VersionGroupSpikeViewDetail> historyNewestPpl = historyNewestPplTask.get();

        // 机型收敛配置表
        Map<String, String> instanceTypeToMergedType = pplDictService.queryInstanceTypeRelate().getRight();

        return VersionGroupSpikeViewResp.aggregateToVersionGroupSpikeViewResp(
                versionLatestRecordWithFilter,
                forecastSpikeData, forecastNotSpikeData,
                historyNewestPpl, instanceTypeToMergedType);
    }

    @NotNull
    private List<VersionGroupSpikeViewDetail> getPplForecast(VersionGroupStatReq req, PplVersionGroupInfoVO vo) {

        // 干预后的数据直接从数据库中获取
        boolean isAfterComd = req.getIsComd() == null || req.getIsComd();

        StringBuilder commonWhere = new StringBuilder();
        String sql = getSpikeViewSql(isAfterComd, commonWhere);

        PplVersionGroupRecordDO latestRecord = vo.getLastRecord();
        Map<String, Object> commonParam = new HashMap<>();
        commonParam.put("versionRecordId", latestRecord.getId());

        List<VersionGroupSpikeViewDetail> detail2;
        {
            Map<String, Object> filterParam = new HashMap<>(commonParam);
            StringBuilder filterSb = getFilterWhere(req, filterParam, vo.getPplVersionDO());
            filterSb.append(commonWhere);
            String sql2 = sql.replace("${FILTER}", filterSb.toString());
            detail2 = demandDBHelper.getRaw(VersionGroupSpikeViewDetail.class, sql2, filterParam);
            boolean filterIsIndustry = "外部业务".equals(req.getBizRangeType());
            // 过滤内外部的数据
            detail2 = filterInnerOrIndustry(detail2, filterIsIndustry);
        }

        boolean isSpikeInitialized = detail2.stream().noneMatch((o) -> Objects.equals(o.getIsSpike(), -1));

        // 干预前的刷数，把isSpike 重新刷一遍数据，还没初始化的时候，也使用
        if (!isAfterComd || !isSpikeInitialized) {
            String sql1 = sql.replace("${FILTER}", commonWhere.toString());
            List<VersionGroupSpikeViewDetail> versionLatestRecordDetail =
                    demandDBHelper.getRaw(VersionGroupSpikeViewDetail.class, sql1, commonParam);
            // 上面是全量的数据，打标也是按照全量的数据来打标
            // ----
            // 下面是过滤后的实际展示量，毛刺和非毛刺用上面的维度来过滤
            // 对 detail 的isSpike 字段打标
            setSpikeColumn(versionLatestRecordDetail);
            Map<VersionGroupSpikeViewDetailKey, Integer> spikeMap = ListUtils.toMap(versionLatestRecordDetail,
                    (o) -> {
                        VersionGroupSpikeViewDetailKey key = new VersionGroupSpikeViewDetailKey();
                        BeanUtils.copyProperties(o, key);
                        return key;
                    }, VersionGroupSpikeViewDetail::getIsSpike);
            // 打标
            setSpikeColumn(spikeMap, detail2);
        }
        return detail2;
    }

    @NotNull
    private String getSpikeViewSql(boolean isAfterComd, StringBuilder sb) {
        String sql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_spike_view.sql");
        if (isAfterComd) {
            sb.append(" AND a.is_comd != 1");  // 干预后
            sql = sql.replace("${GROUP_IS_SPIKE}", ",is_spike");
        } else {
            sb.append(" AND b.source != 'COMD_INTERVENE'"); // 干预前
            sql = sql.replace("${GROUP_IS_SPIKE}", "");
        }
        return sql;
    }

    private void setSpikeColumn(
            Map<VersionGroupSpikeViewDetailKey, Integer> spikeMap, List<VersionGroupSpikeViewDetail> detail2) {
        detail2.forEach((o) -> {
            VersionGroupSpikeViewDetailKey key = new VersionGroupSpikeViewDetailKey();
            BeanUtils.copyProperties(o, key);
            o.setIsSpike(spikeMap.getOrDefault(key, 2)); // 默认没有找到的就不参与
        });
    }

    private List<VersionGroupSpikeViewDetail> filterInnerOrIndustry(List<VersionGroupSpikeViewDetail> detail2,
            boolean filterIsIndustry) {
        detail2 = detail2.stream().filter((o) -> {
            // 模型预测的不参与打标，全部属于非毛刺
            if (Objects.equals(FORECAST_INDUSTRY_CUSTOMER_NAME.getName(), o.getCustomer())
                    && filterIsIndustry) {
                return true;
            }
            if (Objects.equals(FORECAST_INNER_CUSTOMER_NAME.getName(), o.getCustomer())
                    && !filterIsIndustry) {
                return true;
            }
            // 通过名称去判断内外部
            List<IndustryReportAppidInfoLatestWithoutJsonDO> innerCustomer = pplCommonService.getInnerCustomer();
            boolean isInner = ListUtils.contains(innerCustomer, (inner) ->
                    Objects.equals(inner.getCustomerShortName(), o.getCustomer()) ||
                            Objects.equals(String.valueOf(inner.getUin()), o.getCustomer()));
            return !Objects.equals(isInner, filterIsIndustry);
        }).collect(Collectors.toList());
        return detail2;
    }


    private PplForecastPredictTaskDO getLastestTask(VersionGroupStatReq req) {
        checkReq(req);
        boolean isIndustry = Objects.equals("外部业务", req.getBizRangeType());
        boolean isKeepUin = Objects.equals(req.getIsKeepUin(), true);
        return pplForecastNewMiddleService.getLastIndustryPredictTask(isIndustry, isKeepUin);
    }


    private void checkReq(VersionGroupStatReq req) {
        if (Strings.isBlank(req.getBizRangeType())) {
            throw BizException.makeThrow("内外部没有指定： bizRangeType");
        }
        if (Objects.isNull(req.getIsKeepUin())) {
            throw BizException.makeThrow("是否保留客户维度没有指定：isKeepUin");
        }
        if (!Lang.list("内部业务", "外部业务").contains(req.getBizRangeType())) {
            throw BizException.makeThrow("bizRangeType: 字段错误");
        }
    }

    /**
     * 查询历史月份的最新的PPL数据，并进行毛刺划分打标
     */
    private List<VersionGroupSpikeViewDetail> getHistoryNewestPpl(PplVersionGroupInfoVO groupInfoVO,
            QuerySplitMiddleResultParams params,
            VersionGroupStatReq req) {
        // 1. 查询最细的PPL数据，只过滤行业部门和产品

        // 页面接口，干预前是false，干预后是true
        boolean isAfterComd = req.getIsComd() == null || req.getIsComd();
        String sql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_ppl_newest_view.sql");

        StringBuilder partWhereSql = new StringBuilder();
        if (isAfterComd) { // 干预后
            partWhereSql.append(" AND is_comd!=1 ");
            sql = sql.replace("${GROUP_IS_SPIKE}", ",is_spike");
            sql = sql.replace("${IS_SPIKE_COLUMN}", "is_spike,");
        } else { // 干预前
            partWhereSql.append(" AND source!='COMD_INTERVENE' ");
            sql = sql.replace("${GROUP_IS_SPIKE}", "");
            sql = sql.replace("${IS_SPIKE_COLUMN}", "-1 as is_spike,");
        }

        Map<String, Object> sqlParams = new HashMap<>();
        sqlParams.put("industryDepts", params.getIndustryDept());
        sqlParams.put("product", groupInfoVO.getProduct());

        partWhereSql.append(" AND year>=2023 "); // 取2023年起的数据，后续可以改成取最近1年
        Integer beginYear = groupInfoVO.getPplVersionDO().getDemandBeginYear();
        Integer beginMonth = groupInfoVO.getPplVersionDO().getDemandBeginMonth();
        partWhereSql.append(" AND (year<:beginYear or (year=:beginYear and month<:beginMonth)) "); // 取版本之前的历史月份的最新数据
        sqlParams.put("beginYear", beginYear);
        sqlParams.put("beginMonth", beginMonth);

        sql = sql.replace("${FILTER}", partWhereSql.toString());
        List<VersionGroupSpikeViewDetail> detail =
                ckcldStdCrpDBHelper.getRaw(VersionGroupSpikeViewDetail.class, sql, sqlParams);

        // 2. 进行毛刺划分打标,干预前的内存中覆盖就好了
        // 特别注意，这里需要指定单个 product， 一个product 只会有一类状态
        // 打标后，一类数据要么全是-1，要么一定有值
        if (!isAfterComd) {
            setSpikeColumn(detail);
        } else {
            List<VersionGroupSpikeViewDetail> notInitDetail = detail.stream()
                    .filter((o) -> Objects.equals(o.getIsSpike(), -1))
                    .collect(Collectors.toList());
            setSpikeColumn(notInitDetail);
        }

        // 3. 按params参数进行region name 和 instance type的过滤
        if (ListUtils.isNotEmpty(params.getRegionNames())) {
            detail = ListUtils.filter(detail, (o) -> params.getRegionNames().contains(o.getRegionName()));
        }
        if (ListUtils.isNotEmpty(params.getInstanceTypes())) {
            detail = ListUtils.filter(detail, (o) -> params.getInstanceTypes().contains(o.getInstanceType()));
        }

        boolean filterIsIndustry = "外部业务".equals(req.getBizRangeType());
        detail = filterInnerOrIndustry(detail, filterIsIndustry);

        return detail;
    }

    /**
     * 对查出来的ppl 进行打标， queryVersionGroupSpikeView, 历史最新，当前最新的ppl两个都打标了
     */
    private void setSpikeColumn(List<VersionGroupSpikeViewDetail> detail) {
        Map<String, String> region2CustomhouseTitleMap = getRegion2CustomhouseTitleMap();

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        Set<String> headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();

        // 重新打标，先给一个默认值
        detail.forEach((o) -> o.setIsSpike(0));

        detail = detail.stream().filter((o) -> {
                    // 头部客户的一律是重点项目，优先级比黑名单机型高
                    boolean isHeadCustomer = headCustomerShortName.contains(o.getCustomerShortName());
                    if (isHeadCustomer) {
                        o.setIsSpike(1);
                        return false;
                    }

                    boolean isBlackInstanceType = allBlackInstanceType.contains(o.getInstanceType());
                    if (isBlackInstanceType) {
                        o.setIsSpike(2);
                        return false;
                    }

                    // 模型预测的不参与打标，全部属于非毛刺
                    if (isForecast(o.getCustomer())) {
                        o.setIsSpike(0);
                        return false;
                    }

                    return true;
                })
                .collect(Collectors.toList());

        ForSpikeParams<VersionGroupSpikeViewDetail> forSpikeParams = new ForSpikeParams<>();
        forSpikeParams.setData(detail);
        forSpikeParams.setMatchConfigFunc((items, config) -> {
            BigDecimal sum = NumberUtils.sum(items, VersionGroupSpikeViewDetail::getForecastNum);
            if (sum.compareTo(BigDecimal.valueOf(config.getThreshold())) >= 0) {
                items.forEach((o) -> o.setIsSpike(1));
                return true;
            }
            return false;
        });

        forSpikeParams.setRegionNameFunc(VersionGroupSpikeViewDetail::getRegionName);
        forSpikeParams.setCustomhouseTitleFunc(
                (o) -> region2CustomhouseTitleMap.getOrDefault(o.getRegionName(), ""));
        forSpikeParams.setInstanceTypeFunc(
                o -> forSpikeParams.getInstanceTypeToGroup()
                        .getOrDefault(o.getInstanceType(), o.getInstanceType()));

        forSpikeParams.setOtherFunc((o) -> Strings.join("@",
                o.getYearMonth(),
                o.getCustomer(),
                o.getDemandType()
        ));
        Ppl13WeekInputServiceImpl.applyConfig(forSpikeParams);
    }

    @NotNull
    private Map<String, String> getRegion2CustomhouseTitleMap() {
        Map<String, String> domestic = pplDictService.queryAllCityName(new DictCommonReq() {{
            this.setAreaName("domestic");
        }}).stream().collect(Collectors.toMap((o) -> o, o -> "境内", (a, b) -> a));

        Map<String, String> oversea = pplDictService.queryAllCityName(new DictCommonReq() {{
            this.setAreaName("oversea");
        }}).stream().collect(Collectors.toMap((o) -> o, o -> "境外", (a, b) -> a));

        domestic.putAll(oversea);
        return domestic;
    }

    @Override
    public VersionGroupForecastResultViewResp queryVersionGroupForecastResultView(VersionGroupStatReq req) {
        PplVersionGroupInfoVO group = getGroup(req.getGroupId());
        VersionGroupForecastResultViewResp resp = new VersionGroupForecastResultViewResp();

        checkReq(req);
        boolean isIndustry = Objects.equals("外部业务", req.getBizRangeType());
        boolean isKeepUin = Objects.equals(req.getIsKeepUin(), true);
        String categoryPrefix = pplForecastNewMiddleService.getCategoryPrefix(isIndustry, isKeepUin);
        List<PplForecastPredictTaskDO> enabledPredictTask =
                ppl13weekQueryService.getEnabledPredictTask(categoryPrefix);

        // 只要2023年1月起的预测
        enabledPredictTask = ListUtils.filter(enabledPredictTask, o ->
                "2022-12-31".compareTo(com.pugwoo.wooutils.lang.DateUtils.formatDate(o.getInputDateEnd())) <= 0);
        if (Lang.isEmpty(enabledPredictTask)) {
            throw new WrongWebParameterException("预测方案：" + categoryPrefix + "不存在或者未启用");
        }
        QuerySplitMiddleResultParams params = new QuerySplitMiddleResultParams();
        params.setIndustryDept(Lang.list(group.getIndustryDept()));
        params.setRegionNames(req.getRegionNames());
        params.setInstanceTypes(req.getInstanceTypes());

        // 这个lastTask就还是6xx方案的任务，为了取常规执行量
        final PplForecastPredictTaskDO lastTask = enabledPredictTask.get(enabledPredictTask.size() - 1);

        // 未来的预测量，重点项目
        // 2. fill spikeForecastNum 【切换到毛刺了】
        PplForecastPredictTaskDO lastTaskForFutureForecast =
                pplForecastNewMiddleService.getLastHeadCustomerWithSpikePredictTask(isIndustry);
        CompletableFuture<Void> futureForecastTask = CompletableFuture.runAsync(() -> {
            QuerySplitMiddleResultParams paramsForLastTask = copyOne(params);
            paramsForLastTask.setTaskId(Lang.list(lastTaskForFutureForecast.getId()));
            List<PplForecastPredictResultSplitMiddleDO> splitMiddleResult =
                    ppl13weekQueryService.getSplitMiddleResult(paramsForLastTask);
            resp.setDict(splitMiddleResult,
                    PplForecastPredictResultSplitMiddleDO::getRegionName,
                    PplForecastPredictResultSplitMiddleDO::getGinsFamily);
            resp.fillSplitMiddleResult(splitMiddleResult);
        });

        // new -  YearMonth  DimKey  [real 实际值=0, forecast 预测值=0]
        // ret  - YearMonth  DimKey  [real 实际值=0, forecast 预测值=0]
        List<Table<java.time.YearMonth, DimKey, DimValue>> newRetDetailList = new ArrayList<>();
        newRetDetailList.add(HashBasedTable.create());
        newRetDetailList.add(HashBasedTable.create());

        // 历史的预测量，重点项目
        // 3. fill spikeForecastNum
        final List<PplForecastPredictTaskDO> finalEnabledPredictTask = pplForecastNewMiddleService.getHeadCustomerWithSpikePredictTask(
                isIndustry);

        CompletableFuture<Void> historyForecastTask = CompletableFuture.runAsync(() -> {
            if (finalEnabledPredictTask.size() > 1) {
                List<PplForecastPredictTaskDO> historyTasks = finalEnabledPredictTask.subList(0,
                        finalEnabledPredictTask.size() - 1);
                List<Long> taskIds = historyTasks.stream().map(PplForecastPredictTaskDO::getId)
                        .collect(Collectors.toList());

                QuerySplitMiddleResultParams paramsForHistory = copyOne(params);
                paramsForHistory.setTaskId(taskIds);
                // 这里取最新的数据
                paramsForHistory.setPredictIndexs(Lang.list(1));

                List<PplForecastPredictResultSplitMiddleDO> historyMiddleResult =
                        ppl13weekQueryService.getSplitMiddleResult(paramsForHistory);
                resp.setDict(historyMiddleResult,
                        PplForecastPredictResultSplitMiddleDO::getRegionName,
                        PplForecastPredictResultSplitMiddleDO::getGinsFamily);

                historyMiddleResult.forEach((o) -> {
                    java.time.YearMonth ym = java.time.YearMonth.of(o.getYear(), o.getMonth());
                    DimKey dimKey = new DimKey(o.getGinsFamily(), o.getRegionName());

                    DimValue realForecastValue = null;
                    if (Strings.equals(o.getType(), PplForecastTypeEnum.NEW.getType())) {
                        realForecastValue = getOrDefault(newRetDetailList.get(0), ym, dimKey);
                    } else if (Strings.equals(o.getType(), PplForecastTypeEnum.RET.getType())) {
                        realForecastValue = getOrDefault(newRetDetailList.get(1), ym, dimKey);
                    }
                    Assert.notNull(realForecastValue, "不会出现");
                    BigDecimal cur = realForecastValue.getNotSpikeForecastNum();
                    realForecastValue.setNotSpikeForecastNum(cur.add(o.getCoreNum()));
                });
                resp.fillSplitMiddleResult(historyMiddleResult);
            }
        });

        // 4.历史常规项目执行量
        CompletableFuture<Void> notSpikeExecuteTask = CompletableFuture.runAsync(() -> {
            List<DailyZoneAppidGinstypePaymodeApproleDTO> raw = getMiddleFromCk(params, lastTask);
            // 这里加上机型收敛

            List<PplForecastInputDetailDO> newTransData = ppl13weekInputService.transInstanceType(transNew(raw));
            List<PplForecastInputDetailDO> retTransData = ppl13weekInputService.transInstanceType(transRet(raw));

            resp.setDict(newTransData, PplForecastInputDetailDO::getRegionName,
                    PplForecastInputDetailDO::getGinsFamily);
            resp.setDict(retTransData, PplForecastInputDetailDO::getRegionName,
                    PplForecastInputDetailDO::getGinsFamily);

            newTransData.forEach((o) -> {
                java.time.YearMonth ym = java.time.YearMonth.of(o.getYear(), o.getMonth());
                DimKey dimKey = new DimKey(o.getGinsFamily(), o.getRegionName());
                DimValue realForecastValue = getOrDefault(newRetDetailList.get(0), ym, dimKey);
                realForecastValue.setNotSpikeExecuteNum(o.getDiffCoreNum());
            });

            retTransData.forEach((o) -> {
                java.time.YearMonth ym = java.time.YearMonth.of(o.getYear(), o.getMonth());
                DimKey dimKey = new DimKey(o.getGinsFamily(), o.getRegionName());
                DimValue realForecastValue = getOrDefault(newRetDetailList.get(1), ym, dimKey);
                realForecastValue.setNotSpikeExecuteNum(o.getDiffCoreNum());
            });

            resp.filNotSpikeMiddleExecute(raw, newTransData, retTransData);
        });

        // 5.历史重点项目的执行量
        CompletableFuture<Void> spikeExecuteTask = CompletableFuture.runAsync(() -> {
            List<PplForecastInputExcludedSpikeDO> raw = getSpikeData(params, lastTaskForFutureForecast, req.getIsShowHeaderCustomerDetail());
            resp.setDict(raw, PplForecastInputExcludedSpikeDO::getRegionName,
                    PplForecastInputExcludedSpikeDO::getInstanceType,
                    PplForecastInputExcludedSpikeDO::getCustomerUin,
                    PplForecastInputExcludedSpikeDO::getCustomerShortName);
            Map<String, String> instanceTypeToMergedType = pplDictService.queryInstanceTypeRelate().getRight();
            resp.fillSpikeMiddleExecute(raw, instanceTypeToMergedType);
        });

        // 等待所有任务完成
        CompletableFuture.allOf(futureForecastTask, historyForecastTask, notSpikeExecuteTask, spikeExecuteTask).join();

        // 计算毛刺准确率
        calSpikeMinMaxRate(resp.getNewDataList());
        calSpikeMinMaxRate(resp.getRetDataList());

        ListUtils.sortAscNullLast(resp.getNewDataList(), VersionGroupForecastResultViewResp.SummaryDTO::getYearMonth);
        ListUtils.sortAscNullLast(resp.getRetDataList(), VersionGroupForecastResultViewResp.SummaryDTO::getYearMonth);

        resp.distinctDict();
        return resp;
    }

    private void calSpikeMinMaxRate(List<VersionGroupForecastResultViewResp.SummaryDTO> list) {
        for (VersionGroupForecastResultViewResp.SummaryDTO summary : list) {
            Map<String, List<VersionGroupForecastResultViewResp.DetailDTO>> executeMap = ListUtils.toMapList(
                    summary.getSpikeExecuteDetail(),
                    o -> StringTools.join("@", o.getRegionName(),
                            StringTools.isNotBlank(o.getTranserInstanceType()) ? o.getTranserInstanceType()
                                    : o.getInstanceType()),
                    o -> o);
            Map<String, List<VersionGroupForecastResultViewResp.DetailDTO>> forecastMap = ListUtils.toMapList(
                    summary.getSpikeForecastDetail(),
                    o -> StringTools.join("@", o.getRegionName(), o.getInstanceType()),
                    o -> o);// 预测肯定是机型收敛的，所以用instanceType

            if (summary.getSpikeExecuteNum() == null || summary.getSpikeExecuteNum() <= 0) {
                continue; // 没有执行量数据，不算准确率
            }
            BigDecimal minMaxRate = BigDecimal.ZERO;

            for (Entry<String, List<VersionGroupForecastResultViewResp.DetailDTO>> e : executeMap.entrySet()) {
                BigDecimal executeCore = NumberUtils.sum(e.getValue(), o -> o.getNum());
                if (executeCore.compareTo(BigDecimal.ZERO) == 0) {
                    continue;
                }
                BigDecimal forecastCore = NumberUtils.sum(forecastMap.get(e.getKey()), o -> o.getNum());
                BigDecimal rate = NumberUtils.divide(NumberUtils.min(executeCore, forecastCore),
                        NumberUtils.max(executeCore, forecastCore), 6);
                minMaxRate = minMaxRate.add(
                        NumberUtils.divide(rate.multiply(executeCore), summary.getSpikeExecuteNum(), 2));
            }

            summary.setSpikeMinMaxRate(minMaxRate);
        }
    }

    private List<PplForecastInputDetailDO> transRet(List<DailyZoneAppidGinstypePaymodeApproleDTO> raw) {
        List<PplForecastInputDetailDO> pplForecastInputDetailDOlist = Lists.newArrayList();
        for (DailyZoneAppidGinstypePaymodeApproleDTO i : raw) {
            PplForecastInputDetailDO e = convertFrom(i);
            e.setType(PplForecastTypeEnum.RET.getType());
            e.setDiffCoreNum(i.getRetDiff());
            pplForecastInputDetailDOlist.add(e);
        }
        return pplForecastInputDetailDOlist;
    }


    private List<PplForecastInputDetailDO> transNew(List<DailyZoneAppidGinstypePaymodeApproleDTO> raw) {
        List<PplForecastInputDetailDO> pplForecastInputDetailDOlist = Lists.newArrayList();
        for (DailyZoneAppidGinstypePaymodeApproleDTO i : raw) {
            PplForecastInputDetailDO tmp = convertFrom(i);
            tmp.setType(PplForecastTypeEnum.NEW.getType());
            tmp.setDiffCoreNum(i.getNewDiff());
            pplForecastInputDetailDOlist.add(tmp);
        }
        return pplForecastInputDetailDOlist;
    }

    private PplForecastInputDetailDO convertFrom(DailyZoneAppidGinstypePaymodeApproleDTO source) {
        PplForecastInputDetailDO pplForecastInputDetailDO = new PplForecastInputDetailDO();
        pplForecastInputDetailDO.setInputId(0L);
        pplForecastInputDetailDO.setStatTime(source.getFirstDay());
        pplForecastInputDetailDO.setYear(source.getYear());
        pplForecastInputDetailDO.setMonth(source.getMonth());
        pplForecastInputDetailDO.setCustomhouseTitle(source.getCustomhouseTitle());
        pplForecastInputDetailDO.setGinsFamily(source.getGinsFamily());
        pplForecastInputDetailDO.setRegion(source.getRegion());
        pplForecastInputDetailDO.setRegionName(source.getRegionName());
        pplForecastInputDetailDO.setCoreNum(source.getLastDayNum());
        return pplForecastInputDetailDO;
    }

    @NotNull
    private DimValue getOrDefault(Table<java.time.YearMonth, DimKey, DimValue> table,
            java.time.YearMonth ym, DimKey dimKey) {
        DimValue bigDecimals = table.get(ym, dimKey);
        if (bigDecimals == null) {
            bigDecimals = new DimValue(BigDecimal.ZERO, BigDecimal.ZERO);
            table.put(ym, dimKey, bigDecimals);
        }
        return bigDecimals;
    }

    @NotNull
    private BigDecimal getMinMax(BigDecimal real, BigDecimal forecast) {
        BigDecimal notSpikeForecastNum = forecast == null ? BigDecimal.ZERO : forecast;
        BigDecimal notSpikeExecuteNum = real == null ? BigDecimal.ZERO : real;
        BigDecimal min = notSpikeForecastNum.min(notSpikeExecuteNum);
        BigDecimal max = notSpikeForecastNum.max(notSpikeExecuteNum);
        BigDecimal minMax = BigDecimal.ZERO;
        if (max.compareTo(BigDecimal.ZERO) != 0) {
            minMax = min.divide(max, MathContext.DECIMAL128);
        }
        return minMax;
    }

    /**
     * 查询指定任务的毛刺执行量，支持筛选
     *
     * @param params 筛选条件
     * @param lastTask 指定的预测任务
     * @return 剔除的毛刺明细数据
     */
    @HiSpeedCache(expireSecond = 60 * 10, keyScript = "args[0]+args[1]+args[2]")
    public List<PplForecastInputExcludedSpikeDO> getSpikeData(QuerySplitMiddleResultParams params,
            PplForecastPredictTaskDO lastTask, Boolean isShowHeaderCustomerDetail) {
        WhereContent wc = new WhereContent();
        class DbInfo extends PplForecastInputExcludedSpikeDO {

        }
        wc.andInIfValueNotEmpty(DbInfo::getTaskId, Lang.list(lastTask.getId()));
        wc.andInIfValueNotEmpty(DbInfo::getIndustryDept, params.getIndustryDept());
        wc.andInIfValueNotEmpty(DbInfo::getRegionName, params.getRegionNames());

        if (Lang.isNotEmpty(params.getInstanceTypes())) {
            List<String> transType = transCommonModel2Detail(params.getInstanceTypes());
            wc.andIn(DbInfo::getInstanceType, transType);
        }

        LocalDate end = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        // 统一从2023年开始
        wc.addAnd("year>=2023");
        wc.addAnd("year<? or (year=? and month<=?)", end.getYear(), end.getYear(), end.getMonthValue());

        List<PplForecastInputExcludedSpikeDO> list =
                demandDBHelper.getAll(PplForecastInputExcludedSpikeDO.class, wc.getSql(), wc.getParams());

        // 聚合到年月、客户简称（如果没有则用uin）、地域、机型进行聚合
        Map<String, List<PplForecastInputExcludedSpikeDO>> map =
                ListUtils.toMapList(list, o -> StringTools.join("@", o.getYear(), o.getMonth(),
                        (isCustomerNameEmpty(o.getCustomerShortName()) ? o.getCustomerUin() : o.getCustomerShortName()),
                        o.getRegionName(), o.getInstanceType()), o -> o);

        List<PplForecastInputExcludedSpikeDO> result = new ArrayList<>();
        for (Map.Entry<String, List<PplForecastInputExcludedSpikeDO>> entry : map.entrySet()) {
            List<PplForecastInputExcludedSpikeDO> value = entry.getValue();
            PplForecastInputExcludedSpikeDO r = JSON.clone(value.get(0));
            r.setId(null);
            r.setCustomerUin(
                    StringTools.join("/", ListUtils.toSet(value, PplForecastInputExcludedSpikeDO::getCustomerUin)));
            r.setNewCore(NumberUtils.sum(value, PplForecastInputExcludedSpikeDO::getNewCore));
            r.setRetCore(NumberUtils.sum(value, PplForecastInputExcludedSpikeDO::getRetCore));
            result.add(r);
        }

        // 获取到 35 个头部
        List<PplForecastInputExcludedSpikeDO> header35Trans = getHeader35Detail(params, lastTask, isShowHeaderCustomerDetail);
        result.addAll(header35Trans);

        return result;
    }

    /**
     * @param isShowHeaderCustomerDetail 是否显示大客户明细
     */
    private List<PplForecastInputExcludedSpikeDO> getHeader35Detail(
            QuerySplitMiddleResultParams params, PplForecastPredictTaskDO lastTask,
            Boolean isShowHeaderCustomerDetail) {

        String appendWhereSql = "";
        Map<String, Object> appendParams = new HashMap<>();
        if (Lang.isNotEmpty(params.getIndustryDept())) {
            appendWhereSql += "and industry_dept in (:industry_dept_filter) \n";
            appendParams.put("industry_dept_filter", params.getIndustryDept());
        }
        if (Lang.isNotEmpty(params.getRegionNames())) {
            appendWhereSql += "and region_name in (:region_name_filter) \n";
            appendParams.put("region_name_filter", params.getRegionNames());
        }
        if (Lang.isNotEmpty(params.getInstanceTypes())) {
            List<String> transType = transCommonModel2Detail(params.getInstanceTypes());
            appendWhereSql += "and instance_type in (:instance_type_filter) \n";
            if (ListUtils.isEmpty(transType)) { // 找不到机型收敛，就用用户传的
                appendParams.put("instance_type_filter", params.getInstanceTypes());
            } else {
                appendParams.put("instance_type_filter", transType);
            }
        }

        List<DailyZoneAppidGinstypePaymodeApproleWithCustomerDTO> header35 =
                ppl13weekInputService.getCloudWhole35HeadCustomer(lastTask, appendWhereSql, appendParams,
                        isShowHeaderCustomerDetail);

        return header35.stream().map((source) -> {
            PplForecastInputExcludedSpikeDO target = new PplForecastInputExcludedSpikeDO();
            target.setTaskId(0L);
            target.setYear(source.getYear());
            target.setMonth(source.getMonth());
            target.setIndustryDept("");
            if (isShowHeaderCustomerDetail != null && isShowHeaderCustomerDetail) {
                target.setCustomerUin(source.getCustomerUin());
                target.setCustomerShortName(source.getCustomerShortName());
            } else {
                target.setCustomerUin("*");
                target.setCustomerShortName("31个大客户名单");
            }
            target.setInstanceType(source.getGinsFamily());
            target.setRegionName(source.getRegionName());
            target.setZoneName("");
            target.setNewCore(source.getNewDiff().setScale(1, RoundingMode.HALF_UP));
            target.setRetCore(source.getRetDiff().setScale(1, RoundingMode.HALF_UP));
            return target;
        }).collect(Collectors.toList());
    }

    private boolean isCustomerNameEmpty(String customerShortName) {
        return StringTools.isBlank(customerShortName) || "(空值)".equals(customerShortName);
    }

    @HiSpeedCache(expireSecond = 60 * 10, keyScript = "args[0]+args[1]")
    public List<DailyZoneAppidGinstypePaymodeApproleDTO> getMiddleFromCk(
            QuerySplitMiddleResultParams params, PplForecastPredictTaskDO lastTask) {
        PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.getByCode(lastTask.getInputGroupDims());

        StringBuilder sqlCondition = new StringBuilder(lastTask.getInputSqlConditionNew() + "\n");
        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> sqlParams = new HashMap<>();
        BiConsumer<String, List<?>> addIn = (columnName, list) -> {
            if (list == null || list.isEmpty()) {
                return;
            }
            sqlCondition.append(" and ").append(columnName).append(" in (:").append(columnName).append(") \n");
            sqlParams.put(columnName, list);
        };
        addIn.accept("industry_dept", params.getIndustryDept());
        addIn.accept("region_name", params.getRegionNames());

        if (Lang.isNotEmpty(params.getInstanceTypes())) {
            List<String> p = transCommonModel2Detail(params.getInstanceTypes());
            if (Lang.isEmpty(p)) {
                p.add("EMPTY");
            }
            addIn.accept("instance_type", p);
        }

        // 这里复现一遍查询过程，加上上面3个参数
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/middle/monthly_data.sql");
        sql = sql.replace("/*${WEB_CONDITION}*/", " and stat_time in (:predictDateWeb) ");

//        sqlCondition.append(" and stat_time >= :startPredictDate");
//        sqlParams.put("startPredictDate", "2023-01-01");
//        sqlCondition.append(" /* and stat_time < predictDate: */ ");
//        sqlParams.put("predictDate", LocalDate.now().plusMonths(1).withDayOfMonth(1));

        LocalDate end = LocalDate.now().plusMonths(1).withDayOfMonth(1);
        // LocalDate start = end.minusYears(1);
        LocalDate start = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate("2023-01"); // 统一从2023年1月开始

        List<LocalDate> monthEndDates = new ArrayList<>();
        while (start.isBefore(end)) {
            monthEndDates.add(start.withDayOfMonth(start.lengthOfMonth()));
            start = start.plusMonths(1);
        }
        sqlParams.put("predictDate", end);  //兼容跑数据的sql
        sqlParams.put("predictDateWeb", monthEndDates);

        // 处理执行量的group by
        String sqlColumnJoinByComma = PplForecastGroupDimsEnum.getSqlColumnJoinByComma(groupDimsEnum);
        sql = sql.replace("${GROUP_BY_DIM}", sqlColumnJoinByComma);
        sql = sql.replace("${CONDITION}", sqlCondition);

        return DBList.ckcldStdCrpDBHelper.getRaw(DailyZoneAppidGinstypePaymodeApproleDTO.class, sql,
                sqlParams);
    }

    @NotNull
    private List<String> transCommonModel2Detail(List<String> instanceTypes) {
        List<PplForecastConfigSpikeThresholdDO> allSpikeThreshold = ppl13weekCommonDataAccess.getAllSpikeThreshold();
        List<String> p = Lang.list();
        if (Lang.isNotEmpty(instanceTypes)) {
            for (String instanceType : instanceTypes) {
                allSpikeThreshold.stream().filter((o) -> Strings.equals(o.getCommonInstanceType(), (instanceType)))
                        .forEach((o) -> {
                            p.addAll(o.getSplitInstanceTypes());
                        });
            }
        }
        return p;
    }

    @NotNull
    private QuerySplitMiddleResultParams copyOne(QuerySplitMiddleResultParams params) {
        QuerySplitMiddleResultParams rsp = new QuerySplitMiddleResultParams();
        BeanUtils.copyProperties(params, rsp);
        return rsp;
    }

    @Override
    public VersionGroupSummaryResp queryVersionGroupSummary(VersionGroupStatReq req) {

        VersionGroupSummaryResp resp = new VersionGroupSummaryResp();
        List<VersionGroupSummaryResp.SummaryDTO> dataList = resp.getDataList();
        PplVersionGroupInfoVO group = getGroup(req.getGroupId());
        PplVersionDO pplVersionDO = group.getPplVersionDO();
        // 2.1 确定上一版本的group_record_id
        Long beforeVersionRecordId = getLastVersionGroupRecordId(group);
        // 2.2 确定当前版本的group_record_id
        Long afterVersionRecordId = group.getLastRecord().getId();
        checkGroupViewPermission(group);
        resp.setEndYearMonth(pplVersionDO.getDemandEndYear().toString() + "-" +
                pplVersionDO.getDemandEndMonth().toString());
        Function<VersionGroupSummaryVO, Integer> func;
        Function<VersionGroupSummaryVO, Integer> applyFunc;
        Function<VersionGroupSummaryVO, Integer> lockFunc;
        Function<VersionGroupSummaryVO, Integer> diffFunc;
        Function<VersionGroupSummaryVO, Integer> diffApplyFunc;
        Function<VersionGroupSummaryVO, Integer> diffLockFunc;
        if (req.getResourceType().equals("GPU")) {
            func = VersionGroupSummaryVO::getTotalInstanceGpu;
            diffFunc = VersionGroupSummaryVO::getDiffInstanceGpu;
            applyFunc = VersionGroupSummaryVO::getTotalApplyCore;
            lockFunc = VersionGroupSummaryVO::getTotalLockCore;
            diffApplyFunc = VersionGroupSummaryVO::getDiffApplyCore;
            diffLockFunc = VersionGroupSummaryVO::getDiffLockCore;
            resp.setUnit("卡");
        } else if (req.getResourceType().equals("CVM")) {
            func = VersionGroupSummaryVO::getTotalInstanceNum;
            diffFunc = VersionGroupSummaryVO::getDiffInstanceNum;
            applyFunc = VersionGroupSummaryVO::getTotalApplyInstanceNum;
            lockFunc = VersionGroupSummaryVO::getTotalLockInstanceNum;
            diffApplyFunc = VersionGroupSummaryVO::getDiffApplyInstanceNum;
            diffLockFunc = VersionGroupSummaryVO::getDiffLockInstanceNum;
            resp.setUnit("台");
        } else if (req.getResourceType().equals("CORE")) {
            func = VersionGroupSummaryVO::getTotalInstanceCore;
            diffFunc = VersionGroupSummaryVO::getDiffInstanceCore;
            applyFunc = VersionGroupSummaryVO::getTotalApplyCore;
            lockFunc = VersionGroupSummaryVO::getTotalLockCore;
            diffApplyFunc = VersionGroupSummaryVO::getDiffApplyCore;
            diffLockFunc = VersionGroupSummaryVO::getDiffLockCore;
            resp.setUnit("核");
        } else {
            func = VersionGroupSummaryVO::getTotalDisk;
            diffFunc = VersionGroupSummaryVO::getDiffDisk;
            applyFunc = VersionGroupSummaryVO::getTotalApplyCore;
            lockFunc = VersionGroupSummaryVO::getTotalLockCore;
            diffApplyFunc = VersionGroupSummaryVO::getDiffApplyCore;
            diffLockFunc = VersionGroupSummaryVO::getDiffLockCore;
            resp.setUnit("GB");
        }
        Map<String, Object> param = new HashMap<>();
        StringBuilder sb = getFilterWhere(req, param, group.getPplVersionDO());

        param.put("beforeVersionRecordId", beforeVersionRecordId);
        param.put("afterVersionRecordId", afterVersionRecordId);

        // 先查原始预测的
        StringBuilder originalSb = new StringBuilder(sb);
        originalSb.append(" AND b.source != 'COMD_INTERVENE'");
        String originalSql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_summary.sql");
        originalSql = originalSql.replace("${FILTER}", originalSb.toString());
        List<VersionGroupSummaryVO> originalVOs = demandDBHelper.getRaw(VersionGroupSummaryVO.class, originalSql,
                param);
        buildSummaryResp(dataList, originalVOs, func, diffFunc, applyFunc, lockFunc, diffApplyFunc, diffLockFunc,
                false);
        if (group.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())
                || group.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            return resp;
        }
        // 查干预后的
        StringBuilder comdSb = new StringBuilder(sb);
        comdSb.append(" AND a.is_comd != 1");
        String comdSql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_summary.sql");
        comdSql = comdSql.replace("${FILTER}", comdSb.toString());
        List<VersionGroupSummaryVO> comdVOs = demandDBHelper.getRaw(VersionGroupSummaryVO.class, comdSql, param);
        buildSummaryResp(dataList, comdVOs, func, diffFunc, applyFunc, lockFunc, diffApplyFunc, diffLockFunc, true);
        return resp;
    }

    private void buildSummaryResp(List<VersionGroupSummaryResp.SummaryDTO> dataList,
            List<VersionGroupSummaryVO> summaryVOs,
            Function<VersionGroupSummaryVO, Integer> func, Function<VersionGroupSummaryVO, Integer> diffFunc,
            Function<VersionGroupSummaryVO, Integer> applyFunc, Function<VersionGroupSummaryVO, Integer> lockFunc,
            Function<VersionGroupSummaryVO, Integer> diffApplyFunc,
            Function<VersionGroupSummaryVO, Integer> diffLockFunc,
            Boolean isComd) {
        if (ListUtils.isEmpty(summaryVOs)) {
            return;
        }
        if (isComd) {
            // 处理云运管干预
            Map<String, VersionGroupSummaryResp.SummaryDTO> map = dataList.stream().collect(
                    Collectors.toMap(v -> Strings.join("-", Arrays.asList(v.getDemandType(), v.getYearMonth())),
                            v -> v));
            for (VersionGroupSummaryVO vo : summaryVOs) {
                String k = Strings.join("-", Arrays.asList(vo.getDemandType(), vo.getYearMonth()));
                VersionGroupSummaryResp.SummaryDTO summaryDTO = map.get(k);
                if (summaryDTO == null) {
                    summaryDTO = new VersionGroupSummaryResp.SummaryDTO();
                    dataList.add(summaryDTO);
                    summaryDTO.setDemandType(vo.getDemandType());
                    summaryDTO.setYearMonth(vo.getYearMonth());
                }
                summaryDTO.setComdForecastNum(func.apply(vo));
                summaryDTO.setComdApplyNum(applyFunc.apply(vo));
                summaryDTO.setComdLockNum(lockFunc.apply(vo));
                int stockSupplyNum = summaryDTO.getComdForecastNum() - summaryDTO.getComdLockNum();
                summaryDTO.setComdStockSupplyNum(Math.max(stockSupplyNum, 0));

                summaryDTO.setComdDiffForecastNum(diffFunc.apply(vo));
                summaryDTO.setComdDiffApplyNum(diffApplyFunc.apply(vo));
                summaryDTO.setComdDiffLockNum(diffLockFunc.apply(vo));
                // 库存预测差值 = 本周期库存预测 -（总预测核心 - 差值预测量） 上版本预测核心 - （锁定总核心 - 差值锁定量）上版本锁定核心
                int diffStockSupplyNum =
                        summaryDTO.getComdStockSupplyNum() -
                                (
                                        (vo.getTotalInstanceCore() - vo.getDiffInstanceCore()) -
                                                (vo.getTotalLockCore() - vo.getDiffLockCore())
                                );
                summaryDTO.setComdDiffStockSupplyNum(diffStockSupplyNum);
            }
        } else {
            for (VersionGroupSummaryVO vo : summaryVOs) {
                VersionGroupSummaryResp.SummaryDTO summaryDTO = new VersionGroupSummaryResp.SummaryDTO();
                summaryDTO.setDemandType(vo.getDemandType());
                summaryDTO.setYearMonth(vo.getYearMonth());
                summaryDTO.setForecastNum(func.apply(vo));
                summaryDTO.setApplyNum(applyFunc.apply(vo));
                summaryDTO.setLockNum(lockFunc.apply(vo));
                int stockSupplyNum = summaryDTO.getForecastNum() - summaryDTO.getLockNum();
                summaryDTO.setStockSupplyNum(Math.max(stockSupplyNum, 0));

                summaryDTO.setDiffForecastNum(diffFunc.apply(vo));
                summaryDTO.setDiffApplyNum(diffApplyFunc.apply(vo));
                summaryDTO.setDiffLockNum(diffLockFunc.apply(vo));
                // 库存预测差值 = 本周期库存预测 - （总预测核心 - 差值预测量） 上版本预测核心 - （锁定总核心 - 差值锁定量）上版本锁定核心
                int diffStockSupplyNum =
                        summaryDTO.getStockSupplyNum() -
                                (
                                        (vo.getTotalInstanceCore() - vo.getDiffInstanceCore()) -
                                                (vo.getTotalLockCore() - vo.getDiffLockCore())
                                );
                summaryDTO.setDiffStockSupplyNum(diffStockSupplyNum);

                dataList.add(summaryDTO);
            }

        }
    }

    private Long getLastVersionGroupRecordId(VersionGroupStatReq req, VersionGroupStatResp resp,
            PplVersionGroupInfoVO group) {
        List<PplVersionDO> versions = getLatestDoneVersion(group.getPplVersionDO(), group.getIndustryDept(),
                group.getProduct());

        String compareVersionCode = req.getCompareVersionCode();
        if (StringTools.isBlank(compareVersionCode) && ListUtils.isNotEmpty(versions)) {
            compareVersionCode = versions.get(0).getVersionCode();
        }
        resp.setCurrentCompareVersion(compareVersionCode);
        resp.setComparableVersions(ListUtils.transform(versions, o -> VersionGroupStatResp.VersionDTO.from(o)));

        // 2.1 确定上一版本的group_record_id
        Long v1GroupRecordId = 0L;
        if (StringTools.isNotBlank(compareVersionCode)) {
            PplVersionGroupRecordDO record = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                    "where version_group_id in (select id from ppl_version_group where" +
                            " deleted=0 and version_code=? and industry_dept=? and product=?) " +
                            " order by record_version desc",
                    compareVersionCode, group.getIndustryDept(), group.getProduct());
            if (record != null) {
                v1GroupRecordId = record.getId();
            }
        }
        return v1GroupRecordId;
    }

    /**
     * 若美丽上一个版本的 recordId 那么直接返回当前的。
     *
     * @param group
     * @return
     */
    private Long getLastVersionGroupRecordId(PplVersionGroupInfoVO group) {
        PplVersionGroupDO one = demandDBHelper.getOne(PplVersionGroupDO.class,
                "where industry_dept = ? and product = ? and status = ? and id < ? order by id desc",
                group.getIndustryDept(), group.getProduct(), PplVersionGroupStatusEnum.DONE.getCode(),
                group.getId());

        // 如果没有历史的 那么直接返回当前。
        if (one == null) {
            return group.getLastRecordId();
        }
        PplVersionGroupRecordDO recordDO = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where id in (select max(id) from ppl_version_group_record where deleted = 0 and version_group_id = ?)",
                one.getId());
        if (recordDO == null) {
            return group.getLastRecordId();
        }

        return recordDO.getId();
    }


    private void setDictValue(VersionGroupStatResp resp, Long v2GroupRecordId) {
        // 3.1 字典接口
        String dictSql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_item_dict.sql");
        demandDBHelper.executeRaw("SET SESSION group_concat_max_len = 1024000");
        PplVersionGroupItemDictVO dict = demandDBHelper.getRawOne(
                PplVersionGroupItemDictVO.class, dictSql, v2GroupRecordId);
        resp.setDictRegionName(dict.getRegionNameDict());
        resp.setDictWarZone(dict.getWarZoneDict());
        resp.setDictInstanceType(dict.getInstanceTypeDict());
        resp.setDictDemandScene(dict.getDemandSceneDict());
        resp.setDictCustomerShortName(dict.getCustomerShortNameDict());
        resp.setDictCustomerUin(dict.getCustomerUinDict());
    }

    private void getSummaryData(VersionGroupStatReq req, VersionGroupStatResp resp, PplVersionGroupInfoVO group,
            List<YearMonth> yearMonths, Map<String, Object> param, StringBuilder sb) {

        StringBuilder sb2 = new StringBuilder(sb);

        // 需求汇总数据
        // 排除掉被干预的原始数据
        sb.append(" AND a.is_comd != 1");
        String summarySql = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_item_stat_summary.sql");
        summarySql = summarySql.replace("${FILTER}", sb.toString());
        List<PplVersionGroupItemSummaryVO> summaryVOs = demandDBHelper.getRaw(
                PplVersionGroupItemSummaryVO.class, summarySql, param);
        // 对冲数据查询
        PplStockSupplyDO supplyDO = pplStockSupplyService.getVersionLatestDoneSupply(
                group.getVersionCode());
        List<PplStockSupplyReqDO> stockReq = pplStockSupplyService.queryStockReq(req, group.getId(), supplyDO);
        Map<String, List<PplStockSupplyReqDO>> yearMonthReqMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(stockReq)) {
            yearMonthReqMap = stockReq.stream()
                    .collect(Collectors.groupingBy(PplStockSupplyReqDO::getYearMonth));
        }

        List<SummaryDTO> summary = new ArrayList<>();  // 干预后预测数据 对冲数据 预约数据
        List<SummaryDTO> beforeSummary = new ArrayList<>(); // 干预前预测数据
        List<SummaryDTO> result = new ArrayList<>();

        // 获取干预前预测数据 -- 补齐 0数据 13周外数据
        if (req.getResourceType().equals("CORE") || req.getResourceType().equals("CVM")) {
            //  pplOrder 的 source 不等于 云运管干预
            sb2.append(" AND b.source != 'COMD_INTERVENE'");
            String summarySql2 = ORMUtils.getSql("/sql/ppl13week/ppl_version_group_item_stat_summary.sql");
            summarySql2 = summarySql2.replace("${FILTER}", sb2.toString());
            List<PplVersionGroupItemSummaryVO> summaryVOs2 = demandDBHelper.getRaw(
                    PplVersionGroupItemSummaryVO.class, summarySql2, param);
            List<List<SummaryDTO>> summaryList2 = summaryVOs2.stream()
                    .filter(v -> DateUtils.isMonthIn(v.getYear(), v.getMonth(),
                            group.getPplVersionDO().getDemandBeginYear(),
                            group.getPplVersionDO().getDemandBeginMonth(),
                            group.getPplVersionDO().getDemandEndYear(), group.getPplVersionDO().getDemandEndMonth()))
                    .map(o -> SummaryDTO.from2(o, req.getResourceType(), "summary"))
                    .collect(Collectors.toList());
            for (List<SummaryDTO> summaryDTOS : summaryList2) {
                beforeSummary.addAll(summaryDTOS);
            }

            // 补全月份和需求类型的预测前数据  仅处理预测数据
            for (YearMonth yearMonth : yearMonths) {
                for (PplDemandTypeEnum demandType : PplDemandTypeEnum.values()) {
                    if (ListUtils.filter(beforeSummary, o -> Objects.equals(yearMonth.getYear(), o.getYear())
                            && Objects.equals(yearMonth.getMonth(), o.getMonth())
                            && Objects.equals(demandType.getName(), o.getDemandType())).isEmpty()) {
                        // 预测
                        SummaryDTO s1 = new SummaryDTO();
                        s1.setYear(yearMonth.getYear());
                        s1.setMonth(yearMonth.getMonth());
                        s1.setDemandType(demandType.getName());
                        s1.setValue(0);
                        s1.setDiffValue(0);
                        s1.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                        s1.setSummaryType("干预前预测");
                        beforeSummary.add(s1);
                    }
                }
            }

            // 补全13周外 的预测前数据  仅处理预测数据
            for (PplDemandTypeEnum demandType : PplDemandTypeEnum.values()) {
                SummaryDTO s = new SummaryDTO();
                int v1valueSum = 0;
                int v2valueSum = 0;
                String unit = SummaryDTO.getUnit(req.getResourceType());
                for (PplVersionGroupItemSummaryVO summaryDTO : summaryVOs2) {
                    Integer v1value = 0;
                    Integer v2value = 0;
                    // 不在版本范围内的
                    if (!DateUtils.isMonthIn(summaryDTO.getYear(), summaryDTO.getMonth(),
                            group.getPplVersionDO().getDemandBeginYear(),
                            group.getPplVersionDO().getDemandBeginMonth(),
                            group.getPplVersionDO().getDemandEndYear(), group.getPplVersionDO().getDemandEndMonth())
                            && Objects.equals(demandType.getCode(), summaryDTO.getDemandType())) {
                        switch (req.getResourceType()) {
                            case "CVM":
                                v1value = summaryDTO.getV1Num();
                                v2value = summaryDTO.getV2Num();
                                break;
                            case "CBS":
                                v1value = summaryDTO.getV1Storage();
                                v2value = summaryDTO.getV2Storage();
                                break;
                            case "GPU":
                                v1value = summaryDTO.getV1Gpu();
                                v2value = summaryDTO.getV2Gpu();
                                break;
                            default:
                                v1value = summaryDTO.getV1Core();
                                v2value = summaryDTO.getV2Core();
                        }
                        v1valueSum = v1valueSum + v1value;
                        v2valueSum = v2valueSum + v2value;
                    }
                }
                s.setTime("13周外");
                s.setDemandType(demandType.getName());
                s.setValue(v2valueSum);
                s.setDiffValue(v2valueSum - v1valueSum);
                s.setUnit(unit);
                s.setSummaryType("干预前预测");
                beforeSummary.add(s);
            }

        }

        result.addAll(beforeSummary);
        List<List<SummaryDTO>> summaryList = summaryVOs.stream()
                .filter(v -> DateUtils.isMonthIn(v.getYear(), v.getMonth(),
                        group.getPplVersionDO().getDemandBeginYear(),
                        group.getPplVersionDO().getDemandBeginMonth(),
                        group.getPplVersionDO().getDemandEndYear(), group.getPplVersionDO().getDemandEndMonth()))
                .map(o -> SummaryDTO.from(o, req.getResourceType(), "summary"))
                .collect(Collectors.toList());
        for (List<SummaryDTO> summaryDTOS : summaryList) {
            summary.addAll(summaryDTOS);
        }
        // 补全月份和需求类型
        for (YearMonth yearMonth : yearMonths) {
            for (PplDemandTypeEnum demandType : PplDemandTypeEnum.values()) {
                if (ListUtils.filter(summary, o -> Objects.equals(yearMonth.getYear(), o.getYear())
                        && Objects.equals(yearMonth.getMonth(), o.getMonth())
                        && Objects.equals(demandType.getName(), o.getDemandType())).isEmpty()) {
                    // 预测
                    SummaryDTO s1 = new SummaryDTO();
                    s1.setYear(yearMonth.getYear());
                    s1.setMonth(yearMonth.getMonth());
                    s1.setDemandType(demandType.getName());
                    s1.setValue(0);
                    s1.setDiffValue(0);
                    s1.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                    s1.setSummaryType("干预后预测");
                    summary.add(s1);

                    //退回 不返回 已预约和实际对冲
                    if (!demandType.getCode().equals(PplDemandTypeEnum.RETURN.getCode())) {
                        // 已预约
                        SummaryDTO s2 = new SummaryDTO();
                        s2.setYear(yearMonth.getYear());
                        s2.setMonth(yearMonth.getMonth());
                        s2.setDemandType(demandType.getName());
                        s2.setValue(0);
                        s2.setDiffValue(0);
                        s2.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                        s2.setSummaryType("已预约");
                        summary.add(s2);

                        // 实际对冲
                        SummaryDTO s3 = new SummaryDTO();
                        s3.setYear(yearMonth.getYear());
                        s3.setMonth(yearMonth.getMonth());
                        s3.setDemandType(demandType.getName());
                        s3.setValue(supplyDO == null ? null : 0);
                        s3.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                        s3.setSummaryType("实际对冲");
                        summary.add(s3);
                    }
                } else {
                    if (!demandType.getName().equals(PplDemandTypeEnum.RETURN.getName())) {
                        //如果非空数据 则补充对冲数据
                        SummaryDTO s3 = new SummaryDTO();
                        s3.setYear(yearMonth.getYear());
                        s3.setMonth(yearMonth.getMonth());
                        s3.setDemandType(demandType.getName());
                        s3.setValue(supplyDO == null ? null : 0);
                        s3.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                        s3.setSummaryType("实际对冲");
                        if (yearMonthReqMap.get(yearMonth.toDateStr()) != null) {
                            Integer stockInstanceNum = 0;
                            Integer stockCoreNum = 0;
                            for (PplStockSupplyReqDO pplStockSupplyReqDO : yearMonthReqMap.get(yearMonth.toDateStr())) {
                                if (pplStockSupplyReqDO.getDemandType().equals(demandType.getCode())) {
                                    stockInstanceNum = stockInstanceNum + pplStockSupplyReqDO.getInstanceNum();
                                    stockCoreNum = stockCoreNum + pplStockSupplyReqDO.getInstanceTotalCore();
                                }
                            }
                            if (req.getResourceType().equals("CVM")) {
                                s3.setValue(stockInstanceNum);
                            } else {
                                s3.setValue(stockCoreNum);
                            }

                        }

                        summary.add(s3);
                    }
                }
            }
        }
        ListUtils.sortAscNullLast(summary, o -> o.getYear() * 10000 + o.getMonth());

        // 处理13周外的summary
        for (PplDemandTypeEnum demandType : PplDemandTypeEnum.values()) {
            SummaryDTO s = new SummaryDTO();
            int v1valueSum = 0;
            int v2valueSum = 0;
            String unit = SummaryDTO.getUnit(req.getResourceType());
            for (PplVersionGroupItemSummaryVO summaryDTO : summaryVOs) {
                Integer v1value = 0;
                Integer v2value = 0;
                // 不在版本范围内的
                if (!DateUtils.isMonthIn(summaryDTO.getYear(), summaryDTO.getMonth(),
                        group.getPplVersionDO().getDemandBeginYear(),
                        group.getPplVersionDO().getDemandBeginMonth(),
                        group.getPplVersionDO().getDemandEndYear(), group.getPplVersionDO().getDemandEndMonth())
                        && Objects.equals(demandType.getCode(), summaryDTO.getDemandType())) {
                    switch (req.getResourceType()) {
                        case "CVM":
                            v1value = summaryDTO.getV1Num();
                            v2value = summaryDTO.getV2Num();
                            break;
                        case "CBS":
                            v1value = summaryDTO.getV1Storage();
                            v2value = summaryDTO.getV2Storage();
                            break;
                        case "GPU":
                            v1value = summaryDTO.getV1Gpu();
                            v2value = summaryDTO.getV2Gpu();
                            break;
                        default:
                            v1value = summaryDTO.getV1Core();
                            v2value = summaryDTO.getV2Core();
                    }
                    v1valueSum = v1valueSum + v1value;
                    v2valueSum = v2valueSum + v2value;
                }
            }
            s.setTime("13周外");
            s.setDemandType(demandType.getName());
            s.setValue(v2valueSum);
            s.setDiffValue(v2valueSum - v1valueSum);
            s.setUnit(unit);
            s.setSummaryType("干预后预测");
            summary.add(s);

            //退回 不返回 已预约和实际对冲
            if (!demandType.getCode().equals(PplDemandTypeEnum.RETURN.getCode())) {
                // 已预约
                SummaryDTO s2 = new SummaryDTO();
                s2.setTime("13周外");
                s2.setDemandType(demandType.getName());
                s2.setValue(0);
                s2.setDiffValue(0);
                s2.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                s2.setSummaryType("已预约");
                summary.add(s2);

                // 实际对冲
                SummaryDTO s3 = new SummaryDTO();
                s3.setTime("13周外");
                s3.setDemandType(demandType.getName());
                s3.setValue(supplyDO == null ? null : 0);
                s3.setUnit(VersionGroupStatResp.getResourceTypeUnit(req.getResourceType()));
                s3.setSummaryType("实际对冲");
                summary.add(s3);
            }
        }

        result.addAll(summary);
        resp.setSummary(result);
    }


    private StringBuilder getFilterWhere(VersionGroupStatReq req, Map<String, Object> param,
            PplVersionDO pplVersionDO) {
        StringBuilder sb = new StringBuilder();
        if (ListUtils.isNotEmpty(req.getRegionNames())) {
            sb.append(" AND a.region_name in (:regionName)");
            param.put("regionName", req.getRegionNames());
        }
        if (ListUtils.isNotEmpty(req.getWarZoneNames())) {
            sb.append(" AND b.war_zone in (:warZone)");
            param.put("warZone", req.getWarZoneNames());
        }
        if (ListUtils.isNotEmpty(req.getInstanceTypes())) {
            sb.append(" AND a.instance_type in (:instanceType)");
            param.put("instanceType", req.getInstanceTypes());
        }
        if (ListUtils.isNotEmpty(req.getDemandScenes())) {
            sb.append(" AND a.demand_scene in (:demandScene)");
            param.put("demandScene", req.getDemandScenes());
        }
        if (ListUtils.isNotEmpty(req.getCustomerShortNames())) {
            sb.append(" AND b.customer_short_name in (:customerShortName)");
            param.put("customerShortName", req.getCustomerShortNames());
        }
        if (ListUtils.isNotEmpty(req.getCustomerUins())) {
            sb.append(" AND b.customer_uin in (:customerUin)");
            param.put("customerUin", req.getCustomerUins());
        }
        sb.append(" AND a.instance_num > 0");
        // 限制比较的范围就是本次版本的范围
        if (pplVersionDO != null) {
            if (pplVersionDO.getDemandBeginYear() != null && pplVersionDO.getDemandBeginMonth() != null) {
                sb.append(" AND (year(a.begin_buy_date)>:beginYear "
                        + "OR (year(a.begin_buy_date)=:beginYear and month(a.begin_buy_date)>=:beginMonth))");
                param.put("beginYear", pplVersionDO.getDemandBeginYear());
                param.put("beginMonth", pplVersionDO.getDemandBeginMonth());
            }
//            if (pplVersionDO.getDemandEndYear() != null && pplVersionDO.getDemandEndMonth() != null) {
//                sb.append(" AND (b.year<:endYear OR (b.year=:endYear and b.month<=:endMonth))");
//                param.put("endYear", pplVersionDO.getDemandEndYear());
//                param.put("endMonth", pplVersionDO.getDemandEndMonth());
//            }
        }
        return sb;
    }

    /**
     * 查询指定版本之前的已完成版本，按时间逆序排列；说明：这个列表是可能为空的
     */
    private List<PplVersionDO> getLatestDoneVersion(PplVersionDO pplVersionDO, String industryDept, String product) {
        if (pplVersionDO == null) {
            return new ArrayList<>();
        }
        // 如果版本是已完成的，取完成时间；如果没有完成，取创建时间
        Date time = null;
        if (PplVersionStatusEnum.DONE.getCode().equals(pplVersionDO.getStatus())) {
            time = pplVersionDO.getEndAuditTime();
        } else {
            time = pplVersionDO.getStartAuditTime();
        }

        return demandDBHelper.getAll(PplVersionDO.class,
                "where end_audit_time<? and version_code!=? "
                        + " and version_code in "
                        + "(select version_code from ppl_version_group where deleted=0 and status=? and industry_dept=? and product=?)"
                        // 分组已完成的
                        + " order by end_audit_time desc",
                time, pplVersionDO.getVersionCode(), PplVersionGroupStatusEnum.DONE.getCode(), industryDept, product);
    }

    @Override
    public VersionGroupItemResp queryVersionGroupItem(Long groupId) {
        PplVersionGroupInfoVO vo = getGroup(groupId);
        checkGroupViewPermission(vo);

        // 查询最大的record版本
        PplVersionGroupRecordDO latestRecord = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id=? order by record_version desc limit 1", groupId);
        if (latestRecord == null) {
            throw new WrongWebParameterException("分组ID:" + groupId + "不存在明细记录");
        }

        PplVersionGroupDO groupDO = demandDBHelper.getByKey(PplVersionGroupDO.class, groupId);
        if (groupDO == null) {
            throw new WrongWebParameterException("分组ID:" + groupId + "不存在");
        }

        // 中长尾0的数量太大了，把0过滤掉
        boolean isLongTail = Strings.equals(groupDO.getIndustryDept(), "中长尾");
        String postSql = "where version_group_record_id=? ";
        if (isLongTail){
            postSql += " and ( instance_num>0 or data_disk_type != '' )";
        }
        List<PplVersionGroupItemWithSourceItemVO> items = demandDBHelper.getAll(
                PplVersionGroupItemWithSourceItemVO.class, postSql, latestRecord.getId());

        // 查回当前版本最后一次成功的对冲下，这些pplId的对冲结果
        Map<String, List<PplStockSupplyReqWithRspVO>> supplyMap =
                getCvmPplId2supplyMap(groupDO, items);

        // 2024-04-25 查询聚合后的补充对冲结果
        Map<String, List<PplGroupStockSupplyRspDO>> cvmGroupSupplyMap =
                getCvmGroupSupplyMap(groupDO, items);

        //  2023-04-18 gou 对冲需求, gpu 也展示上数据
        //  gpu 按照分组来查找数据
        Map<String, List<PplStockSupplyGpuDetailDO>> gpuSupplyMap =
                getGpuPplId2supplyMap(groupDO, items);

        VersionGroupItemResp resp = new VersionGroupItemResp();
        resp.setPplItems(ListUtils.transform(items, o ->
                VersionGroupItemResp.GroupItemDTO.from(o, supplyMap, cvmGroupSupplyMap, gpuSupplyMap,
                        groupDO.getStatus())));

        if (Ppl13weekProductTypeEnum.BM.getName().equals(vo.getProduct())) {
            // 裸金属对冲结果
            fillBmSupply(resp, groupId);
        }

        for (GroupItemDTO pplItem : resp.getPplItems()) {
            if (Strings.isNotBlank(pplItem.getDataDiskType())) {
                continue;
            }
            String instanceType = pplItem.getInstanceType();
            List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(pplItem.getZoneName());
            boolean contains = mainInstanceTypes.contains(instanceType);
            pplItem.setIsRecommendedInstanceType(contains);
        }

        // 24-01-22同个月、同客户（客户简称，如果没有采用uin）、同机型、同地域下新增/弹性合起来超过阈值，或者退回超过阈值，
        // 那么这个月、这个客户、这个机型、这个地域下的所有新增、退回的PN/PE单就归为毛刺

        // 这里需要干预后的数据， 要取  comd = 0
        // 2024-04-29 这里云管干预的项目类型取自数据库中
        setProjectColumn(resp.getPplItems(), latestRecord);

        // 预约量信息 23-11-22 --- 不再从缺口数据里取预约信息
//        fillApplyInfo(resp.getPplItems(), vo);
        return resp;
    }

    private void setProjectColumn(List<GroupItemDTO> pplItems, PplVersionGroupRecordDO latestRecord) {

        // 31个大客户的不管什么机型和阈值，都直接是重点项目
        Set<String> headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();
        pplItems = ListUtils.filter(pplItems, (o) -> {
            // 其它分组的数据会影响到，大客户有些填写了数据盘
            if (Strings.isNotBlank(o.getDataDiskType()) && Strings.equals(o.getNote(),"CBS模型预测下发")) {
                return false;
            }
            boolean isHeadCustomer = headCustomerShortName.contains(o.getCustomerShortName());
            if (isHeadCustomer) {
                o.setIsSpikeAfterComd(1);
                o.setIsSpikeBeforeComd(1);
                o.setIsSpikeAfterComdReason("属于大客户名单");
                o.setIsSpikeBeforeComdReason("属于大客户名单");
            }
            return !isHeadCustomer;
        });

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        pplItems = pplItems.stream().filter((o) -> {
                    boolean isBlackInstanceType = allBlackInstanceType.contains(o.getInstanceType());
                    if (isBlackInstanceType) {
                        o.setIsSpikeAfterComd(2);
                        o.setIsSpikeBeforeComd(2);
                        o.setIsSpikeAfterComdReason("不参与PPL的机型（老代次或黑名单）");
                        o.setIsSpikeBeforeComdReason("不参与PPL的机型（老代次或黑名单）");
                    }
                    return !isBlackInstanceType;
                })
                .collect(Collectors.toList());

        // 这里是干预前的数据
        List<GroupItemDTO> beforeComdPplItems = ListUtils.filter(pplItems, (o) ->
                Objects.equals(o.getMeddleType(), "未干预"));

        List<GroupItemDTO> afterComdPplItems = ListUtils.filter(pplItems, (o) ->
                Objects.equals(o.getIsComd(), false));

        List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold > 0");

        setThresholds(beforeComdPplItems, thresholds, true);
        setThresholds(afterComdPplItems, thresholds, false);

        /*
         * 状态在干预后，用数据库中的节点
         * 覆盖前面的打标，这样用户指定的就是最高优先级了
         */
        if (PplVersionGroupStatusEnum.isAfterComdNode(latestRecord.getStatus())) {
            pplItems.forEach((o) -> {
                if (o.getIsSpike() != -1) {
                    // 如果项目类型发生了变化，那就是云运管干预时指定
                    if (!Objects.equals(o.getIsSpikeAfterComd(), o.getIsSpike())) {
                        if (o.getIsSpikeAfterComdReason() != null && o.getIsSpikeAfterComdReason()
                                .startsWith("超过阈值")) {
                            o.setIsSpikeAfterComdReason("云运管干预时指定或阈值已发生变化");
                        } else {
                            o.setIsSpikeAfterComdReason("云运管干预时指定");
                        }
                    }
                    o.setIsSpikeAfterComd(o.getIsSpike());
                }
            });
        }
    }


    // queryVersionGroupItem 明细列表的打标
    private void setThresholds(List<GroupItemDTO> pplItems, List<PplForecastConfigSpikeThresholdDO> thresholds,
            Boolean isBefore) {

        // 模型预测的都认为是非毛刺
        ListUtils.filter(pplItems, o -> isForecast(o.getCustomerShortName()))
                .forEach(o -> {
                    o.setSpike(isBefore, 0);
                    if (isBefore) {
                        o.setIsSpikeBeforeComdReason("属于模型预测的输出");
                    } else {
                        o.setIsSpikeAfterComdReason("属于模型预测的输出");
                    }
                });

        // 剩余的再按照毛刺标准来算
        pplItems = ListUtils.filter(pplItems, o -> !isForecast(o.getCustomerShortName()));

        ForSpikeParams<GroupItemDTO> forSpikeParams = new ForSpikeParams<>();
        forSpikeParams.setMatchConfigFunc((items, config) -> {
            BigDecimal sum = NumberUtils.sum(items, GroupItemDTO::getTotalCoreNum);
            if (sum.compareTo(BigDecimal.valueOf(config.getThreshold())) >= 0) {
                items.forEach((o) -> {
                    o.setSpike(isBefore, 1);
                    if (isBefore) {
                        o.setIsSpikeBeforeComdReason("超过阈值(年月、客户简称、机型、地域粒度累计)");
                    } else {
                        o.setIsSpikeAfterComdReason("超过阈值(年月、客户简称、机型、地域粒度累计)");
                    }
                });
                return true;
            }
            return false;
        });
        forSpikeParams.setData(pplItems);
        forSpikeParams.setTotalConfig(thresholds);

        final Map<String, String> region2CustomhouseTitleMap = getRegion2CustomhouseTitleMap();

        forSpikeParams.setRegionNameFunc(GroupItemDTO::getRegionName);
        forSpikeParams.setCustomhouseTitleFunc((o) -> region2CustomhouseTitleMap.getOrDefault(o.getRegionName(), ""));
        forSpikeParams.setInstanceTypeFunc(
                o -> forSpikeParams.getInstanceTypeToGroup().getOrDefault(o.getInstanceType(), o.getInstanceType()));

        forSpikeParams.setOtherFunc((o) -> Strings.join("@",
                o.getYearMonth(),
                Strings.isBlank(o.getCustomerShortName()) ? o.getCustomerUin() : o.getCustomerShortName(),
                PplDemandTypeEnum.RETURN.getCode().equals(o.getDemandType()) ?
                        o.getDemandType() : PplDemandTypeEnum.NEW.getCode()
        ));

        Ppl13WeekInputServiceImpl.applyConfig(forSpikeParams);
    }

    private void fillBmSupply(VersionGroupItemResp resp, Long groupId) {
        List<String> pplIdList = ListUtils.transform(resp.getPplItems(), GroupItemDTO::getPplId);
        List<PplStockSupplyBMDetailDO> bmList = pplBMStockSupplyService.queryStockSupply(groupId, pplIdList);
        if (ListUtils.isEmpty(bmList)) {
            return;
        }
        Map<String, List<PplStockSupplyBMDetailDO>> pplIdSupplyMap = ListUtils.toMapList(bmList,
                PplStockSupplyBMDetailDO::getPplId, item -> item);
        for (GroupItemDTO pplItem : resp.getPplItems()) {
            List<PplStockSupplyBMDetailDO> supplyList = pplIdSupplyMap.get(pplItem.getPplId());
            List<SatisfySupplyDTO> bmSupply = VersionGroupItemResp.convertFromBmSupply(supplyList);
            pplItem.setBmSupply(bmSupply);
        }
    }

    private Map<String, List<PplGroupStockSupplyRspDO>> getCvmGroupSupplyMap(PplVersionGroupDO groupDO,
            List<? extends PplVersionGroupItemVO> items) {
        PplStockSupplyDO supplyDO = pplStockSupplyService.getVersionLatestDoneSupply(groupDO.getVersionCode());
        Map<String, List<PplGroupStockSupplyRspDO>> supplyMap = new HashMap<>();
        if (supplyDO != null) {
            List<PplGroupStockSupplyRspDO> rsps = demandDBHelper.getAll(PplGroupStockSupplyRspDO.class,
                    "where supply_id= ? AND match_type IS NOT NULL and child_ppl_id in (?)"
                    , supplyDO.getId(), ListUtils.transform(items, PplVersionGroupRecordItemDO::getPplId));
            supplyMap = ListUtils.toMapList(rsps, PplGroupStockSupplyRspDO::getChildPplId, o -> o);
        }
        return supplyMap;
    }

    private Map<String, List<PplStockSupplyReqWithRspVO>> getCvmPplId2supplyMap(PplVersionGroupDO groupDO,
            List<? extends PplVersionGroupItemVO> items) {
        PplStockSupplyDO supplyDO = pplStockSupplyService.getVersionLatestDoneSupply(groupDO.getVersionCode());
        Map<String, List<PplStockSupplyReqWithRspVO>> supplyMap = new HashMap<>();
        if (supplyDO != null) {
            List<PplStockSupplyReqWithRspVO> reqs = demandDBHelper.getAll(PplStockSupplyReqWithRspVO.class,
                    "where ppl_id in (?) and supply_id=?",
                    ListUtils.transform(items, PplVersionGroupRecordItemDO::getPplId), supplyDO.getId());
            supplyMap = ListUtils.toMapList(reqs, PplStockSupplyReqDO::getPplId, o -> o);
        }
        return supplyMap;
    }

    private Map<String, List<PplStockSupplyGpuDetailDO>> getGpuPplId2supplyMap(
            PplVersionGroupDO groupDO, List<? extends PplVersionGroupItemVO> items) {
        Map<String, List<PplStockSupplyGpuDetailDO>> gpuSupplyMap = new HashMap<>();
        if (Strings.equals(groupDO.getProduct(), Ppl13weekProductTypeEnum.GPU.getName())) {
            WhereContent gpuWhere = new WhereContent();
            gpuWhere.andEqual(PplStockSupplyGpuDO::getVersionCode, groupDO.getVersionCode());
            PplStockSupplyGpuDO gpuSupply = ORMUtils.db(demandDBHelper).getOne(PplStockSupplyGpuDO.class, gpuWhere);
            if (gpuSupply != null) {
                List<PplStockSupplyGpuDetailDO> reqs = demandDBHelper.getAll(PplStockSupplyGpuDetailDO.class,
                        "where ppl_id in (?) and supply_gpu_id=?",
                        ListUtils.transform(items, PplVersionGroupRecordItemDO::getPplId), gpuSupply.getId());
                gpuSupplyMap = ListUtils.toMapList(reqs, o -> o.getPplId(), o -> o);
            }
        }
        return gpuSupplyMap;
    }

    @Override
    public VersionGroupItemResp queryVersionGroupItemByVersionCode(String versionCode) {
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupInfoVO::getVersionCode, versionCode);
        List<PplVersionGroupInfoVO> groupList = demandDBHelper.getAll(PplVersionGroupInfoVO.class,
                whereContent.getSql(),
                whereContent.getParams());
        if (CollectionUtils.isEmpty(groupList)) {
            throw new BizException("版本尚未启动，请先启动版本");
        }
        List<GroupItemDTO> pplItems = new ArrayList<>();

        // 查回当前版本最后一次成功的对冲下，这些pplId的对冲结果
        PplStockSupplyDO supplyDO = pplStockSupplyService.getVersionLatestDoneSupply(versionCode);
        Map<String, List<PplStockSupplyReqWithRspVO>> supplyMap = new HashMap<>();

        if (supplyDO != null) {
            List<PplStockSupplyReqWithRspVO> reqs = demandDBHelper.getAll(PplStockSupplyReqWithRspVO.class,
                    "where supply_id=?", supplyDO.getId());
            supplyMap = ListUtils.toMapList(reqs, o -> o.getPplId(), o -> o);
        }

        String recordSql = "select *\n"
                + "from ppl_version_group_record_item\n"
                + "where version_group_record_id in (\n"
                + "    select max(id)\n"
                + "    from ppl_version_group_record\n"
                + "    where version_group_id in (\n"
                + "        #   选出一个版本所有的组 group\n"
                + "        select id\n"
                + "        from ppl_version_group\n"
                + "        where version_code = ? \n"
                + "          and deleted = 0 and product in (?) )\n"
                + "      and deleted = 0\n"
                + "    group by version_group_id\n"
                + "    ) and is_comd = 0 and instance_num > 0 and deleted = 0 ;";
        List<PplVersionGroupItemVO> allRecords =
                demandDBHelper.getRaw(PplVersionGroupItemVO.class,
                        recordSql,versionCode,Ppl13weekProductTypeEnum.getCvmCategoryProductNameList());
        Map<Long, List<PplVersionGroupItemVO>> groupIdToRecordItem = allRecords.stream()
                .filter(v -> v.getInstanceNum() != 0)
                .collect(Collectors.groupingBy(PplVersionGroupItemVO::getVersionGroupId));

        for (PplVersionGroupInfoVO pplVersionGroupInfoVO : groupList) {
            Long groupId = pplVersionGroupInfoVO.getId();
            List<PplVersionGroupItemVO> items = groupIdToRecordItem.get(groupId);
            if (CollectionUtils.isEmpty(items)) {
                continue;
            }
            Map<String, List<PplStockSupplyReqWithRspVO>> finalSupplyMap = supplyMap;
            List<GroupItemDTO> itemList = ListUtils.transform(items,
                    o -> GroupItemDTO.from(o, finalSupplyMap, new HashMap<>(), new HashMap<>(),
                            pplVersionGroupInfoVO.getStatus()));
            pplItems.addAll(itemList);
        }
        for (GroupItemDTO pplItem : pplItems) {
            String instanceType = pplItem.getInstanceType();
            List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(pplItem.getZoneName());
            boolean contains = mainInstanceTypes.contains(instanceType);
            pplItem.setIsRecommendedInstanceType(contains);
        }
        VersionGroupItemResp resp = new VersionGroupItemResp();
        resp.setPplItems(pplItems);
        return resp;
    }

    @Override
    public Map<String, Integer> queryVersionGroupView(QueryVersionGroupView req) {
        Long groupId = req.getGroupId();
        // 查询最大的record版本
        PplVersionGroupRecordDO latestRecord = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id=? order by record_version desc limit 1", groupId);
        if (latestRecord == null) {
            throw new WrongWebParameterException("分组ID:" + groupId + "不存在审批记录");
        }

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual("version_group_record_id", latestRecord.getId());
        whereContent.andEqualIfValueNotEmpty(PplVersionGroupItemVO::getDemandType, req.getDemandType());
        whereContent.addAnd("instance_num!=0");

        // 只查干预后的数据
        whereContent.andEqual(PplVersionGroupItemVO::getIsComd, Boolean.FALSE);

        List<PplVersionGroupItemVO> items = demandDBHelper.getAll(PplVersionGroupItemVO.class, whereContent.getSql(),
                whereContent.getParams());
//        List<PplVersionGroupItemVO> items = demandDBHelper.getAll(
//                PplVersionGroupItemVO.class, "where version_group_record_id=?",
//                latestRecord.getId());

        LocalDate start = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(req.getStartDate());
        LocalDate end = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(req.getEndDate());
        items = ListUtils.filter(items, (o) -> {
            if (start != null) {
                if (start.getYear() > o.getYear()) {
                    return false;
                }
                if (start.getYear() < o.getYear()) {
                    return true;
                }
                return (start.getYear() == o.getYear() && start.getMonth().getValue() <= o.getMonth());
            }
            return true;
        });
        items = ListUtils.filter(items, (o) -> {
            if (end != null) {
                if (end.getYear() > o.getYear()) {
                    return true;
                }
                if (end.getYear() < o.getYear()) {
                    return false;
                }
                return (end.getYear() == o.getYear() && end.getMonth().getValue() >= o.getMonth());
            }
            return true;
        });

        Map<String, List<PplVersionGroupItemVO>> groupMap;
        switch (req.getType()) {
            case "WAR_ZONE":
                groupMap = ListUtils.groupBy(items, PplVersionGroupItemVO::getWarZone);
                break;
            case "CUSTOMER_NAME":
                groupMap = ListUtils.groupBy(items, PplVersionGroupItemVO::getCustomerShortName);
                break;
            case "REGION":
                groupMap = ListUtils.groupBy(items, PplVersionGroupItemVO::getRegionName);
                break;
            case "INSTANCE_TYPE":
                groupMap = ListUtils.groupBy(items, PplVersionGroupItemVO::getInstanceType);
                break;
            default:
                throw new BizException("type is error");
        }

        HashMap<String, Integer> ret = new HashMap<>();
        for (String key : groupMap.keySet()) {
            List<PplVersionGroupItemVO> pplVersionGroupItemVOS = groupMap.get(key);
            BigDecimal sumTotalCore = ListUtils.sum(pplVersionGroupItemVOS, (o) -> o.getTotalCore());
            BigDecimal sumTotalGpuNum = ListUtils.sum(pplVersionGroupItemVOS, (o) -> o.getTotalGpuNum());

            if (Strings.isNotBlank(req.getProduct()) && Strings.equals(req.getProduct(),
                    Ppl13weekProductTypeEnum.GPU.getName())) {
                ret.put(key, sumTotalGpuNum.intValue());
            } else {
                ret.put(key, sumTotalCore.intValue());
            }
        }
        return ret;
    }

    private void checkPermission(PplVersionGroupInfoVO group, String nodeStatus, boolean isSystem) {

        //如果当前为系统级别调用,无须鉴权
        if (isSystem) {
            return;
        }

        if (StringTools.isBlank(group.getCurrentProcessor())) {
            throw new BizException("当前处理人为空，请联系管理为当前环节:"
                    + PplVersionGroupStatusEnum.getNameByCode(group.getStatus())
                    + ",行业部门:" + group.getIndustryDept() + ",产品" + group.getProduct() + "添加审批人员");
        }

        if (!group.getCurrentProcessor().contains(LoginUtils.getUserName())) {
            throw new BizException("您不在审批人员列表中，无法操作，如果您是新申请的角色，需要在新的审批单中才生效");
        }

        if (nodeStatus != null && Strings.equals(nodeStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode())) {
            //nodeStatus不等于null 且 等于库存对冲这个节点时
            throw new BizException("当前流程处于产品库存对冲中，不允许您的操作，请刷新重试");
        }


    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    public void saveVersionGroupItem(SaveVersionGroupItemReq req) {
        if (ListUtils.isEmpty(req.getEditItems())
                && (Strings.isBlank(req.getIndustryDept()) && ListUtils.isEmpty(req.getInsertData()))) {
            throw new WrongWebParameterException("编辑和插入内容都为空");
        }

        PplVersionGroupInfoVO group = getGroup(req.getGroupId());
        if (group.getPplVersionDO() == null) {
            throw new BizException(
                    "版本分组:" + group.getId() + "对应的版本" + group.getVersionCode() + "已经被删除，不允许编辑");
        }
        if (!Objects.equals(group.getPplVersionDO().getStatus(), PplVersionStatusEnum.PROCESS.getCode())) {
            throw new BizException(
                    "版本分组:" + group.getId() + "对应的版本" + group.getVersionCode() + "不是进行中状态，不允许编辑");
        }

        checkPermission(group, null, false);

        if (!Lang.list(
                PplVersionGroupStatusEnum.REJECT.getCode(),
                PplVersionGroupStatusEnum.CREATE.getCode(),
                PplVersionGroupStatusEnum.PRE_SUBMIT.getCode(),
                PplVersionGroupStatusEnum.IN_SUBMIT.getCode()).contains(group.getStatus())) {
            throw BizException.makeThrow("版本分组:%d, 当前状态[%s] 不可以操作", group.getId(),
                    PplVersionGroupStatusEnum.getNameByCode(group.getStatus()));
        }

        PplVersionGroupRecordDO record = group.getLastRecord();
        if (record == null) {
            throw new BizException("不存在审批记录，属于数据异常，请联系开发");
        }
        if (Lang.list(PplVersionGroupStatusEnum.CREATE.getCode(),
                PplVersionGroupStatusEnum.PRE_SUBMIT.getCode()).contains(group.getStatus())) {
            record = import2NextRecord(group, "录入自动流转状态", true, null);
        }

        /*
         * 处理插入
         */
        if (Strings.isNotBlank(req.getIndustryDept()) && ListUtils.isNotEmpty(req.getInsertData())) {
            ImportVersionGroupReq importVersionGroupReq = new ImportVersionGroupReq();
            importVersionGroupReq.setGroupId(req.getGroupId());
            importVersionGroupReq.setData(req.getInsertData());
            importVersionGroupReq.setIndustryDept(req.getIndustryDept());
            importVersionGroupReq.setProduct(group.getProduct());
            this.importPplVersionItem(importVersionGroupReq);
        }

        List<PplVersionGroupItemVO> items = demandDBHelper.getAll(PplVersionGroupItemVO.class,
                "where version_group_record_id=?", record.getId());
        Map<String, PplVersionGroupItemVO> pplIdMap = ListUtils.toMap(items, o -> o.getPplId(), o -> o);

        // 处理编辑
        ListUtils.forEach(req.getEditItems(), o -> {

            PplVersionGroupItemVO itemVO = pplIdMap.get(o.getPplId());
            if (itemVO != null
                    && itemVO.getPplItemDO() != null
                    && Strings.equals(itemVO.getPplItemDO().getStatus(), PplItemStatusEnum.APPLIED.getCode())) {
                // 处于已经预约的数据，这里直接跳过
                return;
            }

            OperateTypeEnum operateType = OperateTypeEnum.getByCode(o.getEditType());
            if (operateType == null) {
                throw new WrongWebParameterException("未知的编辑类型:" + o.getEditType());
            }
            if (operateType == OperateTypeEnum.INSERT) {
                throw new WrongWebParameterException("版本审批过程中无法新增pplId需求，只能编辑或删除");
            }
            if (StringTools.isBlank(o.getPplId())) {
                throw new WrongWebParameterException("编辑版本item明细缺少pplId，请检查");
            }
            if (operateType == OperateTypeEnum.UPDATE) {
                PplVersionGroupRecordItemDO item = pplIdMap.get(o.getPplId());
                if (item == null) {
                    throw new WrongWebParameterException("要编辑的pplId已经不存在，可能已经被修改了，请刷新页面重试");
                }

                if (Strings.equals(o.getDemandType(), PplDemandTypeEnum.NEW.getCode())
                        || Strings.equals(o.getDemandType(), PplDemandTypeEnum.RETURN.getCode())) {

                    if (o.getBeginBuyDate() == null) {
                        throw BizException.makeThrow("开始购买日期为空");
                    }
                    if (o.getEndBuyDate() == null) {
                        throw BizException.makeThrow("结束购买日期为空");
                    }

                    Period between = Period.between(o.getBeginBuyDate().withDayOfMonth(1),
                            o.getEndBuyDate().withDayOfMonth(1));
                    if (Math.abs(between.getMonths()) > 1) {
                        throw BizException.makeThrow(
                                "新增需求和退回需求： 结束购买日期年月(%s) - 开始购买日期年月(%s) > 1",
                                o.getBeginBuyDate(), o.getEndBuyDate());
                    }

                }

                // 新增限制城市黑名单
                String regionName = o.getRegionName();
                String zoneName = o.getZoneName();
                pplDictService.checkIsInBlackList(regionName, zoneName);

                o.fill(item);
//                // 填充核心数
//                if (item.getInstanceNum() != null) {
//                    item.setTotalCore(item.getInstanceNum() *
//                            dictService.getInstanceCoreNum(item.getInstanceModel()));
//                }
                // 填充新的核心数
                updateTotalAmount(item);
                demandDBHelper.update(item);
            }
            if (operateType == OperateTypeEnum.DELETED) {
                PplVersionGroupRecordItemDO item = pplIdMap.get(o.getPplId());
                // 已经被删除了就忽略
                if (item != null) {
                    // 删除等于设置数量为0
                    item.setInstanceNum(0);
                    item.setTotalCore(0);
                    item.setTotalDisk(0);
                    item.setTotalGpuNum(BigDecimal.valueOf(0));
                    demandDBHelper.update(item);
                }
            }
        });
    }

    @Override
    @Transactional("demandTransactionManager")
    public void initVersionGroupItem(InitNewVersionGroupReq req) {
        //是否已存在校验
        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupDO::getVersionCode, req.getVersionCode());
        whereContent.andEqual(PplVersionGroupDO::getProduct, req.getProduct());
        whereContent.andEqual(PplVersionGroupDO::getIndustryDept, req.getIndustryDept());
        List<PplVersionGroupDO> all = demandDBHelper.getAll(PplVersionGroupDO.class, whereContent.getSql(),
                whereContent.getParams());
        if (!CollectionUtils.isEmpty(all)) {
            throw new BizException("当前版本已存在 " + req.getIndustryDept() + " 的" + req.getProduct() + "审批");
        }

        PplVersionGroupDO groupDO = new PplVersionGroupDO();
        groupDO.setVersionCode(req.getVersionCode());
        groupDO.setIndustryDept(req.getIndustryDept());
        groupDO.setProduct(req.getProduct());
        groupDO.setStatus(PplVersionGroupStatusEnum.CREATE.getCode());
        demandDBHelper.insert(groupDO);

        List<PplItemWithOrderVO> list = new ArrayList<>();

        PplVersionDO pplVersion = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?",
                req.getVersionCode());
        WhereSQL whereSQL = new WhereSQL();
        // item里的状态只有有效和已执行，所以都要
        // 只要满足条件的ppl单   23-2-6 继承ppl单 只要求ppl大于版本启动时间, 可不小于版本截止时间
        // 2023年4月28日 09:53:35 继承范围由【未预约】改成ppl单来源为IMPORT、FORECAST、APPLY_AUTO_FILL、APPLY_AUTO_FILL_LONGTAIL，4种类型
        // 2023年6月28日 17:23:06 继承范围去掉FORECAST、APPLY_AUTO_FILL、APPLY_AUTO_FILL_LONGTAIL
        whereSQL.and("product = ? and begin_buy_date >= ? and ppl_order in (select ppl_order from ppl_order"
                        + " where deleted=0 and source in (?) and industry_dept = ?)",
                req.getProduct(),
                DateUtils.getFirstDayOfMonth(pplVersion.getDemandBeginYear() + "-" + pplVersion.getDemandBeginMonth()),
                ListUtils.newList(PplOrderSourceTypeEnum.IMPORT.getCode()
                ), req.getIndustryDept());

        list = demandDBHelper.getAll(PplItemWithOrderVO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        // 创建两种类型的数据
        GroupRecordAndItem groupRecordAndItem = pplVersionService.savePplRecordAndItemForCreate(groupDO, list);
        //新增待办 23-6-27 13周录入不发送待办
//        pplVersionService.createTodoTask(groupRecordAndItem.getGroupDO(), groupRecordAndItem.getRecord());
    }


    @Override
    public void toStockSupplyStatus(List<PplVersionGroupRecordWithGroupVO> notInStockSupply) {

        String nextGroupStatus = PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode();
        List<String> needFinishTodoStatus = Lang.list(PplVersionGroupStatusEnum.PAAS_SUBMIT.getCode(),
                PplVersionGroupStatusEnum.COMD_INTERVENE.getCode(),
                PplVersionGroupStatusEnum.COMD_APPROVE.getCode());

        for (PplVersionGroupRecordWithGroupVO recordWithGroup : notInStockSupply) {
            PplVersionGroupDO group = recordWithGroup.getGroupDO();
            String currentStatus = group.getStatus();
            if (group == null) {
                throw BizException.makeThrow("record的版本未找到: %d，属于数据异常，请联系开发解决",
                        recordWithGroup.getId());
            }

            if (needFinishTodoStatus.contains(recordWithGroup.getStatus())) {
                String taskId = StringTools.join("-", group.getVersionCode(), group.getIndustryDept(),
                        group.getProduct(), group.getStatus(), recordWithGroup.getId());
                finishTodo(LoginUtils.getUserName(), taskId, "通过");
            }
            String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    nextGroupStatus, group.getIndustryDept(), group.getProduct());

            // update old
            group.setStatus(nextGroupStatus);
            group.setCurrentProcessor(processor);
            demandDBHelper.update(group);

            String userNameWithSystem = LoginUtils.getUserNameWithSystem();
            recordWithGroup.setOperateUser(userNameWithSystem);
            recordWithGroup.setApproveTime(new Date());
            recordWithGroup.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            recordWithGroup.setApproveNote(userNameWithSystem + " 手工下发库存对冲，状态自动流转。");
            demandDBHelper.update(recordWithGroup);

            // deal with new
            {
                Integer newRecordVersion = pplCommonService.getVersionGroupRecordVersion();

                // 插入PAAS节点的log  处于系统生成中状态的PAAS级产品版本组
                if (!StringUtil.equals(group.getProduct(), "CVM&CBS") && StringUtil.equals(currentStatus,
                        PplVersionGroupStatusEnum.CREATE.getCode())) {
                    PplVersionGroupRecordDO paasRecord = new PplVersionGroupRecordDO();
                    paasRecord.setVersionGroupId(group.getId());
                    paasRecord.setRecordVersion(newRecordVersion);
                    paasRecord.setStatus(PplVersionGroupStatusEnum.PAAS_SUBMIT.getCode());
                    paasRecord.setOperateUser(userNameWithSystem);
                    paasRecord.setApproveTime(new Date());
                    paasRecord.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
                    paasRecord.setApproveNote(userNameWithSystem + "手工下发库存对冲,自动跳过该节点");
                    demandDBHelper.insert(paasRecord);
                }
                if (StringUtil.equals(currentStatus, PplVersionGroupStatusEnum.COMD_APPROVE.getCode()) ||
                        StringUtil.equals(currentStatus, PplVersionGroupStatusEnum.DONE.getCode())) {
                    //如果处于物理机转换 或已完成状态节点时 将之前的log逻辑删除
                    List<String> statusList = Lang.list(PplVersionGroupStatusEnum.COMD_APPROVE.getCode(),
                            PplVersionGroupStatusEnum.DONE.getCode(), PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode());
                    WhereContent whereContent = new WhereContent();
                    whereContent.andEqual(PplVersionGroupRecordDO::getVersionGroupId,
                            recordWithGroup.getVersionGroupId());
                    whereContent.andIn(PplVersionGroupRecordDO::getStatus, statusList);
                    List<PplVersionGroupRecordDO> deletedList = demandDBHelper.getAll(PplVersionGroupRecordDO.class,
                            whereContent.getSql(), whereContent.getParams());
                    for (PplVersionGroupRecordDO pplVersionGroupRecordDO : deletedList) {
                        pplVersionGroupRecordDO.setBizDeleted(Boolean.TRUE);
                    }
                    demandDBHelper.update(deletedList);
                }

                PplVersionGroupRecordDO recordNew = new PplVersionGroupRecordDO();
                recordNew.setVersionGroupId(group.getId());
                recordNew.setRecordVersion(newRecordVersion + 1);
                recordNew.setStatus(nextGroupStatus);
                demandDBHelper.insert(recordNew);

                // 复制上一个record的item
                List<PplVersionGroupRecordItemDO> items = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                        "where version_group_record_id=?", recordWithGroup.getId());
                ListUtils.forEach(items, o -> {
                    o.setId(null); // 清除id，相当于复制
                    o.setCreateTime(null);
                    o.setRecordVersion(newRecordVersion);
                    o.setVersionGroupRecordId(recordNew.getId());
                });
                demandDBHelper.insertBatchWithoutReturnId(items);
            }

            // 自动对冲不需要代办了
        }


    }


    /**
     * 录入流程触发流转, 也给行业内流程调用
     *
     * @param group
     * @param approveNote
     * @param isImportData true-会卡在录入中， false-直接进入对冲中
     * @return
     */
    @Override
    public PplVersionGroupRecordDO import2NextRecord(PplVersionGroupInfoVO group, String approveNote,
            Boolean isImportData, String nextGroupStatus) {
        if (nextGroupStatus == null) {
            nextGroupStatus = PplVersionGroupStatusEnum.getNextStatus(group.getStatus(), group.getProduct(),
                    isImportData);
        }
        // 对接待办系统
//        String taskId = StringTools.join("-", group.getVersionCode(), group.getIndustryDept(),
//                group.getProduct(), group.getStatus(), group.getLastRecordId());
//        finishTodo(LoginUtils.getUserName(), taskId, "通过");

        String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                nextGroupStatus, group.getIndustryDept(), group.getProduct());
        updateGroupRecordStatus(group, PplVersionGroupApproveResultEnum.PASS, approveNote,
                nextGroupStatus, processor, true);
        /*
            重要这里更新了 record  1/17 录入中不触发todo
             */
//        pplVersionService.createTodoTask(group, recordNew);
        return copyRecordToNextRecord(group, nextGroupStatus);
    }

    private void updateTotalAmount(PplVersionGroupRecordItemDO pplItemDO) {
//        Integer instanceCoreNum = dictService.getInstanceCoreNum(pplItemDO.getInstanceModel());
        Tuple2<Integer, Integer> cpuRamNum = P2PInstanceModelParse.parseInstanceModel(
                pplItemDO.getInstanceModel());
        Integer instanceNum = pplItemDO.getInstanceNum();
        instanceNum = instanceNum == null ? 0 : instanceNum;
        pplItemDO.setTotalCore(cpuRamNum._1 * instanceNum);

        int totalDiskNum = 0;
        if (pplItemDO.getSystemDiskNum() == null) {
            pplItemDO.setSystemDiskNum(1);
        }
        if (pplItemDO.getSystemDiskStorage() != null) {
            totalDiskNum += pplItemDO.getSystemDiskStorage() * pplItemDO.getSystemDiskNum();
        }
        if (pplItemDO.getDataDiskStorage() != null && pplItemDO.getDataDiskNum() != null) {
            totalDiskNum += pplItemDO.getDataDiskStorage() * pplItemDO.getDataDiskNum();
        }
        pplItemDO.setTotalDisk(totalDiskNum * instanceNum);
    }


    @Override
    @Transactional("demandTransactionManager")
    public void approveVersionGroupWithOutLock(ApproveVersionGroupReq req) {
        approveVersionGroup(req);
    }

    // 上升到 version， 等待 5 秒钟
    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    public void approveVersionGroupByAdmin(String changeStatus,
            PplVersionGroupDO pplVersionGroupDO) {

        PplVersionGroupInfoVO group = getGroup(pplVersionGroupDO.getId());
        String nodeStatus = group.getStatus();

        if (!StringUtil.equals(pplVersionGroupDO.getStatus(), nodeStatus)) {
            throw new BizException("当前节点已被其他人处理过了，请刷新重试");
        }

        if (group.getPplVersionDO() == null) {
            throw new BizException(
                    "版本分组:" + group.getId() + "对应的版本" + group.getVersionCode() + "已经被删除，不允许审批");
        }

        /*
         * stock 2 done
         */
        if (PplVersionGroupStatusEnum.isEqual(nodeStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY) &&
                PplVersionGroupStatusEnum.isEqual(changeStatus, PplVersionGroupStatusEnum.DONE)) {

            // 更新当前group和record状态，
            updateGroupRecordStatus(group, PplVersionGroupApproveResultEnum.PASS, "大数据补录，不参与对冲，自动完结",
                    PplVersionGroupStatusEnum.DONE.getCode(), "system", Boolean.TRUE);
            //新增预测物理机record
            PplVersionGroupRecordDO comdRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.COMD_APPROVE.getCode());
            comdRecord.setApproveNote("大数据补录，不参与对冲，自动完结");
            comdRecord.setApproveTime(new Date());
            comdRecord.setOperateUser("system");
            comdRecord.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            demandDBHelper.update(comdRecord);

            //新增已完成record
            PplVersionGroupRecordDO doneRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.DONE.getCode());
            doneRecord.setApproveNote("大数据补录，不参与对冲，自动完结");
            doneRecord.setApproveTime(new Date());
            doneRecord.setOperateUser("system");
            doneRecord.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            demandDBHelper.update(doneRecord);

            // 23-2-10  版本关闭时才将record-item  -》  item中
//            // 完成并写入到 item中
//            applyToItem(group);
//            // 最后，快照当前的pplId的状态到最后group_record_item上
//            snapshotItemStatus(demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
//                    "where version_group_record_id=?", doneRecord.getId()));

            return;
        }

        if (PplVersionGroupStatusEnum.isEqual(nodeStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY) &&
                PplVersionGroupStatusEnum.isEqual(changeStatus, PplVersionGroupStatusEnum.IN_SUBMIT)) {
            // STOCK_SUPPLY --> IN_SUBMIT

            // 更新版本组状态
            String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), group.getIndustryDept(), group.getProduct());
            group.setStatus(PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            group.setCurrentProcessor(processor);
            demandDBHelper.update(group);

            // 业务删除旧审批记录 库存对冲和
            demandDBHelper.executeRaw(
                    "update ppl_version_group_record set biz_deleted = 1 where version_group_id = ? and status in ('IN_SUBMIT','STOCK_SUPPLY')",
                    group.getId());
            // 新增record,
            PplVersionGroupRecordDO inSubmitRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            return;
        }

        if (PplVersionGroupStatusEnum.isEqual(nodeStatus, PplVersionGroupStatusEnum.PAAS_SUBMIT) &&
                PplVersionGroupStatusEnum.isEqual(changeStatus, PplVersionGroupStatusEnum.IN_SUBMIT)) {
            // PAAS_SUBMIT --> IN_SUBMIT

            // 更新版本组状态
            String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), group.getIndustryDept(), group.getProduct());
            group.setStatus(PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            group.setCurrentProcessor(processor);
            demandDBHelper.update(group);

            // 业务删除旧审批记录 库存对冲和
            demandDBHelper.executeRaw(
                    "update ppl_version_group_record set biz_deleted = 1 where version_group_id = ? and status in ('IN_SUBMIT','PAAS_SUBMIT')",
                    group.getId());
            // 新增record,
            PplVersionGroupRecordDO inSubmitRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            return;
        }

        if (PplVersionGroupStatusEnum.isEqual(nodeStatus, PplVersionGroupStatusEnum.WAIT_INTERVENE) &&
                PplVersionGroupStatusEnum.isEqual(changeStatus, PplVersionGroupStatusEnum.IN_SUBMIT)) {
            // WAIT_INTERVENE --> IN_SUBMIT

            // 更新版本组状态
            String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), group.getIndustryDept(), group.getProduct());
            group.setStatus(PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            group.setCurrentProcessor(processor);
            demandDBHelper.update(group);

            // 业务删除旧审批记录 库存对冲和
            demandDBHelper.executeRaw(
                    "update ppl_version_group_record set biz_deleted = 1 where version_group_id = ? and status in ('IN_SUBMIT','WAIT_INTERVENE')",
                    group.getId());
            // 新增record,
            PplVersionGroupRecordDO inSubmitRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            return;
        }

        if (PplVersionGroupStatusEnum.isEqual(nodeStatus, PplVersionGroupStatusEnum.COMD_INTERVENE) &&
                PplVersionGroupStatusEnum.isEqual(changeStatus, PplVersionGroupStatusEnum.IN_SUBMIT)) {
            // COMD_INTERVENE --> IN_SUBMIT

            // 更新版本组状态
            String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode(), group.getIndustryDept(), group.getProduct());
            group.setStatus(PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            group.setCurrentProcessor(processor);
            demandDBHelper.update(group);

            // 业务删除旧审批记录 库存对冲和
            demandDBHelper.executeRaw(
                    "update ppl_version_group_record set biz_deleted = 1 where version_group_id = ? and status in ('IN_SUBMIT','COMD_INTERVENE')",
                    group.getId());
            // 新增record,
            PplVersionGroupRecordDO inSubmitRecord = copyRecordToNextRecord(group,
                    PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            return;
        }

        log.info("状态不可以更改： source[%s] -> target[%s]", nodeStatus, changeStatus);
    }

    // 上升到 version， 等待 5 秒钟
    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = "ppl-approveVersionGroup", waitLockMillisecond = 5000, throwExceptionIfNotGetLock = false)
    public void approveVersionGroup(ApproveVersionGroupReq req) {
        PplVersionGroupApproveResultEnum approveResult = PplVersionGroupApproveResultEnum.
                getByCode(req.getApproveResult());
        if (approveResult == null) {
            throw new WrongWebParameterException("未知的审批结果");
        }

        PplVersionGroupInfoVO group = getGroup(req.getGroupId());
        String nodeStatus = group.getStatus();

        if (!StringUtil.equals(req.getCurrentStatus(), nodeStatus)) {
            throw new BizException("当前节点已被其他人处理过了，请刷新重试");
        }
        if (group.getPplVersionDO() == null) {
            throw new BizException(
                    "版本分组:" + group.getId() + "对应的版本" + group.getVersionCode() + "已经被删除，不允许审批");
        }
        if (!Objects.equals(group.getPplVersionDO().getStatus(), PplVersionStatusEnum.PROCESS.getCode())) {
            throw new BizException(
                    "版本分组:" + group.getId() + "对应的版本" + group.getVersionCode() + "不是进行中状态，不允许审批");
        }
        if (PplVersionGroupStatusEnum.DONE.getCode().equals(group.getStatus())) {
            throw new BizException("已经处于完成状态，不允许再审批操作");
        }

        checkPermission(group, nodeStatus, req.isSystem());

        // 1. 如果是驳回，那么驳回给行业干预人修改，状态：已驳回
        if (approveResult == PplVersionGroupApproveResultEnum.REJECT) {
            dealReject(req, approveResult, group);
            return;
        }
//        List<PplVersionGroupRecordDO> records = group.getRecords();
//        List<PplVersionGroupRecordDO> rejectRecords = ListUtils.filter(records,
//                (o) -> Strings.equals(o.getStatus(), PplVersionGroupStatusEnum.REJECT.getCode()));
        String nextGroupStatus = PplVersionGroupStatusEnum.getNextStatus(group.getStatus(), group.getProduct(), false);
        // todo https://iwiki.woa.com/pages/viewpage.action?pageId=4007560688

        // 对接待办系统
        String taskId = StringTools.join("-", group.getVersionCode(), group.getIndustryDept(),
                group.getProduct(), group.getStatus(), group.getLastRecordId());
        if (!StringUtil.equals(nodeStatus, PplVersionGroupStatusEnum.IN_SUBMIT.getCode())) {
            finishTodo(LoginUtils.getUserName(), taskId, "通过");
        }

        String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                nextGroupStatus, group.getIndustryDept(), group.getProduct());
        updateGroupRecordStatus(group, approveResult, req.getApproveNote(),
                nextGroupStatus, processor, req.isSystem());
        PplVersionGroupRecordDO recordNew = copyRecordToNextRecord(group, nextGroupStatus);

        if (!StringUtil.equals(nodeStatus, PplVersionGroupStatusEnum.PRE_SUBMIT.getCode()) &&
                !StringUtil.equals(nodeStatus, PplVersionGroupStatusEnum.CREATE.getCode())) {
            // 待录入节点状态的不创建todo
            pplVersionService.createTodoTask(group, recordNew);
        }

        // 23-8-11 废弃刷新预约数据逻辑
        // 如果当前的状态是   IN_SUBMIT , 提交的时候
        // 注意这里没有事务， 假如下面的代码出错了， 这里也会去刷新
        // 当前是 IN_SUBMIT, 一定会跳到下一个节点，不要停在这里了
//        if (Lang.list(PplVersionGroupStatusEnum.PRE_SUBMIT.getCode(),
//                PplVersionGroupStatusEnum.CREATE.getCode(),
//                PplVersionGroupStatusEnum.IN_SUBMIT.getCode()).contains(nodeStatus)
//                && !Strings.equals(group.getProduct(), Ppl13weekProductTypeEnum.GPU.getName())) {
//            long startWatch = System.currentTimeMillis();
//            updateYunXiaoApplyData(group.getId());
//            startWatch = System.currentTimeMillis() - startWatch;
//            taskLogService.genRunWarnLog("updateYunXiaoApplyData " + req.getGroupId(), "approveVersionGroup",
//                    String.valueOf(startWatch));
//        }

        if (Strings.equals(nextGroupStatus, PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode())) {
            WhereContent groupWhere = new WhereContent();
            groupWhere.andEqual(PplStockSupplyDO::getVersionCode, group.getVersionCode());
            List<PplVersionGroupDO> allGroups = ORMUtils.db(demandDBHelper)
                    .getAll(PplVersionGroupDO.class, groupWhere);
            List<PplVersionGroupDO> filter = ListUtils.filter(allGroups,
                    (o) -> Strings.equals(o.getStatus(), PplVersionGroupStatusEnum.STOCK_SUPPLY.getCode()));
            // 所有的进入了这里开始 对冲
            if (filter.size() == allGroups.size()) {
                //自动开启审批,库存对冲
                pplStockSupplyService.startNewStockSupply(group.getVersionCode(), true);
            }
        } else if (Strings.equals(nodeStatus, PplVersionGroupStatusEnum.COMD_APPROVE.getCode())) {
            //当前节点为物理机转换(云运管) 节点 触发推送数据到下游
            //  24-04-24 单个分组不单独推送到星云， 直接再流转完所有状态后，推所有分组信息过去
//            pplConvertPhysicalServerService.acceptPushRequestByGroupId(req.getGroupId());
        }

    }

    private void dealReject(ApproveVersionGroupReq req, PplVersionGroupApproveResultEnum approveResult,
            PplVersionGroupInfoVO group) {
        if (PplVersionGroupStatusEnum.REJECT.getCode().equals(group.getStatus())) {
            throw new BizException("已经处于驳回状态，不允许再驳回");
        }

        String taskId = StringTools.join("-", group.getVersionCode(), group.getIndustryDept(),
                group.getProduct(), group.getStatus(), group.getLastRecordId());

        String processor = PplVersionGroupStatusEnum.getProcessor(pplCommonService,
                PplVersionGroupStatusEnum.REJECT.getCode(), group.getIndustryDept(), group.getProduct());
        updateGroupRecordStatus(group, approveResult, req.getApproveNote(),
                PplVersionGroupStatusEnum.REJECT.getCode(), processor, req.isSystem());
        PplVersionGroupRecordDO recordNew = copyRecordToNextRecord(group,
                PplVersionGroupStatusEnum.REJECT.getCode());

        // 对接待办系统
        finishTodo(LoginUtils.getUserName(), taskId, "驳回");
        pplVersionService.createTodoTask(group, recordNew);
    }


    @Override
    public void updateYunXiaoApplyData(long groupId) {

        PplVersionGroupInfoVO group = getGroup(groupId);

        PplVersionGroupRecordDO lastRecord = group.getLastRecord();

        WhereContent versionItemWhere = new WhereContent();
        versionItemWhere.andEqual(PplVersionGroupRecordItemDO::getVersionGroupRecordId, lastRecord.getId());
        versionItemWhere.andNotEqual(PplVersionGroupRecordItemDO::getInstanceNum, 0);
        List<PplRecordItemJoinOrderVO> allDemand = ORMUtils.db(demandDBHelper).getAll(
                PplRecordItemJoinOrderVO.class, versionItemWhere);

        // 13周PPL中，客户可能存在uin为空的情况，这部分无法映射
        allDemand = allDemand.stream().filter((o) -> Strings.isNotBlank(o.getCustomerUin()))
                .collect(Collectors.toList());

        List<PplItemJoinOrderVO> yunXiaoData = getYunXiaoData(allDemand);

        Map<String, List<PplItemJoinOrderVO>> yunXiaoGroupByData = ListUtils.groupBy(yunXiaoData,
                (o) -> Strings.join("@", o.getProduct(), o.getIndustryDept(),
                        o.getYear(), o.getMonth(), o.getDemandType(), o.getCustomerUin(), o.getRegionName(),
                        o.getInstanceType(), o.getInstanceModel()
                ));

        Map<String, List<PplRecordItemJoinOrderVO>> demandGroupByData = ListUtils.groupBy(allDemand,
                (o) -> Strings.join("@", o.getProduct(), o.getIndustryDept(),
                        o.getYear(), o.getMonth(), o.getDemandType(), o.getCustomerUin(), o.getRegionName(),
                        o.getInstanceType(), o.getInstanceModel()
                ));

        // group by 用来匹配
        List<PplVersionGroupRecordItemDO> updateRecordItem = Lang.list();
        for (String demandKey : demandGroupByData.keySet()) {
            List<PplItemJoinOrderVO> yunXiaoDataGroup = yunXiaoGroupByData.get(demandKey);
            List<PplRecordItemJoinOrderVO> demandDataGroup = demandGroupByData.get(demandKey);

            // 云霄没有数据，说明没有预约数据
            if (Lang.isEmpty(yunXiaoDataGroup)) {
                continue;
            }

            // 分为两部分来映射
            // 先按照开始日期排序
            ListUtils.sortAscNullLast(demandDataGroup, PplRecordItemJoinOrderVO::getBeginBuyDate);

            List<PplRecordItemJoinOrderVO> data1 = demandDataGroup.stream()
                    .filter((o) -> !Strings.equals(o.getZoneName(), "随机可用区")).collect(Collectors.toList());
            List<PplRecordItemJoinOrderVO> data2 = demandDataGroup.stream()
                    .filter((o) -> Strings.equals(o.getZoneName(), "随机可用区")).collect(Collectors.toList());
            joinData(updateRecordItem, yunXiaoDataGroup, data1, true);
            joinData(updateRecordItem, yunXiaoDataGroup, data2, false);

        }
        demandDBHelper.update(updateRecordItem);
    }

    private void joinData(List<PplVersionGroupRecordItemDO> updateRecordItem,
            List<PplItemJoinOrderVO> yunXiaoDataGroup, List<PplRecordItemJoinOrderVO> data2, boolean filterZoneName) {
        for (PplRecordItemJoinOrderVO oneDemandData : data2) {
            int totalCore = oneDemandData.getTotalCore();
            int instanceNum = oneDemandData.getInstanceNum();
            // 只要有一条为0 直接停止匹配
            if (totalCore <= 0 || instanceNum <= 0) {
                continue;
            }

            List<PplItemJoinOrderVO> dealData = yunXiaoDataGroup;
            if (filterZoneName) {
                dealData = yunXiaoDataGroup.stream()
                        .filter((o) -> Strings.equals(o.getZoneName(), oneDemandData.getZoneName()))
                        .collect(Collectors.toList());
            }
            // 每一条都去匹配云霄的预约数据
            List<String> yunXiaoId = Lang.list();
            for (PplItemJoinOrderVO oneYunXiaoData : dealData) {

                int yunxiaoCore = oneYunXiaoData.getTotalCore();
                int yunxiaoNum = oneYunXiaoData.getInstanceNum();
//                    没有数量了不匹配
                if (yunxiaoCore <= 0 || yunxiaoNum <= 0) {
                    continue;
                }

                int minCore = Math.min(totalCore, yunxiaoCore);
                int minNum = Math.min(instanceNum, yunxiaoNum);

                totalCore -= minCore;
                instanceNum -= minNum;

                oneYunXiaoData.setTotalCore(yunxiaoCore - minCore);
                oneYunXiaoData.setInstanceNum(yunxiaoNum - minNum);
                yunXiaoId.add(oneYunXiaoData.getItemDO().getYunxiaoOrderId());

                if (totalCore <= 0 || instanceNum <= 0) {
                    // 匹配完了直接退出
                    break;
                }
            }
//
//            PplVersionGroupRecordItemDO groupRecordItemDO = oneDemandData.getGroupRecordItemDO();
//            groupRecordItemDO.setApplyTotalCore(oneDemandData.getTotalCore() - totalCore);
//            groupRecordItemDO.setApplyInstanceNum(oneDemandData.getInstanceNum() - instanceNum);
//            groupRecordItemDO.setYunxiaoOrderIds(Strings.join("@",
//                    Lang.list(com.pugwoo.wooutils.lang.DateUtils.formatDate(new Date())).addAll(yunXiaoId)));
//
//            updateRecordItem.add(groupRecordItemDO);
        }
    }

    private List<PplItemJoinOrderVO> getYunXiaoData(List<PplRecordItemJoinOrderVO> all) {

        // t1 item t2 order
        WhereContent itemWhere = new WhereContent();
        itemWhere.andIn("t1", "product",
                all.stream().map(PplRecordItemJoinOrderVO::getProduct).distinct()
                        .collect(Collectors.toList()));
        itemWhere.andIn("t2", "industry_dept",
                all.stream().map(PplRecordItemJoinOrderVO::getIndustryDept).distinct()
                        .collect(Collectors.toList()));
        // 废除年月字段 23-8-11 该方法已废弃 因此暂不做处理
//        itemWhere.andIn("t2", "year",
//                all.stream().map(PplRecordItemJoinOrderVO::getYear).distinct()
//                        .collect(Collectors.toList()));
//        itemWhere.andIn("t2", "month",
//                all.stream().map(PplRecordItemJoinOrderVO::getMonth).distinct().collect(Collectors.toList()));
        itemWhere.andIn("t1", "demand_type",
                all.stream().map(PplRecordItemJoinOrderVO::getDemandType).distinct().collect(Collectors.toList()));
        itemWhere.andIn("t2", "customer_uin",
                all.stream().map(PplRecordItemJoinOrderVO::getCustomerUin).distinct().collect(Collectors.toList()));
        itemWhere.andIn("t1", "region_name",
                all.stream().map(PplRecordItemJoinOrderVO::getRegionName).distinct().collect(Collectors.toList()));
        itemWhere.andIn("t1", "instance_type",
                all.stream().map(PplRecordItemJoinOrderVO::getInstanceType).distinct().collect(Collectors.toList()));
        itemWhere.andIn("t1", "instance_model",
                all.stream().map(PplRecordItemJoinOrderVO::getInstanceModel).distinct().collect(Collectors.toList()));

//        预约单取值范围：状态：资源准备中，待客户购买，结束
        itemWhere.andIn("t1", "yunxiao_order_status",
                Lang.list(YunxiaoOrderStatusEnum.RESOURCES_PREPARE.getCode(),
                        YunxiaoOrderStatusEnum.ON_SALE.getCode(),
                        YunxiaoOrderStatusEnum.FINISHED.getCode()));
        itemWhere.andIn("t2", "source", Lang.list(PplOrderSourceTypeEnum.SYNC_YUNXIAO.getCode()));
        itemWhere.andEqual("t1.status", "APPLIED");

        return ORMUtils.db(demandDBHelper).getAll(PplItemJoinOrderVO.class, itemWhere);
    }

    @Override
    public List<PplVersionGroupRecordItemVO> getVersionRecordItem(String pplId) {
        if (StringTools.isBlank(pplId)) {
            return new ArrayList<>();
        }
        return demandDBHelper.getAll(PplVersionGroupRecordItemVO.class, "where ppl_id=? order by id desc", pplId);
    }

    private void finishTodo(String approver, String taskId, String approveMemo) {
        TodoService.ApprovalMessageBody msg = new TodoService.ApprovalMessageBody();

        msg.setApproveMemo(approveMemo);
        msg.setApprover(approver);
        msg.setApproverOrder(taskId); // 实际是靠taskId来结束任务的
        msg.setApproveResult(0);

        pplCommonService.finishTodo(msg);
    }

    @Override
    public void snapshotItemStatus(List<PplVersionGroupRecordItemDO> items) {
        if (ListUtils.isEmpty(items)) {
            return;
        }
        List<String> pplIds = ListUtils.transform(items, o -> o.getPplId());
        List<PplItemDO> all = demandDBHelper.getAll(PplItemDO.class, "where ppl_id in (?)", pplIds);
        Map<String, PplItemDO> map = ListUtils.toMap(all, o -> o.getPplId(), o -> o);

        ListUtils.forEach(items, o -> {
            PplItemDO pplItemDO = map.get(o.getPplId());
            if (pplItemDO == null) {
                log.error("pplId:{} not exist, fail to update stauts to PplVersionGroupRecordItemDO", o.getPplId());
            } else {
                o.setStatus(pplItemDO.getStatus());
                demandDBHelper.update(o);
            }
        });
    }

    @Override
    public List<PplVersionGroupItemVO> queryPplVersionGroupItemVO(Long groupId) {
        // 查询最大的record版本
        PplVersionGroupRecordDO latestRecord = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id=? order by record_version desc limit 1", groupId);
        if (latestRecord == null) {
            return Collections.EMPTY_LIST;
        }

        List<PplVersionGroupItemVO> items = demandDBHelper.getAll(
                PplVersionGroupItemVO.class, "where version_group_record_id=? and instance_num > 0",
                latestRecord.getId());
        return items;
    }

    @Override
    public PplVersionGroupRecordDO queryLatestRecord(Long groupId) {
        return demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id=? order by record_version desc limit 1", groupId);
    }

    @Override
    public PplVersionGroupDO queryPplVersionGroup(Long groupId) {
        return demandDBHelper.getByKey(PplVersionGroupDO.class, groupId);
    }

    @Override
    public List<GroupItemDTO> queryGroupItemDtoWithComd(Long groupId) {
        return null;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void checkVersionDeadline() {
        PplVersionDO pplVersionDO = demandDBHelper.getOne(PplVersionDO.class, "where status = ? ",
                PplVersionStatusEnum.PROCESS.getCode());
        if (pplVersionDO != null && pplVersionDO.getDeadline() != null && pplVersionDO.getDeadline()
                .before(new Date())) {
            // 需要转化到云运管干预的组:  系统生成 待提交  paas接口人确认  待启动干预
            try {
                pplStockSupplyService.calcApplyForSupply(pplVersionDO.getVersionCode());
            } catch (Exception ex) {
                log.error("已预约数据生成失败", ex);
                AlarmRobotUtil.doAlarm("calcApplyForSupply", "已预约数据生成失败: " + ex.getMessage(), null, false);
                alert.sendRtx("oliverychen", "已预约数据生成失败", ex.getMessage());
            }

            List<String> transformStatus = new ArrayList<>(Arrays.asList(PplVersionGroupStatusEnum.CREATE.getCode(),
                    PplVersionGroupStatusEnum.PRE_SUBMIT.getCode(), PplVersionGroupStatusEnum.PAAS_SUBMIT.getCode(),
                    PplVersionGroupStatusEnum.WAIT_INTERVENE.getCode()));
            // 已经到截止日期了 判断是否还存在未转化的版本组
            List<PplVersionGroupInfoVO> all = demandDBHelper.getAll(PplVersionGroupInfoVO.class,
                    "where version_code = ? and product not in (?) and status in (?)"
                    , pplVersionDO.getVersionCode(), Arrays.asList(Ppl13weekProductTypeEnum.GPU.getName(),
                            Ppl13weekProductTypeEnum.BM.getName(),
                            Ppl13weekProductTypeEnum.DATABASE.getName(),
                            Ppl13weekProductTypeEnum.COS.getName()), transformStatus);

            // 获取云霄预约单已锁定的数据
            List<PplAppliedSupplylDO> appliedSupplylDOList = demandDBHelper.getAll(PplAppliedSupplylDO.class,
                    "where version_code = ? and apply_lock_core != 0", pplVersionDO.getVersionCode());
            Map<String, PplAppliedSupplylDO> pplIdToLockSupplyDO = new HashMap<>();
            if (ListUtils.isNotEmpty(appliedSupplylDOList)) {
                pplIdToLockSupplyDO = appliedSupplylDOList.stream()
                        .collect(Collectors.toMap(PplAppliedSupplylDO::getPplId, v -> v));
            }

            // 获取订单中已锁定的数据
            OrderQueryReq req = new OrderQueryReq();
            req.setOrderNodeCode(Arrays.asList(OrderNodeCodeEnum.node_order_supply.getCode()
                    , OrderNodeCodeEnum.node_order_following.getCode(), OrderNodeCodeEnum.node_order_close.getCode()));
            req.setOrderStatus(Arrays.asList(OrderStatusEnum.PROCESS.getCode(), OrderStatusEnum.FINISHED.getCode()));
            req.setOrderSource(OrderSourceEnum.PPL_TRANSFORM.getCode());
            List<OrderDetailResp> orderDetailRspList = orderCommonService.queryOrder(req);
            Map<String, OrderItemDTO> orderMap = orderDetailRspList.stream().flatMap(vo -> vo.getItemList().stream())
                    .collect(Collectors.toMap(OrderItemDTO::getSourcePplId, v -> v, (v1, v2) -> v1));

            for (PplVersionGroupInfoVO groupInfoVO : all) {
                // 更新版本中数据的已锁定资源量
                updateLockResourceNum(groupInfoVO, pplIdToLockSupplyDO, orderMap);
                // 从ppl_item同步预约状态到版本ppl中
                syncVersionPplApplyStatusFromPplItem(groupInfoVO);
//                 预约预测关联匹配处理，将PE单写入13周版本
//                2024-07-10        去除运管版本大包冲减逻辑。
//                forecastApplyMatch(groupInfoVO, pplIdToLockSupplyDO);
                // 到达录入截止时间,同步共识信息 https://iwiki.woa.com/p/4009356318#%E7%B3%BB%E7%BB%9F%E6%B5%81%E7%A8%8B
                syncConsensusInfoToVersionPpl(groupInfoVO);

                // 全部都转换到云运管干预
                PplVersionGroupRecordDO recordNew = import2NextRecord(groupInfoVO,
                        "到达录入截止时间,自动扭转流程", Boolean.FALSE,
                        PplVersionGroupStatusEnum.COMD_INTERVENE.getCode());
                pplVersionService.createTodoTask(groupInfoVO, recordNew);
            }
        }
    }

    private void syncConsensusInfoToVersionPpl(PplVersionGroupInfoVO groupInfoVO) {
        try {
            if (groupInfoVO == null) {
                return;
            }
            // 获取最新的共识版本
            String consensusVersionCode = getLatestConsensusVersionCode(groupInfoVO);
            if (StringUtils.isBlank(consensusVersionCode)) {
                return;
            }

            WhereContent where = new WhereContent()
                    .andEqual(PplSupplyConsensusDO::getVersionCode, consensusVersionCode)
                    .andEqualIfValueNotEmpty(PplSupplyConsensusDO::getIndustryDept, groupInfoVO.getIndustryDept())
                    .andEqualIfValueNotEmpty(PplSupplyConsensusDO::getProduct, groupInfoVO.getProduct());
            List<PplSupplyConsensusDO> consensusList = demandDBHelper
                    .getAll(PplSupplyConsensusDO.class, where.getSql(), where.getParams());
            Map<String, List<PplSupplyConsensusDO>> map = ListUtils
                    .toMapList(consensusList, PplSupplyConsensusDO::getPplId, item -> item);

            List<PplVersionGroupRecordItemDO> list = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                    "where version_group_record_id = ?", groupInfoVO.getLastRecordId());
            // 批量更新版本ppl的共识信息
            List<PplVersionGroupRecordItemDO> updateList = new ArrayList<>();
            for (PplVersionGroupRecordItemDO itemVO : list) {
                if (itemVO == null || itemVO.getId() == null || StringUtils.isBlank(itemVO.getPplId())) {
                    continue;
                }
                List<PplSupplyConsensusDO> consensusDOS = map.get(itemVO.getPplId());
                if (ListUtils.isEmpty(consensusDOS)) {
                    consensusDOS = new ArrayList<>();
                }
                PplVersionGroupRecordItemDO copy = new PplVersionGroupRecordItemDO();
                copy.setId(itemVO.getId());
                copy.calcAnSetConsensusInfo(consensusDOS);
                updateList.add(copy);
            }
            if (ListUtils.isNotEmpty(updateList)) {
                demandDBHelper.update(updateList);
            }
        } catch (Exception e) {
            alert.sendRtx("dotyou", "到达录入截止时间,同步共识信息到版本ppl中失败, versionGroupRecordId:"
                    + groupInfoVO.getLastRecordId(), e.getClass().toString() + ":" + e.getMessage());
            throw e;
        }
    }

    private String getLatestConsensusVersionCode(PplVersionGroupDO groupInfo) {
        if (groupInfo == null) {
            return null;
        }
        WhereContent where = new WhereContent()
                .andEqualIfValueNotEmpty(PplSupplyConsensusDO::getIndustryDept, groupInfo.getIndustryDept())
                .andEqualIfValueNotEmpty(PplSupplyConsensusDO::getProduct, groupInfo.getProduct())
                .orderDesc("id");
        PplSupplyConsensusDO one = demandDBHelper.getOne(PplSupplyConsensusDO.class, where.getSql(), where.getParams());
        return one == null ? null : one.getVersionCode();
    }

    /**
     * 匹配已锁定资源量
     *
     * @param groupInfoVO
     * @param pplIdToLockSupplyDO
     */
    private void updateLockResourceNum(PplVersionGroupInfoVO groupInfoVO,
            Map<String, PplAppliedSupplylDO> pplIdToLockSupplyDO, Map<String, OrderItemDTO> pplIdToOrderMap) {
        if (groupInfoVO.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())
                || groupInfoVO.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            return;
        }
        if (pplIdToLockSupplyDO.size() == 0 && pplIdToOrderMap.size() == 0) {
            return;
        }

        List<PplVersionGroupRecordItemDO> all = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                "where version_group_record_id = ?", groupInfoVO.getLastRecordId());
        List<PplVersionGroupRecordItemDO> updateList = new ArrayList<>();
        for (PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO : all) {
            OrderItemDTO orderItemDTO = pplIdToOrderMap.get(pplVersionGroupRecordItemDO.getPplId());
            if (orderItemDTO != null) {
                // 更新已锁定资源量
                pplVersionGroupRecordItemDO.setIsLock(Boolean.TRUE);
                updateList.add(pplVersionGroupRecordItemDO);
                continue;
            }

            PplAppliedSupplylDO pplAppliedSupplylDO = pplIdToLockSupplyDO.get(
                    pplVersionGroupRecordItemDO.getPplId());
            if (pplAppliedSupplylDO != null) {
                // 更新已锁定资源量
                pplVersionGroupRecordItemDO.setIsLock(Boolean.TRUE);
                updateList.add(pplVersionGroupRecordItemDO);
            }

        }
        demandDBHelper.update(updateList);
    }

    // 从ppl_item同步预约状态到版本ppl(版本中最新的ppl记录)中
    private void syncVersionPplApplyStatusFromPplItem(PplVersionGroupInfoVO groupInfoVO) {
        try {
            if (groupInfoVO == null) {
                return;
            }
            // 删除取消预约的PE单
            deleteCancelPplOfVersionGroup(groupInfoVO.getId());

            //
            List<PplVersionGroupItemVO> list = demandDBHelper.getAll(PplVersionGroupItemVO.class,
                    "where version_group_record_id = ?", groupInfoVO.getLastRecordId());
            list.removeIf(item -> item == null || item.getPplItemDO() == null || item.getId() == null);
            Map<String, List<PplVersionGroupItemVO>> statusMap = ListUtils.toMapList(list,
                    item -> item.getPplItemDO().getStatus(), v -> v);
            // 批量更新版本ppl的预约状态 以及预约量
            List<PplVersionGroupRecordItemDO> updateList = new ArrayList<>();
            for (PplVersionGroupItemVO pplVersionGroupItemVO : list) {
                PplItemDO pplItemDO = pplVersionGroupItemVO.getPplItemDO();
                if (!pplItemDO.getStatus().equals(pplVersionGroupItemVO.getStatus())
                        || pplItemDO.getStatus().equals(PplItemStatusEnum.APPLIED.getCode())) {
                    // 如果状态不一致
                    pplVersionGroupItemVO.setStatus(pplItemDO.getStatus());
                    pplVersionGroupItemVO.setYunxiaoOrderId(pplItemDO.getYunxiaoOrderId());
                    pplVersionGroupItemVO.setInstanceNum(pplItemDO.getInstanceNum());
                    pplVersionGroupItemVO.setTotalCore(pplItemDO.getTotalCore());
                    pplVersionGroupItemVO.setTotalGpuNum(pplItemDO.getTotalGpuNum());
                    pplVersionGroupItemVO.setInstanceNumApplyAfter(pplItemDO.getInstanceNumApplyAfter());
                    pplVersionGroupItemVO.setTotalCoreApplyAfter(pplItemDO.getTotalCoreApplyAfter());
                    pplVersionGroupItemVO.setTotalGpuNumApplyAfter(pplItemDO.getTotalGpuNumApplyAfter());
                    pplVersionGroupItemVO.setUpdateTime(new Date());
                    updateList.add(pplVersionGroupItemVO);
                }
            }
            if (ListUtils.isNotEmpty(updateList)) {
                demandDBHelper.update(updateList);
            }
        } catch (Exception e) {
            alert.sendRtx("dotyou", "从ppl_item同步预约状态到版本ppl中失败, versionGroupRecordId:"
                    + groupInfoVO.getLastRecordId(), e.getClass().toString() + ":" + e.getMessage());
            throw e;
        }
    }

//    private void updatePplVersionGroupItemStatusByIds(String status, List<Long> pplVersionGroupItemIdList) {
//        if (ListUtils.isEmpty(pplVersionGroupItemIdList)) {
//            return;
//        }
//        BatchUtil.syncBatchExec(pplVersionGroupItemIdList, 1000, ids -> {
//            if (status == null) {
//                demandDBHelper.executeRaw("update ppl_version_group_record_item set status = null, update_time = now() "
//                        + "where deleted = 0 and id in (?)", ids);
//            } else {
//                demandDBHelper.executeRaw("update ppl_version_group_record_item set status = ? , update_time = now() "
//                        + "where deleted = 0 and id in (?)", status, ids);
//            }
//        });
//    }

    @Override
    @Transactional("demandTransactionManager")
    public void forecastApplyMatch(Long versionGroupInfoId) {
        PplVersionGroupInfoVO vo = demandDBHelper.getByKey(PplVersionGroupInfoVO.class, versionGroupInfoId);
        forecastApplyMatch(vo, new HashMap<>());
    }

    /**
     * <a href="https://iwiki.woa.com/p/4008538330">预测、预约关联匹配，将PE单也写入13周版本数据中</a>
     */
    public void forecastApplyMatch(PplVersionGroupInfoVO groupInfo,
            Map<String, PplAppliedSupplylDO> pplIdToLockSupplyDO) {
        if (groupInfo == null) {
            return;
        }
        // 删除取消预约的PE单
        deleteCancelPplOfVersionGroup(groupInfo.getId());

        // 获取未预约的预测单 未预约的PN单
        List<PplItemForecastApplyMatchVO> forecastList = demandDBHelper.getRaw(PplItemForecastApplyMatchVO.class,
                "select b.industry_dept ,a.* from ppl_version_group_record_item a\n"
                        + "left join ppl_order b on a.ppl_order = b.ppl_order \n"
                        + "where a.deleted = 0 and b.deleted = 0 and"
                        + " a.status != ? and a.version_group_record_id = ? and a.ppl_id like 'PN%' ",
                PplItemStatusEnum.APPLIED.getCode(), groupInfo.getLastRecordId());
        if (forecastList == null) {
            forecastList = new ArrayList<>();
        }
        Map<String, List<PplItemForecastApplyMatchVO>> forecastMap = ListUtils
                .toMapList(forecastList, this::forecastApplyMatchKey, item -> item);

        // 获取预测外预约自动补的预测单 PE单
        String sql = ORMUtils.getSql("/sql/ppl13week/forecast_apply_match_vo_from_ppl_item.sql");
        List<PplItemForecastApplyMatchVO> applyList = demandDBHelper.getRaw(PplItemForecastApplyMatchVO.class,
                sql, groupInfo.getLastRecordId(), groupInfo.getVersionCode(), groupInfo.getProduct(),
                groupInfo.getIndustryDept());
        if (ListUtils.isEmpty(applyList)) {
            return;
        }

//        不再判断    isLongtailUin 信任补预约单时的中长尾判定。
//        applyList.removeIf(item -> {
//            if (pplApplyService.isLongtailUin(item.getIndustryDept(), item.getCustomerUin())) {
//                // 移除中长尾客户，只保留头部客户
//                return true;
//            }
//            item.setVersionGroupId(groupInfo.getId());
//            item.setVersionGroupRecordId(groupInfo.getLastRecordId());
//            item.setRecordVersion(groupInfo.getLastRecord().getRecordVersion());
//            return false;
//        });
        for (PplItemForecastApplyMatchVO pplItemForecastApplyMatchVO : applyList) {
            pplItemForecastApplyMatchVO.setVersionGroupId(groupInfo.getId());
            pplItemForecastApplyMatchVO.setVersionGroupRecordId(groupInfo.getLastRecordId());
            pplItemForecastApplyMatchVO.setRecordVersion(groupInfo.getLastRecord().getRecordVersion());
            PplAppliedSupplylDO pplAppliedSupplylDO = pplIdToLockSupplyDO.get(pplItemForecastApplyMatchVO.getPplId());
            // 如果该ppl已锁定量>0 那么不参与 行业级大包扣减
            pplItemForecastApplyMatchVO.setIsLock(Boolean.FALSE);
            if (pplAppliedSupplylDO != null) {
                pplItemForecastApplyMatchVO.setIsLock(Boolean.TRUE);
            }
        }
        Map<String, List<PplItemForecastApplyMatchVO>> applyMap = ListUtils
                .toMapList(applyList, this::forecastApplyMatchKey, item -> item);

        boolean isGpu = Ppl13weekProductTypeEnum.GPU.getName().equals(groupInfo.getProduct());
        applyMap.forEach((key, peList) -> {
            List<PplItemForecastApplyMatchVO> pnList = forecastMap == null ? null : forecastMap.get(key);
            if (ListUtils.isEmpty(pnList)) {
                return;
            }
            for_pe:
            for (PplItemForecastApplyMatchVO pe : peList) {
                if (isGpu) {
                    // gpu
                    BigDecimal oneInstanceCore = pe.getInstanceNum() == null || pe.getInstanceNum() == 0
                            ? new BigDecimal(pe.getTotalGpuNum().toString())
                            : NumberUtils.divide(pe.getTotalGpuNum(), pe.getInstanceNum(), 0);
                    for (PplItemForecastApplyMatchVO pn : pnList) {
                        int peCore = pe.getTotalGpuNum() == null ? 0 : pe.getTotalGpuNum().intValue();
                        int pnCore = pn.getTotalGpuNum() == null ? 0
                                : (
                                        pn.getOccupiedCoreNum() == null
                                                ? pn.getTotalGpuNum().intValue()
                                                : pn.getTotalGpuNum().intValue() - pn.getOccupiedCoreNum()
                                );
                        if (peCore < 1) {
                            break for_pe;
                        }
                        if (pnCore < 1) {
                            continue;
                        }
                        if (peCore <= pnCore) {
                            // 1、PE1 核心数＜PN1 调整后，关联上，PN1剩余的还可以给其他PE调整；
                            // 2、PE1 核心数=PN1 刚好调整，关联上
                            pe.setTotalGpuNum(BigDecimal.ZERO);
                            pe.addOccupyOthersNum(peCore);
                            pe.addOccupyOthersPplId(pn.getPplId());
                            BigDecimal instanceNum = oneInstanceCore == null || oneInstanceCore.equals(BigDecimal.ZERO)
                                    ? null : NumberUtils.divide(pe.getTotalGpuNum(), oneInstanceCore.intValue(), 0);
                            pe.setInstanceNum(instanceNum == null ? 0 : instanceNum.intValue());
                            // 记录一下用掉了多少pn单需求量
                            pn.addOccupiedNum(peCore);
                            pn.addOccupiedByPplId(pe.getPplId());
                            break;
                        } else {
                            // 3、PE1核心数＞PN1 需要关联上，PPE1的剩余部分也应该继续匹配，直到没有可用的PN为止。
                            pe.setTotalGpuNum(new BigDecimal(String.valueOf(peCore - pnCore)));
                            pe.addOccupyOthersNum(pnCore);
                            pe.addOccupyOthersPplId(pn.getPplId());
                            BigDecimal instanceNum = oneInstanceCore == null || oneInstanceCore.equals(BigDecimal.ZERO)
                                    ? null : NumberUtils.divide(pe.getTotalGpuNum(), oneInstanceCore.intValue(), 0);
                            pe.setInstanceNum(instanceNum == null ? 0 : instanceNum.intValue());
                            // 记录一下用掉了多少pn单需求量
                            pn.addOccupiedNum(pnCore);
                            pn.addOccupiedByPplId(pe.getPplId());
                            continue;
                        }
                    }
                } else {
                    if (pe.getIsLock()) {
                        continue;
                    }
                    BigDecimal oneInstanceCore = pe.getInstanceNum() == null || pe.getInstanceNum() == 0
                            ? new BigDecimal(pe.getTotalCore().toString())
                            : NumberUtils.divide(pe.getTotalCore(), pe.getInstanceNum(), 0);
                    for (PplItemForecastApplyMatchVO pn : pnList) {
                        int peCore = pe.getTotalCore() == null ? 0 : pe.getTotalCore();
                        int pnCore = pn.getTotalCore() == null ? 0
                                : (
                                        pn.getOccupiedCoreNum() == null
                                                ? pn.getTotalCore()
                                                : pn.getTotalCore() - pn.getOccupiedCoreNum()
                                );
                        if (peCore < 1) {
                            break for_pe;
                        }
                        if (pnCore < 1) {
                            continue;
                        }
                        if (peCore <= pnCore) {
                            // 1、PE1 核心数＜PN1 调整后，关联上，PN1剩余的还可以给其他PE调整；
                            // 2、PE1 核心数=PN1 刚好调整，关联上
                            pe.setTotalCore(0);
                            pe.addOccupyOthersNum(peCore);
                            pe.addOccupyOthersPplId(pn.getPplId());
                            BigDecimal instanceNum = oneInstanceCore == null || oneInstanceCore.equals(BigDecimal.ZERO)
                                    ? null : NumberUtils.divide(pe.getTotalCore(), oneInstanceCore.intValue(), 0);
                            pe.setInstanceNum(instanceNum == null ? 0 : instanceNum.intValue());
                            // 记录一下用掉了多少pn单需求量
                            pn.addOccupiedNum(peCore);
                            pn.addOccupiedByPplId(pe.getPplId());
                            break;
                        } else {
                            // 3、PE1核心数＞PN1 需要关联上，PPE1的剩余部分也应该继续匹配，直到没有可用的PN为止。
                            pe.setTotalCore(peCore - pnCore);
                            pe.addOccupyOthersNum(pnCore);
                            pe.addOccupyOthersPplId(pn.getPplId());
                            BigDecimal instanceNum = oneInstanceCore == null || oneInstanceCore.equals(BigDecimal.ZERO)
                                    ? null : NumberUtils.divide(pe.getTotalCore(), oneInstanceCore.intValue(), 0);
                            pe.setInstanceNum(instanceNum == null ? 0 : instanceNum.intValue());
                            // 记录一下用掉了多少pn单需求量
                            pn.addOccupiedNum(pnCore);
                            pn.addOccupiedByPplId(pe.getPplId());
                            continue;
                        }
                    }
                }
            }
        });
        // 将预测外预约数据写进13周版本
        demandDBHelper.insertOrUpdate(PplItemForecastApplyMatchVO.toItemDOList(applyList));
        // 更新预测数据的被占用量、占用者PPLID
        demandDBHelper.update(PplItemForecastApplyMatchVO.toItemDOList(forecastList));
    }

    // 删除13周版本组中取消预约的PE单
    private void deleteCancelPplOfVersionGroup(Long versionGroupId) {
        List<PplVersionGroupRecordItemDO> systemItemList = demandDBHelper.getRaw(PplVersionGroupRecordItemDO.class,
                "select a.* from ppl_version_group_record_item a \n"
                        + " inner join ppl_order b on a.ppl_order = b.ppl_order \n"
                        + " where a.deleted = 0 and b.deleted = 0 \n"
                        + " and b.source in('APPLY_AUTO_FILL', 'SYNC_YUNXIAO') \n"
                        + " and a.version_group_id = ? ", versionGroupId);
        List<String> pplIds = systemItemList.stream().map(PplVersionGroupRecordItemDO::getPplId).distinct()
                .collect(Collectors.toList());
        if (ListUtils.isNotEmpty(pplIds)) {
            Map<String, PplItemDO> pplItemDOMap = demandDBHelper.getAll(PplItemDO.class, "where ppl_id in(?)", pplIds)
                    .stream().collect(Collectors.toMap(PplItemDO::getPplId, v -> v, (v1, v2) -> v1));

            List<String> cancelStatusList = Arrays.asList(YunxiaoOrderStatusEnum.CANCELED.getCode(),
                    YunxiaoOrderStatusEnum.BAD_CANCELED.getCode(), YunxiaoOrderStatusEnum.REJECTED.getCode());

            List<Long> updateRecordIds = new ArrayList<>();
            for (PplVersionGroupRecordItemDO itemDO : systemItemList) {
                PplItemDO pplItemDO = pplItemDOMap.get(itemDO.getPplId());
                if (pplItemDO == null || !PplItemStatusEnum.APPLIED.getCode().equals(pplItemDO.getStatus())
                        || cancelStatusList.contains(pplItemDO.getYunxiaoOrderStatus())) {
                    updateRecordIds.add(itemDO.getId());
                }
            }
            if (ListUtils.isNotEmpty(updateRecordIds)) {
                demandDBHelper.executeRaw("update ppl_version_group_record_item set deleted = 1 where id in (?) ",
                        updateRecordIds);
            }
        }
    }

    private String forecastApplyMatchKey(PplItemForecastApplyMatchVO item) {
        InstanceTypeRelateDTO relateDTO = pplDictService.queryInstanceTypesForCommon(item.getInstanceType());
        String instanceType = item.getInstanceType();
        if (relateDTO != null) {
            instanceType = relateDTO.getCommonInstanceType();
        }
        return new StringJoiner("@")
                .add(item.getProduct())
                .add(item.getDemandType())
                .add(item.getIndustryDept())
                .add(String.valueOf(item.getBeginBuyDate().getYear()))
                .add(String.valueOf(item.getBeginBuyDate().getMonthValue()))
                .add(instanceType)
                .add(item.getRegionName())
                .toString();
    }

    @Override
    @Transactional("demandTransactionManager")
    public void sendInSubmitMail() {
        Boolean isTest = org.apache.logging.log4j.util.Strings.isNotBlank(testEnv.get());
        //  测试环境关掉
        String prefix = isTest ? "exp-" : "";
        PplVersionDO pplVersionDO = demandDBHelper.getOne(PplVersionDO.class, "where status = ? ",
                PplVersionStatusEnum.PROCESS.getCode());
        if (pplVersionDO != null && pplVersionDO.getDeadline() != null && pplVersionDO.getDeadline()
                .after(new Date())) {
            // 未到截止日期 查询出处于录入中状态的版本组
            List<PplVersionGroupInfoVO> all = demandDBHelper.getAll(PplVersionGroupInfoVO.class,
                    "where version_code = ? and status = ?", pplVersionDO.getVersionCode()
                    , PplVersionGroupStatusEnum.IN_SUBMIT.getCode());
            for (PplVersionGroupInfoVO groupInfoVO : all) {
                String currentProcessor = groupInfoVO.getCurrentProcessor();
                PplVersionGroupRecordDO lastRecord = groupInfoVO.getLastRecord();
                List<PplVersionGroupRecordItemDO> itemDOList = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                        "where version_group_record_id = ? and instance_num > 0", lastRecord.getId());
                if (CollectionUtils.isEmpty(itemDOList)) {
                    return;
                }
                if (isTest) {
                    if (currentProcessor.contains("kaijiazhang") || currentProcessor.contains("oliverychen")) {
                        // 锴佳能收到测试环境邮件
                        currentProcessor = "kaijiazhang;oliverychen;erickssu;brightwwu";
                    } else {
                        return;
                    }
                }
                alert.sendMail(currentProcessor, "行业13周预测未提交预警",
                        "您负责的行业 " + groupInfoVO.getIndustryDept() + "-" + groupInfoVO.getProduct() +
                                " 有" + itemDOList.size() + "条需求属于“录入中”状态，用户还未提交。请尽快与行业确认并提交数据，未提交的预测将不参与对冲。"
                                + "<a href='https://" + prefix
                                + "crp.woa.com/13ppl/view-approval/" + groupInfoVO.getId().toString()
                                + "'>点击前往</a>。<br />");

            }
        }
    }

    @Override
    public List<PplVersionGroupRecordItemDO> queryLatestPplVersionItem(String versionCode, List<String> pplIdList) {
        if (Strings.isBlank(versionCode)) {
            return new ArrayList<>();
        }
        String sql = "select * from ppl_version_group_record_item\n"
                + "where deleted = 0 and version_group_record_id IN (\n"
                + "    SELECT MAX(id) FROM `ppl_version_group_record`\n"
                + "    WHERE deleted=0\n"
                + "    AND version_group_id IN (SELECT id FROM `ppl_version_group` "
                + "                             WHERE deleted=0 and version_code = ?)\n"
                + "    GROUP BY version_group_id\n"
                + ") ";
        if (ListUtils.isNotEmpty(pplIdList)) {
            sql = sql + " and ppl_id in (?)";
            return demandDBHelper.getRaw(PplVersionGroupRecordItemDO.class, sql, versionCode, pplIdList);
        }
        return demandDBHelper.getRaw(PplVersionGroupRecordItemDO.class, sql, versionCode);
    }

    @Override
    public List<PplVersionGroupRecordItemDO> queryLatestPplVersionItemByVersionGroupId(Long versionGroupId,
            List<String> pplIdList) {
        if (versionGroupId == null) {
            return new ArrayList<>();
        }
        String sql = "select * from ppl_version_group_record_item\n"
                + "where deleted = 0 and version_group_record_id IN (\n"
                + "    SELECT MAX(id) FROM `ppl_version_group_record`\n"
                + "    WHERE deleted=0 AND version_group_id = ? "
                + "    GROUP BY version_group_id\n"
                + ") ";
        if (ListUtils.isNotEmpty(pplIdList)) {
            sql = sql + " and ppl_id in (?)";
            return demandDBHelper.getRaw(PplVersionGroupRecordItemDO.class, sql, versionGroupId, pplIdList);
        }
        return demandDBHelper.getRaw(PplVersionGroupRecordItemDO.class, sql, versionGroupId);
    }

    @Override
    public String refreshGpuGap(String versionCode, String industryDept, String product) {
        PplVersionGroupDO one = demandDBHelper.getOne(PplVersionGroupDO.class,
                "where version_code = ? and product = ? and industry_dept = ? and status in (?)",
                versionCode, product, industryDept, Arrays.asList(PplVersionGroupStatusEnum.CREATE.getCode(),
                        PplVersionGroupStatusEnum.PRE_SUBMIT.getCode()));
        if (one == null) {
            return "没有系统继承或预提交的分组";
        }
        List<PplAuditItemQueueDO> AllItemQueueDOList = demandDBHelper.getAll(PplAuditItemQueueDO.class,
                "where version_id = (SELECT max(version_id) from ppl_audit_item_queue where "
                        + "deleted = 0 and industry_dept = ? and product = ?)",
                industryDept, product);
        PplVersionGroupRecordDO recordDO = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id = ? and status = ?",
                one.getId(), one.getStatus());
        demandDBHelper.executeRaw("update ppl_version_group_record_item set deleted = 1 "
                + "where version_group_record_id = ?", recordDO.getId());
        Integer newRecordVersion = pplCommonService.getVersionGroupRecordVersion();
        List<PplVersionGroupRecordItemDO> recordItemDOList = new ArrayList<>();
        for (PplAuditItemQueueDO pplAuditItemQueueDO : AllItemQueueDOList) {
            PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO = JsonUtil.toObject(
                    pplAuditItemQueueDO.getItemJson(), PplVersionGroupRecordItemDO.class);
            pplVersionGroupRecordItemDO.setAlternativeInstanceType(
                    pplVersionGroupRecordItemDO.getAlternativeInstanceType() == null ? ""
                            : pplVersionGroupRecordItemDO.getAlternativeInstanceType());
            pplVersionGroupRecordItemDO.setVersionGroupId(one.getId());
            pplVersionGroupRecordItemDO.setVersionGroupRecordId(recordDO.getId());
            pplVersionGroupRecordItemDO.setRecordVersion(newRecordVersion);
            pplVersionGroupRecordItemDO.setImportType("sync");
            recordItemDOList.add(pplVersionGroupRecordItemDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(recordItemDOList);
        return "同步成功";
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void syncYunTiPpl(String versionCode) {
        PplVersionDO pplVersionDO = demandDBHelper.getOne(PplVersionDO.class, "where version_code = ?", versionCode);
        if (pplVersionDO == null) {
            return;
        }
        LocalDate versionBeginDate = LocalDate.of(pplVersionDO.getDemandBeginYear(), pplVersionDO.getDemandBeginMonth(),
                1);
        String industryDept = "内部业务部";
        // 去查yunti的需求
        Map<String, List<GroupItemDTO>> map = yuntiDemand2CrpService.publicPool2Crp(new publicPoolReq(pplVersionDO));

        // 海外地域
        List<String> overseas = pplDictService.queryAllRegionName(true);


        // 目前仅接收 cvm
        map.forEach((k, v) -> {
            if (k != null && !k.equals(Ppl13weekProductTypeEnum.CVM.getName())) {
                return;
            }
            PplVersionGroupDO versionGroupDOS = demandDBHelper.getOne(PplVersionGroupDO.class,
                    "where industry_dept = ? and product = ? and version_code = ? and status in (?)",
                    industryDept, k, pplVersionDO.getVersionCode(),
                    Arrays.asList(PplVersionGroupStatusEnum.CREATE.getCode(),
                            PplVersionGroupStatusEnum.PRE_SUBMIT.getCode()));
            if (versionGroupDOS == null) {
                return;
            }

            v.removeIf(o -> {
                // 非退回需求 && 海外地域 && 海外需求时间不满足 需要剔除掉
                if (!o.getDemandType().equals(PplDemandTypeEnum.RETURN.getCode())
                && overseas.contains(o.getRegionName())
                && !pplVersionDO.isSatisfyOverseasYearMonth(o.getBeginBuyDate())){
                    return true;
                }
                return false;
            });

            // 1、删除版本明细的ppl数据
            demandDBHelper.executeRaw("update ppl_version_group_record_item set deleted = 1 where deleted = 0 "
                    + "and version_group_id = ? ", versionGroupDOS.getId());

            // 2、删除范围内的所有最新版ppl
            List<String> pplOrderList = demandDBHelper.getRaw(String.class,
                    "select ppl_order from ppl_order where deleted = 0 and source = ? and industry_dept = ? ",
                    PplOrderSourceTypeEnum.IMPORT.getCode(), "内部业务部");
            demandDBHelper.executeRaw("update ppl_item set deleted = 1 where deleted = 0 "
                            + "and product = ? and ppl_order in (?) and begin_buy_date >= ?"
                    , k, pplOrderList, versionBeginDate.toString());

            // 3、导入云梯ppl到版本明细中
            ImportVersionGroupReq req = new ImportVersionGroupReq();
            req.setGroupId(versionGroupDOS.getId());
            req.setIndustryDept("内部业务部");
            req.setProduct(k);
            req.setSourceTypeEnum(PplOrderSourceTypeEnum.IMPORT);
            req.setData(v);
            importPplVersionItem(req);

            // 4、同步到最新版数据
            List<PplVersionGroupRecordItemDO> all = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                    "where version_group_id = ?", versionGroupDOS.getId());
            List<PplItemDO> transform = ListUtils.transform(all, this::toItemDO);
            demandDBHelper.insertBatchWithoutReturnId(transform);

            // 5、扭转到下一节点
            ApproveVersionGroupReq approveVersionGroupReq = new ApproveVersionGroupReq();
            approveVersionGroupReq.setGroupId(versionGroupDOS.getId());
            approveVersionGroupReq.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            approveVersionGroupReq.setCurrentStatus(versionGroupDOS.getStatus());
            approveVersionGroupReq.setApproveNote("同步云梯公有池数据，自动审批");
            approveVersionGroupReq.setSystem(Boolean.TRUE);
            approveVersionGroup(approveVersionGroupReq);

        });
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void refreshSpike() {
        List<PplVersionGroupInfoVO> all = demandDBHelper.getAll(PplVersionGroupInfoVO.class,
                "where id < ? and status = ? and product = ?",
                10105, PplVersionGroupStatusEnum.DONE.getCode(), Ppl13weekProductTypeEnum.CVM.getName());
        for (PplVersionGroupInfoVO groupInfoVO : all) {
            List<PplVersionGroupRecordItemDO> itemList = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                    "where version_group_record_id = ?", groupInfoVO.getLastRecordId());
            if (ListUtils.isEmpty(itemList)) {
                continue;
            }
            setThresholds(itemList);
            Map<Integer, List<PplVersionGroupRecordItemDO>> group = itemList.stream()
                    .collect(Collectors.groupingBy(PplVersionGroupRecordItemDO::getIsSpike));
            group.forEach((k, v) -> {
                List<Long> ids = v.stream().map(PplVersionGroupRecordItemDO::getId).collect(Collectors.toList());
                demandDBHelper.executeRaw("update ppl_version_group_record_item set is_spike = ? "
                        + "where id in (?)", k, ids);
            });

        }
    }

    @Override
    public void setThresholdsForCBS(List<PplVersionGroupRecordItemDO> pplItems) {
        // 获取所有的 pplOrder
        Map<String, PplOrderDO> pplOrderMap = getAllPplOrder(pplItems);
        //先将所有的PPL订单设置为长尾报备
        pplItems.forEach(o -> o.setCbsIsSpike(0));

        //在CBS大客户名单的所有pplItems全都置为头部项目
        Set<String> headCustomerShortName = ppl13weekCommonDataAccess.getAllHeadCustomerShortNameForCBS();
        pplItems = ListUtils.filter(pplItems, (o) -> {
            PplOrderDO pplOrderDO = pplOrderMap.get(o.getPplOrder());
            assert pplOrderDO != null;
            boolean isHeadCustomer = headCustomerShortName.contains(pplOrderDO.getCustomerShortName());
            if (isHeadCustomer) {
                o.setCbsIsSpike(1);
            }
            return !isHeadCustomer;
        });

        //接着将所有模型预测的ppl数据置为长尾模型预测
        ListUtils.filter(pplItems, o -> pplOrderMap.get(o.getPplOrder()).getIndustryDept().equals("中长尾"))
                .forEach(o -> o.setCbsIsSpike(2));

        //针对剩下的需求，按照客户+可用区+地域+月份维度进行聚合
        Map<String, String> region2CustomhouseTitleMap = getRegion2CustomhouseTitleMap();
        pplItems = pplItems.stream().filter(o -> o.getCbsIsSpike() == 0).collect(Collectors.toList());
        Map<String, List<PplVersionGroupRecordItemDO>> mapList = ListUtils.toMapList(pplItems, o -> {
            PplOrderDO pplOrderDO = pplOrderMap.get(o.getPplOrder());
            LocalDate beginBuyDate = o.getBeginBuyDate();
            return String.join("@", pplOrderDO.getCustomerShortName(),
                    java.time.YearMonth.of(beginBuyDate.getYear(), beginBuyDate.getMonthValue()).toString(),
                    o.getZoneName(), o.getRegionName());
        }, o -> o);
        for (Entry<String, List<PplVersionGroupRecordItemDO>> zoneEntry : mapList.entrySet()) {
            BigDecimal systemCapacity = NumberUtils.sum(zoneEntry.getValue().stream().filter(o -> o.getSystemDiskNum() != null && o.getSystemDiskStorage() != null).collect(
                            Collectors.toList()),
                    o -> BigDecimal.valueOf(o.getInstanceNum()).multiply(BigDecimal.valueOf(o.getSystemDiskNum()))
                            .multiply(BigDecimal.valueOf(o.getSystemDiskStorage())));
            BigDecimal dataCapacity = NumberUtils.sum(zoneEntry.getValue().stream().filter(o -> o.getDataDiskNum() != null && o.getDataDiskStorage() != null).collect(
                            Collectors.toList()),
                    o -> BigDecimal.valueOf(o.getInstanceNum()).multiply(BigDecimal.valueOf(o.getDataDiskNum()))
                            .multiply(BigDecimal.valueOf(o.getDataDiskStorage())));
            String customhouseTitle = region2CustomhouseTitleMap.get(zoneEntry.getValue().get(0).getRegionName());
            if (customhouseTitle != null) {
                if (customhouseTitle.equals("境内")) {
                    if (systemCapacity.add(dataCapacity)
                            .compareTo(BigDecimal.valueOf(500).multiply(BigDecimal.valueOf(1024))) > 0) {
                        zoneEntry.getValue().forEach(o -> o.setCbsIsSpike(1));
                    }
                } else {
                    if (systemCapacity.add(dataCapacity)
                            .compareTo(BigDecimal.valueOf(100).multiply(BigDecimal.valueOf(1024))) > 0) {
                        zoneEntry.getValue().forEach(o -> o.setCbsIsSpike(1));
                    }
                }
            }else {
                if (systemCapacity.add(dataCapacity)
                        .compareTo(BigDecimal.valueOf(600).multiply(BigDecimal.valueOf(1024))) > 0) {
                    zoneEntry.getValue().forEach(o -> o.setCbsIsSpike(1));
                }
            }
        }
    }

    @Override
    @Transactional(value = "demandTransactionManager")
    public void refreshCbsSpike(String versionCode) {
        //找到当前版本下所有的group_item
        List<PplVersionGroupDO> all = demandDBHelper.getAll(PplVersionGroupDO.class,
                "where version_code = ? and product <> ?",
                versionCode, Ppl13weekProductTypeEnum.DATABASE.getName());
        List<PplVersionGroupRecordItemDO> itemList = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                "where version_group_id in (?)", all.stream().map(BaseDO::getId).collect(Collectors.toList()));
        Map<String, List<PplVersionGroupRecordItemDO>> mapList = ListUtils.toMapList(itemList, o -> String.valueOf(o.getVersionGroupId()), o -> o);
        for (Entry<String, List<PplVersionGroupRecordItemDO>> entry : mapList.entrySet()) {
            setThresholdsForCBS(entry.getValue());
        }
        Map<Integer, List<PplVersionGroupRecordItemDO>> group = itemList.stream()
                .collect(Collectors.groupingBy(PplVersionGroupRecordItemDO::getCbsIsSpike));
        group.forEach((k, v) -> {
            List<Long> ids = v.stream().map(PplVersionGroupRecordItemDO::getId).collect(Collectors.toList());
            demandDBHelper.executeRaw("update ppl_version_group_record_item set cbs_is_spike = ? "
                    + "where id in (?)", k, ids);
        });
    }

    @Override
    public void removeGroup(List<PplVersionGroupRecordWithGroupVO> list) {
        String userNameWithSystem = LoginUtils.getUserNameWithSystem();
        List<Long> groupIds = new ArrayList<>();
        for (PplVersionGroupRecordWithGroupVO groupVO : list) {
            PplVersionGroupDO group = groupVO.getGroupDO();
            String taskId = StringTools.join("-", group.getVersionCode(), group.getIndustryDept(),
                    group.getProduct(), group.getStatus(), groupVO.getId());
            finishTodo(userNameWithSystem, taskId, "");
            groupIds.add(group.getId());
        }
        if (!CollectionUtils.isEmpty(groupIds)) {
            demandDBHelper.executeRaw("update ppl_version_group set deleted = 1 where id in (?)", groupIds);
        }

    }

    /**
     * 审批通过时，将变化应用到item上
     */
    @Override
    public void applyToItem(PplVersionGroupInfoVO group) {
        PplVersionGroupRecordDO firstRecord = group.getFirstRecord();
        PplVersionGroupRecordDO lastRecord = group.getLastRecord();
        if (firstRecord == null || lastRecord == null) {
            log.error("groupId:{} has no record, fail to apply to items", group.getId());
            return;
        }
        if (Objects.equals(firstRecord.getId(), lastRecord.getId())) {
            log.error("groupId:{} has only one record, fail to apply to items", group.getId());
            return;
        }

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("industry_dept = ? ", group.getIndustryDept());
        if (group.getProduct().equals(Ppl13weekProductTypeEnum.BM.getName())) {
            whereSQL.and("product like '裸金属' or product like '%;裸金属%' or product like '%裸金属;%'");
        } else {
            whereSQL.and("product like ?", "%" + group.getProduct() + "%");
        }
        PplInnerProcessDO processDO = demandDBHelper.getOne(PplInnerProcessDO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        // 如果在行业审批流有配置，则不进行回刷， 如果没有，则代表需要回刷。
        Boolean isFlushData = processDO != null ? Boolean.FALSE : Boolean.TRUE;

        if (!isFlushData) {
            // 不需要回刷的分组 直接返回
            // 大人 时代变了， 运管干预的不需要更新到最新版了 23-11-28
            return;

        }

        // 查询两个版本的item进行比较，并应用到item中
        List<PplVersionGroupRecordItemDO> firstItems = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                "where version_group_record_id=?", firstRecord.getId());
        List<PplVersionGroupRecordItemDO> lastItems = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                "where version_group_record_id=?", lastRecord.getId());

        Map<String, PplVersionGroupRecordItemDO> firstItemMap = ListUtils.toMap(firstItems, o -> o.getPplId(), o -> o);
        Map<String, PplVersionGroupRecordItemDO> lastItemMap = ListUtils.toMap(lastItems, o -> o.getPplId(), o -> o);

        // 刷新是否被干预状态 不区分是否走行业内外流程
//        List<String> isComdList = lastItems.stream().filter(PplVersionGroupRecordItemDO::getIsComd)
//                .map(PplVersionGroupRecordItemDO::getPplId)
//                .filter(pplId -> !StringUtils.startsWith(pplId, "PE")) // PE单数据干预后不回刷到ppl_item
//                .collect(Collectors.toList());
//        if (ListUtils.isNotEmpty(isComdList)) {
//            demandDBHelper.executeRaw("update ppl_item set is_comd = 1 where deleted = 0 and ppl_id in (?)",
//                    isComdList);
//        }

        // 处理删除的，firstItems有而lastItems没有的
        for (PplVersionGroupRecordItemDO item : firstItems) {
            if (!lastItemMap.containsKey(item.getPplId())) {
                ChangeItemEventDTO event = new ChangeItemEventDTO();
                event.setOperateUser(LoginUtils.getUserName());
                event.setEventType(PplItemEventTypeEnum.PPL_VERSION_AUDIT.getCode());
                event.setEventName(group.getVersionTypeName() + "版本审批删除PPL");
                event.setEventContent("整条需求删除");
                event.setOrderType("VERSION");
                event.setOrderId(group.getVersionCode());
                pplItemService.deletePplId(item.getPplId(), event);
            }
        }

        // 注意【不能加需求】，所以这里没有新增的例子，所以这里不用处理新增的
        // 2022-12-02 可以加需求

        // 处理都有的，如果有编辑，比较之后有修改的才进行；如果没有编辑，盖章记录
        for (PplVersionGroupRecordItemDO newItem : lastItems) {
            if (StringUtils.startsWith(newItem.getPplId(), "PE")
                    || StringUtils.startsWith(newItem.getSourcePplId(), "PE")) {
                // PE单数据不回刷（版本关闭，更新"行业报备"的云运管干预部分，PE单是预测外预约生成的系统补充的ppl）
                // https://doc.weixin.qq.com/flowchart/f4_AQAAmwbNAMwIgLSPMLyQNm01fLOYN?scode=AJEAIQdfAAoY9cuUc3AQAAmwbNAMw
                continue;
            }
            PplVersionGroupRecordItemDO originItem = firstItemMap.get(newItem.getPplId());
            // 新增不需要判断是否需要回刷数据， 存在干预数据，确实需要回刷，我们只需要保证行业的数据别被覆盖了就可以。
            if (originItem == null) {
                // 那就是新增需求，是不应该存在的，这里打log
//                log.error("should not add ppl-item:{}", JSON.toJson(newItem));
//                continue;

                PplItemDO one = toItemDO(newItem);
                one.setStatus(PplItemStatusEnum.VALID.getCode());// 立即生效
                if (PplItemStatusEnum.APPLIED.getCode().equals(newItem.getStatus())) {
                    // 解决云运管干预为已预约状态后，版本结束时PPL预约状态不对的问题
                    one.setStatus(PplItemStatusEnum.APPLIED.getCode());
                }
                one.setPplOrder(newItem.getPplOrder());
                one.setPplId(newItem.getPplId());
                ChangeItemEventDTO changeItemEventDTO = new ChangeItemEventDTO();
                changeItemEventDTO.setEventName("PPL版本提交新增");
                changeItemEventDTO.setEventContent("新增整条");
                changeItemEventDTO.setEventType(PplItemEventTypeEnum.PPL_VERSION_INPUT.getCode());
                changeItemEventDTO.setOperateUser(LoginUtils.getUserName());
                pplItemService.insert(one, changeItemEventDTO);

            } else {
                Map<String, String> change = diffChange(originItem, newItem);

                ChangeItemEventDTO event = new ChangeItemEventDTO();
                event.setOperateUser(LoginUtils.getUserName());
                event.setEventType(PplItemEventTypeEnum.PPL_VERSION_AUDIT.getCode());
                event.setEventName(group.getVersionTypeName() + "审批编辑PPL");
                event.setEventContent(diffChangeToString(change));
                event.setOrderType("VERSION");
                event.setOrderId(group.getVersionCode());

                pplItemService.updatePplId(toItemDO(newItem), event);
            }
        }
    }

    private PplItemDO toItemDO(PplVersionGroupRecordItemDO item) {
        PplItemDO pplItemDO = new PplItemDO();
        pplItemDO.setStatus(PplItemStatusEnum.VALID.getCode());
        pplItemDO.setPplOrder(item.getPplOrder());
        pplItemDO.setPplId(item.getPplId());
        pplItemDO.setProduct(item.getProduct());
        pplItemDO.setDemandType(item.getDemandType());
        pplItemDO.setDemandScene(item.getDemandScene());
        pplItemDO.setProjectName(item.getProjectName());
        pplItemDO.setBillType(item.getBillType());
        pplItemDO.setWinRate(item.getWinRate());
        pplItemDO.setImportantDemand(item.getImportantDemand());
        pplItemDO.setBeginBuyDate(item.getBeginBuyDate());
        pplItemDO.setEndBuyDate(item.getEndBuyDate());
        pplItemDO.setBeginElasticDate(item.getBeginElasticDate());
        pplItemDO.setEndElasticDate(item.getEndElasticDate());
        pplItemDO.setNote(item.getNote());
        pplItemDO.setRegionName(item.getRegionName());
        pplItemDO.setZoneName(item.getZoneName());
        pplItemDO.setInstanceType(item.getInstanceType());
        pplItemDO.setInstanceModel(item.getInstanceModel());
        pplItemDO.setInstanceNum(item.getInstanceNum());
        pplItemDO.setAlternativeInstanceType(item.getAlternativeInstanceType());
        pplItemDO.setAffinityType(item.getAffinityType());
        pplItemDO.setAffinityValue(item.getAffinityValue());
        pplItemDO.setSystemDiskType(item.getSystemDiskType());
        pplItemDO.setSystemDiskStorage(item.getSystemDiskStorage());
        pplItemDO.setSystemDiskNum(item.getSystemDiskNum());
        pplItemDO.setDataDiskType(item.getDataDiskType());
        pplItemDO.setDataDiskStorage(item.getDataDiskStorage());
        pplItemDO.setDataDiskNum(item.getDataDiskNum());
        pplItemDO.setTotalCore(item.getTotalCore());
        pplItemDO.setTotalDisk(item.getTotalDisk());

        //gpu
        pplItemDO.setGpuProductType(item.getGpuProductType());
        pplItemDO.setGpuType(item.getGpuType());
        pplItemDO.setGpuNum(item.getGpuNum());
        pplItemDO.setIsAcceptAdjust(item.getIsAcceptAdjust());
        pplItemDO.setAcceptGpu(item.getAcceptGpu());
        pplItemDO.setTotalGpuNum(item.getTotalGpuNum());
        pplItemDO.setBizScene(item.getBizScene());
        pplItemDO.setBizDetail(item.getBizDetail());
        pplItemDO.setServiceTime(item.getServiceTime());
        pplItemDO.setIsComd(item.getIsComd());
//        pplItemDO.setSourcePplId(item.getSourcePplId());
        pplItemDO.setSaleDurationYear(item.getSaleDurationYear());
        pplItemDO.setBusinessCpq(item.getBusinessCpq());
        pplItemDO.setApplyDiscount(item.getApplyDiscount());
        pplItemDO.setPlacementGroup(item.getPlacementGroup());

        return pplItemDO;
    }


    private String diffChangeToString(Map<String, String> change) {
        if (change == null || change.isEmpty()) {
            return "没有调整";
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> e : change.entrySet()) {
            sb.append(e.getKey()).append(": ").append(e.getValue()).append(";\n");
        }
        return sb.toString();
    }

    /**
     * 比对两个item的变化，如果没有变化，返回空的map
     */
    @Override
    public Map<String, String> diffChange(PplVersionGroupRecordItemDO originItem, PplVersionGroupRecordItemDO newItem) {
        Map<String, String> map = new HashMap<>();

        // 只要itemDO的属性
        originItem = JSON.parse(JSON.toJson(originItem), PplVersionGroupRecordItemDO.class);
        newItem = JSON.parse(JSON.toJson(newItem), PplVersionGroupRecordItemDO.class);

        Map<String, Object> originMap = JSON.toMap(originItem);
        Map<String, Object> newMap = JSON.toMap(newItem);

        for (Map.Entry<String, Object> n : newMap.entrySet()) {
            String attr = n.getKey();
            // 这些属性不比较
            if (StringTools.isIn(attr, "id", "deleted", "createTime", "updateTime",
                    "versionGroupId", "recordVersion", "versionGroupRecordId", "pplOrder", "pplId", "status")) {
                continue;
            }

            Object originValue = originMap.get(attr);
            Object newValue = n.getValue();
            if (!Objects.equals(originValue, newValue)) {
                String attrName = recordItemColumnNameMap.get(attr);
                map.put(attrName == null ? attr : attrName, originValue + " -> " + newValue);
            }
        }

        return map;
    }

    private Map<String, String> recordItemColumnNameMap = new HashMap<String, String>() {{
        put("product", "产品");
        put("demandType", "需求类型");
        put("demandScene", "需求场景");
        put("projectName", "项目名称");
        put("billType", "计费模式");
        put("winRate", "赢率");
        put("beginBuyDate", "开始购买日期");
        put("endBuyDate", "结束购买日期");
        put("beginElasticDate", "弹性开始日期");
        put("endElasticDate", "弹性结束日期");
        put("note", "特殊备注说明");
        put("regionName", "地域");
        put("zoneName", "可用区");
        put("instanceType", "实例类型");
        put("instanceModel", "实例规格");
        put("instanceNum", "实例数量");
        put("totalCore", "实例总核心数");
        put("alternativeInstanceType", "可接受的其它实例类型");
        put("affinityType", "亲和度类型");
        put("affinityValue", "亲和度值");
        put("systemDiskType", "系统盘磁盘类型");
        put("systemDiskStorage", "系统盘磁盘容量");
        put("systemDiskNum", "系统盘磁盘块数");
        put("dataDiskType", "数据盘磁盘类型");
        put("dataDiskStorage", "数据盘磁盘容量");
        put("dataDiskNum", "数据盘磁盘块数");
    }};

    public PplVersionGroupRecordDO copyRecordToNextRecord(PplVersionGroupInfoVO group, String groupNextStatus) {
        Integer recordVersion = pplCommonService.getVersionGroupRecordVersion();

        PplVersionGroupRecordDO recordNew = new PplVersionGroupRecordDO();
        recordNew.setVersionGroupId(group.getId());
        recordNew.setRecordVersion(recordVersion);
        recordNew.setStatus(groupNextStatus);
        if (PplVersionGroupStatusEnum.DONE.getCode().equals(groupNextStatus)) {
            recordNew.setOperateUser("system");
            recordNew.setApproveTime(new Date());
            recordNew.setApproveResult(PplVersionGroupApproveResultEnum.PASS.getCode());
            recordNew.setApproveNote("流程结束");
        }
        demandDBHelper.insert(recordNew);

        // 复制上一个record的item
        PplVersionGroupRecordDO lastRecord = group.getLastRecord();
        if (lastRecord != null) {
            List<PplVersionGroupRecordItemDO> items = demandDBHelper.getAll(PplVersionGroupRecordItemDO.class,
                    "where version_group_record_id=?", lastRecord.getId());
            ListUtils.forEach(items, o -> {
                o.setId(null); // 清除id，相当于复制
                o.setCreateTime(null);
                o.setRecordVersion(recordVersion);
                o.setVersionGroupRecordId(recordNew.getId());
            });

            // 打标项目类型
            // 如果下一个状态是 云运管干预， 这里对项目类型进行打标
            // 2024年7月15号， 这里增加2个状态，录入中和等待需求录入，因为都是后台接口一次调用，性能不是问题
            if (Ppl13weekProductTypeEnum.CVM.getName().equals(group.getProduct())
                    && !Objects.equals(group.getIndustryDept(), IndustryDeptEnum.LONG_TAIL.getName())
                    && Lang.list(PplVersionGroupStatusEnum.IN_SUBMIT.getCode(),
                    PplVersionGroupStatusEnum.WAIT_INTERVENE.getCode(),
                    PplVersionGroupStatusEnum.COMD_INTERVENE.getCode()).contains(groupNextStatus)) {
                setThresholds(items);
            }

            //打标CBS项目类型
            if (!Ppl13weekProductTypeEnum.DATABASE.getName().equals(group.getProduct())) {
                setThresholdsForCBS(items);
            }

            demandDBHelper.insertBatchWithoutReturnId(items);
        } else {
            log.error("groupId:{} has no record, fail to generate next items", group.getId());
        }

        return recordNew;
    }

    /**
     * 状态流转【云运管干预的】的时候，进行打标
     *
     * @param pplItems items
     */
    @Override
    public void setThresholds(List<PplVersionGroupRecordItemDO> pplItems) {

        // 获取所有的 pplOrder
        Map<String, PplOrderDO> pplOrderMap = getAllPplOrder(pplItems);
        // 默认是常规项目
        pplItems.forEach((o) -> o.setIsSpike(0));

        // 31个大客户的不管什么机型和阈值，都直接是重点项目
        Set<String> headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();
        pplItems = ListUtils.filter(pplItems, (o) -> {
            PplOrderDO pplOrderDO = pplOrderMap.get(o.getPplOrder());
            assert pplOrderDO != null;
            boolean isHeadCustomer = headCustomerShortName.contains(pplOrderDO.getCustomerShortName());
            if (isHeadCustomer) {
                o.setIsSpike(1);
            }
            return !isHeadCustomer;
        });

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        pplItems = pplItems.stream().filter((o) -> {
                    boolean isBlackInstanceType = allBlackInstanceType.contains(o.getInstanceType());
                    if (isBlackInstanceType) {
                        o.setIsSpike(2);
                    }
                    return !isBlackInstanceType;
                })
                .collect(Collectors.toList());

        // 模型预测的都认为是非毛刺
        ListUtils.filter(pplItems, o -> isForecast(pplOrderMap.get(o.getPplOrder()).getCustomerShortName()))
                .forEach(o -> o.setIsSpike(0));

        // 剩余的再按照毛刺标准来算
        pplItems = ListUtils.filter(pplItems,
                o -> !isForecast(pplOrderMap.get(o.getPplOrder()).getCustomerShortName()));

        ForSpikeParams<PplVersionGroupRecordItemDO> forSpikeParams = new ForSpikeParams<>();
        forSpikeParams.setMatchConfigFunc((items, config) -> {
            BigDecimal sum = NumberUtils.sum(items, PplVersionGroupRecordItemDO::getTotalCore);
            if (sum.compareTo(BigDecimal.valueOf(config.getThreshold())) >= 0) {
                items.forEach((o) -> o.setIsSpike(1));
                return true;
            }
            return false;
        });
        forSpikeParams.setData(pplItems);

        final Map<String, String> region2CustomhouseTitleMap = getRegion2CustomhouseTitleMap();

        forSpikeParams.setRegionNameFunc(PplVersionGroupRecordItemDO::getRegionName);
        forSpikeParams.setCustomhouseTitleFunc((o) -> region2CustomhouseTitleMap.getOrDefault(o.getRegionName(), ""));
        forSpikeParams.setInstanceTypeFunc(
                o -> forSpikeParams.getInstanceTypeToGroup().getOrDefault(o.getInstanceType(), o.getInstanceType()));
        forSpikeParams.setOtherFunc((o) -> {
                    PplOrderDO pplOrderDO = pplOrderMap.get(o.getPplOrder());
                    assert pplOrderDO != null;
                    return Strings.join("@",
                            o.getBeginBuyDate().getYear(),
                            o.getBeginBuyDate().getMonthValue(),
                            Strings.isBlank(pplOrderDO.getCustomerShortName())
                                    ? pplOrderDO.getCustomerUin() : pplOrderDO.getCustomerShortName(),
                            PplDemandTypeEnum.RETURN.getCode().equals(o.getDemandType())
                                    ? o.getDemandType() : PplDemandTypeEnum.NEW.getCode());
                }
        );
        Ppl13WeekInputServiceImpl.applyConfig(forSpikeParams);
    }

    private Map<String, PplOrderDO> getAllPplOrder(List<PplVersionGroupRecordItemDO> pplItems) {
        List<String> allPplOrderId = pplItems.stream()
                .map(PplVersionGroupRecordItemDO::getPplOrder)
                .collect(Collectors.toList());
        List<PplOrderDO> allPplOrder = demandDBHelper.getAll(PplOrderDO.class, "where ppl_order in (?)", allPplOrderId);
        return ListUtils.toMap(allPplOrder, PplOrderBaseDO::getPplOrder, Function.identity());
    }


    private void updateGroupRecordStatus(PplVersionGroupInfoVO group, PplVersionGroupApproveResultEnum approveResult,
            String approveNote, String groupNextStatus, String currentProcessor, boolean isSystem) {
        group.setStatus(groupNextStatus);
        group.setCurrentProcessor(currentProcessor);
        demandDBHelper.update(group);

        PplVersionGroupRecordDO lastRecord = group.getLastRecord();
        if (lastRecord != null) {
            //如果是系统级调用则获取带系统的用户名查询；反之不带。
            lastRecord.setOperateUser(isSystem ? LoginUtils.getUserNameWithSystem() : LoginUtils.getUserName());
            lastRecord.setApproveTime(new Date());
            lastRecord.setApproveResult(approveResult.getCode());
            lastRecord.setApproveNote(approveNote);
            demandDBHelper.update(lastRecord);
        } else {
            log.error("groupId:{} has no record, fail to change status", group.getId());
        }
    }

    private PplVersionGroupInfoVO getGroup(Long groupId) {
        if (groupId == null) {
            throw new WrongWebParameterException("缺少分组ID[groupId]参数");
        }
        PplVersionGroupInfoVO vo = demandDBHelper.getByKey(PplVersionGroupInfoVO.class, groupId);
        if (vo == null) {
            throw new WrongWebParameterException("分组ID:" + groupId + "不存在");
        }
        return vo;
    }


    private List<PplCvmImportExcelDTO> decodeExcel(MultipartFile file) {
        List<PplCvmImportExcelDTO> data = new LinkedList<>();
        try {
            EasyExcel.read(
                    file.getInputStream(), PplCvmImportExcelDTO.class,
                    new AnalysisEventListener<PplCvmImportExcelDTO>() {
                        @Override
                        public void invoke(PplCvmImportExcelDTO o, AnalysisContext analysisContext) {
                            if (ObjUtils.allFieldIsNull(o)) {
                                log.info("读到第一个空行，结束");
                                throw new ExcelAnalysisStopException();
                            }
                            PplCvmImportExcelDTO i = PplCvmImportExcelDTO.copy(o);
                            data.add(i);
                        }

                        @Override
                        public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                        }
                    }
            ).sheet(0).headRowNumber(2).doRead();
        } catch (Exception e) {
            log.error("decode excel error:", e);
            throw new BizException("文件解析失败");
        }
        return data;
    }


    @Override
    @SneakyThrows
    public void getNoSuccessUin() {

        ArrayList<String> uins = Lang.list("25848616", "100008530154", "100002676981", "100009757294", "100013651415",
                "100028346305", "478617351", "2202439991", "200019689895", "3205597606", "100007145067", "100012841146",
                "438167613", "100010224120", "2977354942", "2841721246", "200026321895", "2776458258", "2185972708",
                "200028131598", "100027399511", "100026565921", "100012374837", "200027936018", "200027829529",
                "100014288834", "100014379989", "100005643178", "100014780699", "2409831278", "100022853233",
                "100023908185", "100018238576", "100012381166", "2285267934", "100025867287", " 100001242037",
                "100001489235", "1265315895", "3195726362", "200026401228", "2597885706", "1794844604", "1015979028",
                "100020812627", "100027013925", "100000462694", "3095929208", "2880338630", "1684117838", "1601048706",
                "1396093318", "100025192524", "100000937037", "1412648152", "200027960067", "200026321892",
                "100013165318", "100023016323", "100010627166", "2905526316", "100015874355", "100001242037",
                "100021310295", "3020991488", "100019967849", "100000460815", "100009420779", "100000789856",
                "3302623583", "100011073618", "200027197056", "100019406079", "200021271193", "100026957613",
                "100006700735", "100016453802", "100015349336", "100015349391", "100015444031", "2870624074",
                "100005900303", "100019268488", "100026492949", "1927997462", "100003399065", "200017476569",
                "100026008576", "100027292109", "1654437368", "2047116", "100020510489", "100000622510", "200027271251",
                "200025230521", "200025930368", "100013425031", "100027000639", "100010528933", "2637337100",
                "860238188", "100019791619", "100013549672", "100013621339", "100013095145", "100024968528",
                "100026321862", "188031758", "200023400201", "200022207533", "2016612676", "3212825840", "100013433222",
                "200021945871", "100000062750", "3401337927", "100026696949", "100027173915", "100025072126",
                "3329997281", "200016255503", "100012634872", "100013381490", "100025574623", "477727565", "770967167",
                "3042131761", "200022262001", "1410592331", "100011600420", "100009955362", "100023822808",
                "3390191309", "200024528679", "200025187395", "200025189276", "2485625721", "1634563", "200022239971",
                "200022822813", "100016807662", "3320903430", "2393504171", "100000806847", "2850708091",
                "100017677525", "100027067938", "2462949865", "100016952586", "2097472548", "100013915254",
                "1527398214", "200021733942", "100026425036", "100019932767", "200025299581", "100026403115",
                "100020240914", "3446455389", "200021730342", "3103261434", "100011600472", "3473200091", "2290317848",
                "100007704184", "100013483657", "2793132804", "100018143029", "2678475016", "100025301886",
                "100009495213", "200019192759", "200026504182", "200024086597", "200018500527", "100004546815",
                "100012646329", "100023843007", "100013645153", "200020202428", "100002759547", "200023944891",
                "100018492051", "342151535", "100006545568", "212642308", "100010109505", "100016800489",
                "100012150916", "100001479072", "2355375704", "100011600514", "42212758", "100024553828",
                "100013439583", "100004338880", "100001126453", "100025302248", "2930784446", "248546916",
                "100025085402", "3179339950", "3537736185", "200025283322", "100019618168", "100019718816",
                "100003069518", "2559440788", "200020310383", "100016852347", "2597674310", "100025604004",
                "100000997578", "100011974129", "100008561593", "100017956248", "100018189745", "200019599942",
                "3428215633", "200016623586", "100004651808", "3287248762", "100006386161", "3107482090",
                "100006283580", "100019047070", "100000016727", "100009636310", "100001509447", "100012644465",
                "200020746802", "200025259254", "100001559531", "100001044169", "1344047294", "100019033611",
                "100022214952", "100023929271", "100021965045", "100000060144", "100001995278", "100024094136",
                "100023297601", "100017014509", "100018535504", "200019919216", "29862529", "100001475605",
                "100022873236", "100021192862", "100008650820", "100024535935", "100015915918", "2914025727",
                "100005116839", "100007062829", "3331501460", "1502125473", "100021188957", "100023252756",
                "200023455982", "100005998566", "100019623328", "200022823574", "100011957817", "100015724999",
                "2710703044", "100003972631", "100003079716", "100012602116", "3502873056", "100001540306", "69016538",
                "100020471825", "565093963", "100008632269", "100019921921", "3289682752", "100020418923",
                "100021271977", "100000062748", "100010716916", "100022369637", "2355741710", "100009796914",
                "100001272463", "100012419788", "285136806", "100019391216", "100010274760", "200027322261",
                "1521488775", "100021396676", "1257535774", "100000918116", "10808", "100013961083", "100016095832",
                "100009507343", "100017474801", "200028707556", "100018959846", "100017409990", "100020781646",
                "100020290737", "100019731037", "100027784384", "1255928432", "100007977958", "100018999837",
                "3490666271", "100009491134", "100024853949", "100026974407", "100009069548", "100020183819",
                "100006343061", "100022193641", "100022882018", "2531886716", "425896663", "100000853831", "19569319",
                "100024527355", "100000455537", "100017652387", "100028597561", "2994353596", "100025081193",
                "100028214474", "100011111966", "100027507298", "100005662505", "100027269538", "200020012014",
                "200026796917", "200019621815");

        ArrayList<String> list = Lang.list(" 100001242037", "1255928432");

        ArrayList<Object> noSuccess = Lang.list();
        for (String key : list) {

            QueryInfoByUinRsp queryInfoByUinRsp = pplDictService.queryInfoByUin(key);

            if (!queryInfoByUinRsp.getIsExist()) {
                log.info(" uin 不合法： {}", key);
                noSuccess.add(key);
            }
//            Thread.sleep(50);
        }
        System.out.println("over");
        System.out.println(noSuccess);

    }


    private <K> ExcelProperty getEP(IGetter<K> fn) {
        return ORMUtils.getAnnotationByGetter(ExcelProperty.class, fn);
    }

    private <K> String getColName(IGetter<K> fn) {
        return getEP(fn).value()[0];
    }

    private <K> int getColIndex(IGetter<K> fn) {
        return getEP(fn).index() + 1;
    }


    public <K> PplItemImportRsp.ErrorMessage makeError(int raw, IGetter<K> fn, String message) {
        return new PplItemImportRsp.ErrorMessage(raw, getColIndex(fn), getColName(fn), message);
    }


    static ExecutorService executor = Executors.newFixedThreadPool(10);

    @SneakyThrows
    @Override
    public PplItemImportRsp uploadPplItemDetailExcel(MultipartFile file,
            YearMonth startYearMonth, YearMonth endYearMonth, String product) {

        List<PplCvmImportExcelDTO> uploadData = decodeExcel(file);

        List<String> uins = uploadData.stream()
                .map(PplCvmImportExcelDTO::getCustomerUin)
                .filter(Strings::isNotBlank)
                .distinct().collect(Collectors.toList());

        List<Future<Tuple2<String, QueryInfoByUinRsp>>> futures = new ArrayList<>();
        for (String uin : uins) {
            Future<Tuple2<String, QueryInfoByUinRsp>> future = executor.submit(
                    () -> Tuple.of(uin, pplDictService.queryInfoByUin(uin)));
            futures.add(future);
        }
        HashMap<String, QueryInfoByUinRsp> uinMapInfo = new HashMap<>();
        // 遍历 future 对象列表，检查每个任务是否执行成功
        long startTmp = System.currentTimeMillis();
        for (Future<Tuple2<String, QueryInfoByUinRsp>> future : futures) {
            Tuple2<String, QueryInfoByUinRsp> one = future.get();
            uinMapInfo.put(one._1, one._2);
        }
        log.info("excel time {}: {} : {}", -1, System.currentTimeMillis() - startTmp, "allUin");

        List<GroupItemDTO> retData = Lang.list();
        List<PplItemImportRsp.ErrorMessage> errors = new MyList<>();

        for (int i = 0; i < uploadData.size(); i++) {
            PplCvmImportExcelDTO oneData = uploadData.get(i);
            int row = i + 3;
            int beginErrorSize = errors.size();
            PplItemImportRsp.ErrorMessage error = null;

            int year = 0, month = 0;
            String yearMonth = null;
            BigDecimal winRate = null;
            QueryInfoByUinRsp queryInfoByUinRsp = null;
            // 购买退回日期、弹性时间
            LocalDate beginBuyDateRet = null;
            LocalDate endBuyDateRet = null;
            LocalTime beginElasticDateRet = null;
            LocalTime endElasticDateRet = null;
            int totalCore = 0, totalDisk = 0;
            BigDecimal affinityValue = null;

            error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getCustomerTypeName,
                    CustomerTypeEnum.names());
            errors.add(error);
            CustomerTypeEnum customerTypeEnum = CustomerTypeEnum.getByName(oneData.getCustomerTypeName());

            long start;

            start = System.currentTimeMillis();
            if (customerTypeEnum != null) {
                if (customerTypeEnum == CustomerTypeEnum.EXISTING) {

                    queryInfoByUinRsp = uinMapInfo.get(oneData.getCustomerUin());
                    if (queryInfoByUinRsp == null) {
                        queryInfoByUinRsp = pplDictService.queryInfoByUin(oneData.getCustomerUin());
                    }

                    if (!queryInfoByUinRsp.getIsExist() && !oneData.getDemandTypeName()
                            .equals(PplDemandTypeEnum.RETURN.getName())) {
                        error = makeError(row, PplCvmImportExcelDTO::getCustomerUin,
                                "没有找到uin: " + oneData.getCustomerUin());
                        errors.add(error);
                    }
                } else {
                    String customerShortName = oneData.getCustomerShortName();
                    queryInfoByUinRsp = uinMapInfo.get(oneData.getCustomerUin());
                    if (queryInfoByUinRsp == null) {
                        queryInfoByUinRsp = pplDictService.queryInfoByUin(oneData.getCustomerUin());
                    }

                    if (Strings.isNotBlank(oneData.getCustomerUin())) {
                        if (!queryInfoByUinRsp.getIsExist()) {
                            error = makeError(row, PplCvmImportExcelDTO::getCustomerUin, "Uin 不合法");
                            errors.add(error);
                        }
                    }

                    if (!queryInfoByUinRsp.getIsExist() && Strings.isBlank(customerShortName)) {
                        error = makeError(row, PplCvmImportExcelDTO::getCustomerUin, "Uin 不合法 或者 客户简称为空");
                        errors.add(error);
                    }
                }
            }
            log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryInfoByUin");

            error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getDemandTypeName,
                    PplDemandTypeEnum.names());
            errors.add(error);

            start = System.currentTimeMillis();
            PplDemandTypeEnum demandTypeEnum = PplDemandTypeEnum.getByName(oneData.getDemandTypeName());
            if (demandTypeEnum != null && customerTypeEnum != null) {
                List<String> demandScenes = pplDictService.queryDemandScene(customerTypeEnum.getCode(),
                        demandTypeEnum.getCode());
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getDemandScene, demandScenes);
                errors.add(error);
            }
            log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryDemandScene");

            start = System.currentTimeMillis();
            if (demandTypeEnum != null && demandTypeEnum != PplDemandTypeEnum.RETURN) {
                List<String> billTypes = pplDictService.queryBillType(demandTypeEnum);
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getBillType, billTypes);
                errors.add(error);
            }
            log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryBillType");

            // 非空校验
            if (Strings.isNotBlank(oneData.getWinRate())) {
                // 包含在 0 - 100 中
                List<String> winRateRange = Lang.list();
                for (int cnt = 0; cnt <= 100; cnt++) {
                    winRateRange.add(cnt + "%");
                }
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getWinRate, winRateRange);
                errors.add(error);
                String replace = oneData.getWinRate().replace('%', ' ').trim();
                winRate = NumberUtils.parseBigDecimal(replace);
            }

            boolean beginSuccess = false;
            String beginBuyDate = oneData.getBeginBuyDate();
            error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getBeginBuyDate);
            if (error == null) {
                LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
                if (localDate == null) {
                    error = makeError(row, PplCvmImportExcelDTO::getBeginBuyDate, "开始日期解析出错");
                    errors.add(error);
                } else {
                    beginBuyDateRet = localDate;
                    year = localDate.getYear();
                    month = localDate.getMonth().getValue();

                    yearMonth = com.pugwoo.wooutils.lang.DateUtils.format(localDate, "yyyy-MM");
                    if (startYearMonth.getYear() > year ||
                            (startYearMonth.getYear() == year && startYearMonth.getMonth() > month)) {
                        String info = "开始日期:" + yearMonth + "  <  版本开始日期:" + startYearMonth.toDateStr();
                        error = makeError(row, PplCvmImportExcelDTO::getBeginBuyDate, info);
                        errors.add(error);
                    }
//                    2023-2-8  允许录入版本时间外的ppl
//                    if (endYearMonth.getYear() < year ||
//                            (endYearMonth.getYear() == year && endYearMonth.getMonth() < month)) {
//                        String info = "开始日期:" + yearMonth + "  >  版本结束日期:" + endYearMonth.toDateStr();
//                        error = makeError(row, PplItemImportExcelDTO::getBeginBuyDate, info);
//                        errors.add(error);
//                    }
                    beginSuccess = true;
                }
            }

            String endBuyDate = oneData.getEndBuyDate();
            error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getEndBuyDate);
            if (error == null) {
                LocalDate localDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(endBuyDate);
                endBuyDateRet = localDate;
                if (localDate == null) {
                    error = makeError(row, PplCvmImportExcelDTO::getEndBuyDate, "结束日期解析出错");
                    errors.add(error);
                } else {
                    if (beginSuccess) {
                        LocalDate beginLocalDate = com.pugwoo.wooutils.lang.DateUtils.parseLocalDate(beginBuyDate);
                        if (beginLocalDate.compareTo(localDate) > 0) {
                            error = makeError(row, PplCvmImportExcelDTO::getEndBuyDate, "结束日期比开始日期小");
                            errors.add(error);
                        }

                        if (Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.NEW.getName())
                                || Strings.equals(oneData.getDemandTypeName(), PplDemandTypeEnum.RETURN.getName())) {
//                            https://zhiyan.woa.com/requirement/6756/story/#/cloudrm-1186?tab=info&story_tab=info

                            Period between = Period.between(localDate.withDayOfMonth(1),
                                    beginLocalDate.withDayOfMonth(1));
                            if (Math.abs(between.getMonths()) > 1) {
                                error = makeError(row, PplCvmImportExcelDTO::getEndBuyDate,
                                        "新增需求/退回需求的月份差值大于1： 结束购买日期年月 - 开始购买日期年月 > 1");
                                errors.add(error);
                            }
//                            if (localDate.minusDays(14).compareTo(beginLocalDate) >= 0) {
//                                error = makeError(row, PplItemImportExcelDTO::getEndBuyDate,
//                                        "新增需求： 结束日期 - 14 >= 开始日期， 开始结束相距不可以超过14天");
//                                errors.add(error);
//                            }
                        }
                    }
                }
            }

            if (demandTypeEnum != null) {
                if (demandTypeEnum == PplDemandTypeEnum.ELASTIC) {
                    error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getBeginElasticDate);
                    errors.add(error);
                    error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getEndElasticDate);
                    errors.add(error);
                    String beginElasticDate = oneData.getBeginElasticDate();
                    LocalTime localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(beginElasticDate);
                    beginElasticDateRet = localTime;
                    if (localTime == null) {
                        error = makeError(row, PplCvmImportExcelDTO::getBeginElasticDate, "弹性开始日期解析出错");
                        errors.add(error);
                    }
                    String endElasticDate = oneData.getEndElasticDate();
                    localTime = com.pugwoo.wooutils.lang.DateUtils.parseLocalTime(endElasticDate);
                    endElasticDateRet = localTime;
                    if (localTime == null) {
                        error = makeError(row, PplCvmImportExcelDTO::getEndElasticDate, "弹性结束日期解析出错");
                        errors.add(error);
                    }
                }
            }

            start = System.currentTimeMillis();
            List<String> citys = pplDictService.queryAllCityName();
            log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryAllCityName");
            citys.add("随机");
            error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getRegionName, citys);
            errors.add(error);
            if (error == null) {
                start = System.currentTimeMillis();
                List<String> zones = pplDictService.queryAllZoneName(oneData.getRegionName());
                log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryAllZoneName");
                zones.add("随机");
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getZoneName, zones);
                errors.add(error);
            }

            Integer coreNum = NumberUtils.parseInt(oneData.getInstanceModelCpuCore());
            Integer ramNum = NumberUtils.parseInt(oneData.getInstanceModelRam());
            boolean isRandomZone =
                    Strings.equals(oneData.getZoneName(), "随机")
                            || Strings.equals(oneData.getZoneName(), "随机可用区");
            start = System.currentTimeMillis();
//            List<String> types = pplDictService.queryInstanceType(isRandomZone ? "" : oneData.getZoneName());
            List<String> types;

            if (isRandomZone) {
                types = pplDictService.queryInstanceType("");
            } else {
                types = pplDictService.queryZoneInstanceTypeMap().getOrDefault(oneData.getZoneName(), Lang.list());
            }

            log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryInstanceType");
            String instanceType = oneData.getInstanceType();
            if (Strings.isNotBlank(instanceType)) {
                instanceType = instanceType.toUpperCase();
            }
            oneData.setInstanceType(instanceType);

            // 空的报错
            error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getInstanceType);
            errors.add(error);
            boolean findOne = false;
            for (String type : types) {
                if (Strings.equalsIgnoreCase(type, instanceType)) {
                    findOne = true;
                    oneData.setInstanceType(type);
                    break;
                }
            }
            if (!findOne) {
                if (Lang.isEmpty(types)) {
                    errors.add(makeError(row, PplCvmImportExcelDTO::getInstanceType,
                            oneData.getZoneName() + " 可用区不存在可选实例类型"));
                } else {
                    errors.add(makeError(row, PplCvmImportExcelDTO::getInstanceType,
                            "实例类型不在下列范围内(由可用区筛选)： " + types));
                }
            }

            if (error == null && findOne) {

                error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getInstanceModelCpuCore);
                errors.add(error);
                error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getInstanceModelRam);
                errors.add(error);

                if (coreNum == null) {
                    errors.add(makeError(row, PplCvmImportExcelDTO::getInstanceModelCpuCore, "解析cpu核心数出错"));
                }
                if (ramNum == null) {
                    errors.add(makeError(row, PplCvmImportExcelDTO::getInstanceModelRam, "解析内存数出错"));
                }

                if (coreNum != null && ramNum != null) {

                    Integer num = NumberUtils.parseInt(oneData.getInstanceNum());
                    totalCore = coreNum * (num == null ? 0 : num);
                    start = System.currentTimeMillis();
//                    List<InstanceModelInfoDTO> instanceModelInfos = pplDictService.queryInstanceModelInfo(
//                            isRandomZone ? "" : oneData.getZoneName(),
//                            oneData.getInstanceType());
                    List<InstanceModelInfoDTO> instanceModelInfos;

                    if (isRandomZone) {
                        Map<String, List<InstanceModelInfoDTO>> type2InstanceModelInfoMap =
                                pplDictService.queryType2InstanceModelInfoMap();
                        instanceModelInfos = type2InstanceModelInfoMap.getOrDefault(oneData.getInstanceType(),
                                Lang.list());
                    } else {
                        Map<Tuple2<String, String>, List<InstanceModelInfoDTO>> zoneNameType2InstanceModelInfoMap =
                                pplDictService.queryZoneNameType2InstanceModelInfoMap();
                        instanceModelInfos = zoneNameType2InstanceModelInfoMap.getOrDefault(
                                Tuple.of(oneData.getZoneName(), oneData.getInstanceType()), Lang.list());
                    }

                    log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryInstanceModelInfo");

                    if (Lang.isNotEmpty(instanceModelInfos)) {
                        instanceModelInfos = instanceModelInfos.stream().distinct().collect(Collectors.toList());
                    }

                    List<InstanceModelInfoDTO> filter = ListUtils.filter(instanceModelInfos,
                            (o) -> Strings.isNotBlank(o.getInstanceModel())
                                    && Objects.equals(o.getCoreNum(), coreNum)
                                    && Objects.equals(o.getRamNum(), ramNum));
                    if (Lang.isEmpty(filter)) {

                        List<Tuple2<Integer, Integer>> collect = instanceModelInfos.stream()
                                .map((o) -> Tuple.of(o.getCoreNum(), o.getRamNum())).distinct()
                                .collect(Collectors.toList());
                        String info = "没有找到对应核心和内存的实例规格,可选范围 [(CPU核心，内存GB)]：" + collect;
                        errors.add(makeError(row, PplCvmImportExcelDTO::getInstanceModel, info));
                    } else {
                        oneData.setInstanceModel(filter.get(0).getInstanceModel());
                    }
                }
            }

            if (Strings.isNotBlank(oneData.getAffinityType())) {
                List<String> tmpAff = Lang.list("母机", "交换机", "机柜");
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getAffinityType, tmpAff);
                errors.add(error);
            }

            if (Strings.isNotBlank(oneData.getAffinityValue())) {
                affinityValue = NumberUtils.parseBigDecimal(oneData.getAffinityValue());
                if (affinityValue == null) {
                    error = makeError(row, PplCvmImportExcelDTO::getAffinityValue,
                            "解析数字出错： " + oneData.getAffinityValue());
                    errors.add(error);
                }
            }
            List<String> systemDiskTypes = PplDiskTypeEnum.systemDiskNameList;
            List<String> dataDiskTypes = PplDiskTypeEnum.dataDiskNameList;

            if (Strings.isNotBlank(oneData.getDataDiskType())) {

                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getDataDiskType, dataDiskTypes);
                errors.add(error);

                if (Strings.isNotBlank(oneData.getDataDiskStorage())) {
                    error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getDataDiskStorage);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getDataDiskStorage());
                        if (integer == null) {
                            error = makeError(row, PplCvmImportExcelDTO::getDataDiskStorage,
                                    "解析数字出错： " + oneData.getDataDiskStorage());
                        }
                    }
                    errors.add(error);
                }

                if (Strings.isNotBlank(oneData.getDataDiskNum())) {
                    error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getDataDiskNum);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getDataDiskNum());
                        if (integer == null) {
                            error = makeError(row, PplCvmImportExcelDTO::getDataDiskNum,
                                    "解析数字出错： " + oneData.getDataDiskNum());
                        }
                    }
                    errors.add(error);
                }

            }
            if (Strings.isNotBlank(oneData.getSystemDiskType())) {
                error = oneData.makeErrorIfNotContain(row, PplCvmImportExcelDTO::getSystemDiskType, systemDiskTypes);
                errors.add(error);

                if (Strings.isNotBlank(oneData.getSystemDiskStorage())) {
                    error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getSystemDiskStorage);
                    if (error == null) {
                        Integer integer = NumberUtils.parseInt(oneData.getSystemDiskStorage());
                        if (integer == null) {
                            error = makeError(row, PplCvmImportExcelDTO::getSystemDiskStorage,
                                    "解析数字出错： " + oneData.getSystemDiskStorage());
                        }
                    }
                    errors.add(error);
                }
            }

            error = oneData.makeErrorIfBlank(row, PplCvmImportExcelDTO::getInstanceNum);
            if (error == null) {
                Integer integer = NumberUtils.parseInt(oneData.getInstanceNum());
                if (integer == null) {
                    error = makeError(row, PplCvmImportExcelDTO::getInstanceNum,
                            "解析数字出错： " + oneData.getInstanceNum());
                }
            }
            errors.add(error);

            List<String> alternativeInstances = Lang.list();
            if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
//                List<String> strings = pplDictService.queryMainTyeInstanceModel(oneData.getZoneName());
                String alterInstance = oneData.getAlternativeInstanceType();
                if (Strings.isNotBlank(alterInstance)) {
                    List<String> list = Lang.list(alterInstance.split(";"));
                    alternativeInstances = list;
                }
            }

            Integer systemStorage = NumberUtils.parseInt(oneData.getSystemDiskStorage());
            Integer diskNum = NumberUtils.parseInt(oneData.getDataDiskNum());
            Integer diskStorage = NumberUtils.parseInt(oneData.getDataDiskStorage());

            int oneDiskSize = 0;
            if (systemStorage != null) {
                oneDiskSize += systemStorage;
            }
            if (diskNum != null && diskStorage != null) {
                oneDiskSize += diskNum * diskStorage;
            }
            Integer num = NumberUtils.parseInt(oneData.getInstanceNum());
            totalDisk = oneDiskSize * (num == null ? 0 : num);

            if (beginErrorSize == errors.size()) {

                GroupItemDTO tmp = GroupItemDTO.trans(oneData);
                retData.add(tmp);
                if (customerTypeEnum != null) {
                    tmp.setCustomerType(customerTypeEnum.getCode());
                }
                tmp.setYear(year);
                tmp.setMonth(month);
                tmp.setYearMonth(yearMonth);
                tmp.setCustomerShortName(oneData.getCustomerShortName());
                if (queryInfoByUinRsp != null) {
                    if (Strings.isNotBlank(queryInfoByUinRsp.getCustomerShortName())) {
                        tmp.setCustomerShortName(queryInfoByUinRsp.getCustomerShortName());
                    }
                    if (Strings.isBlank(tmp.getCustomerShortName())) {
                        tmp.setCustomerTypeName(tmp.getCustomerName());
                    }
                    tmp.setIndustry(queryInfoByUinRsp.getIndustry());
                    tmp.setCustomerSource(queryInfoByUinRsp.getCustomerSource());
                    tmp.setWarZone(queryInfoByUinRsp.getWarZone());
                    tmp.setCustomerName(queryInfoByUinRsp.getCustomerName());
                }
                tmp.setWinRate(winRate);
                tmp.setBeginBuyDate(beginBuyDateRet);
                tmp.setEndBuyDate(endBuyDateRet);
                tmp.setEndElasticDate(endElasticDateRet);
                tmp.setBeginElasticDate(beginElasticDateRet);
                tmp.setTotalCoreNum(totalCore);
                tmp.setTotalDiskNum(totalDisk);
                tmp.setInstanceModelCoreNum(coreNum);
                tmp.setInstanceModelRamNum(ramNum);
                tmp.setStatus(PplItemStatusEnum.VERSION_IMPORT.getCode());
                tmp.setStatusName(PplItemStatusEnum.VERSION_IMPORT.getName());
                tmp.setAffinityValue(NumberUtils.parseBigDecimal(oneData.getAffinityValue()));
                if (Strings.equals(oneData.getIsAcceptAlternative(), "是")) {
                    tmp.setAlternativeInstanceType(alternativeInstances);
                }
                tmp.setAffinityValue(affinityValue);
                tmp.setProduct(product);

                // 2022-12-13 增加推荐机型
                start = System.currentTimeMillis();
                List<String> mainInstanceTypes = pplDictService.queryMainInstanceType(tmp.getZoneName());
                log.info("excel time {}: {} : {}", i, System.currentTimeMillis() - start, "queryMainInstanceType");

                boolean isMainInstanceType = mainInstanceTypes.contains(tmp.getInstanceType());
                tmp.setIsRecommendedInstanceType(isMainInstanceType);


            }
        }

        return new PplItemImportRsp(errors.size() == 0, errors, retData);
    }

    @Override
    public FileNameAndBytesDTO exportPplVersionItemExcel(ExportPplVersionItemReq req) {

//        String templateName = "ppl13week_version_import.xlsx";
        InputStream templateIn = IOUtils.readClasspathResourceInputStream("excel/" + req.getExcelTemplateName());

        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        Map<String, Collection<DataWrapper>> dataWrapperMap = dropMapUtil.dataWrapperMap(groupDO.getProduct());

        HashMap<String, Integer> config = new HashMap<>();

        // 如果是横向的，加上这个
//        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();

        List data = new ArrayList<>();
        List dictData = new ArrayList();

        // 获取关联的审批版本信息
        List<Long> innerVersionIds = req.getData().stream().map(GroupItemDTO::getInnerVersionId)
                .filter(e -> e != 0).collect(Collectors.toList());
        Map<Long, PplInnerProcessVersionDO> innerProcessVersionDOMap = new HashMap<>();
        if (ListUtils.isNotEmpty(innerVersionIds)) {
            innerProcessVersionDOMap = demandDBHelper.getAll(PplInnerProcessVersionDO.class, "where id in(?) ",
                            innerVersionIds).stream()
                    .collect(Collectors.toMap(PplInnerProcessVersionDO::getId, v -> v, (v1, v2) -> v2));
        }

        if (req.getProduct().equals(Ppl13weekProductTypeEnum.GPU.getName())) {
            data = PplGpuImportExcelDTO.transFrom(req.getData(), innerProcessVersionDOMap);
            dictData.addAll(getGpuDictData(config));
        } else {
            data = PplItemImportExcelNumberExportDTO.transFrom(req.getData(), innerProcessVersionDOMap);
            dictData.addAll(getDictData(config));
        }
        ExcelWriter excelWriter = EasyExcel.write(out)
                .registerWriteHandler(new PplExportWriteHandler(config))
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        WriteSheet dictSheet = EasyExcel.writerSheet("字典").build();

        excelWriter.
                fill(new FillWrapper("item", data), writeSheet)
                .write(dictData, dictSheet)
                .finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                req.getFileName() + com.pugwoo.wooutils.lang.DateUtils.format(new Date(), "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return fileNameAndBytesDTO;

    }


    ExecutorService executorService = Executors.newFixedThreadPool(5);


    @SneakyThrows
    private List<PplCvmImportExcelDTO> getDictData(HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";

        Future<List<String>> billTypeFuture = executorService.submit(() -> {
            return pplDictService.queryBillType();
        });
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return pplDictService.queryAllCityName(null);
        });
        Future<List<IndustryDemandRegionZoneInstanceTypeDictDO>> zonesFuture = executorService.submit(() -> {
            return pplDictService.queryAllZoneName();
        });
        Future<List<String>> instanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceType("");
        });
        Future<List<String>> instanceModelFuture = executorService.submit(() -> {
            return pplDictService.queryInstanceModel(null, null);
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });

        List<IndustryDemandRegionZoneInstanceTypeDictDO> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> billType = billTypeFuture.get();
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> instanceType = instanceTypeFuture.get();
        List<String> instanceModel = instanceModelFuture.get();
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();

        List<String> AffType = Lang.list("母机", "交换机", "机柜");
        List<String> systemDiskTypes = PplDiskTypeEnum.systemDiskNameList;
        List<String> dataDiskTypes = PplDiskTypeEnum.dataDiskNameList;

        List<String> winRateRange = Lang.list();

        config.put("D", demandType.size());
        config.put("E", demandScene.size());
        config.put("G", billType.size());
        config.put("N", cityNames.size());
        config.put("O", zones.size());
        config.put("P", instanceType.size());
        config.put("Q", instanceModel.size());
        config.put("V", 2);
        config.put("X", AffType.size());
        config.put("Z", systemDiskTypes.size());
        config.put("AB", dataDiskTypes.size());
        config.put("R", parseCpu.size());
        config.put("S", parseRam.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }
        config.put("H", winRateRange.size());

        List<PplCvmImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplCvmImportExcelDTO one = new PplCvmImportExcelDTO();

            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(parseCpu.get(i));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(parseRam.get(i));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setIsAcceptAlternative("是");
                one.setZoneName("随机可用区");
            }
            if (i == 1) {
                one.setIsAcceptAlternative("否");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i < instanceType.size()) {
                one.setInstanceType(instanceType.get(i));
            }
            if (i < instanceModel.size()) {
                one.setInstanceModel(instanceModel.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i).getZoneName());
            }
            if (i < AffType.size()) {
                one.setAffinityType(AffType.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < systemDiskTypes.size()) {
                one.setSystemDiskType(systemDiskTypes.get(i));
            }
            if (i < dataDiskTypes.size()) {
                one.setDataDiskType(dataDiskTypes.get(i));
            }
            ret.add(one);
        }

        return ret;
    }

    @SneakyThrows
    private List<PplGpuImportExcelDTO> getGpuDictData(HashMap<String, Integer> config) {

        String demandSceneSql = "select distinct ${column} "
                + "from ppl_config_demand_scene order by ${column};";

        Future<List<String>> billTypeFuture = executorService.submit(() -> {
            return pplDictService.queryBillType();
        });

        // 此处region和zone区别于cvm的  数据来源为腾讯云
        Future<List<String>> citysFuture = executorService.submit(() -> {
            return dictService.queryRegionNameList();
        });
        Future<List<String>> zonesFuture = executorService.submit(() -> {
            return dictService.queryZoneNameList();
        });
        Future<List<String>> gpuInstanceTypeFuture = executorService.submit(() -> {
            return pplDictService.queryGpuInstanceTypeList();
        });
        Future<List<String>> demandSceneFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_scene"));
        });
        Future<List<String>> demandTypeFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class,
                    demandSceneSql.replace("${column}", "demand_type"));
        });

        String coreSpl = "select distinct parse_ram\n"
                + "from industry_demand_region_zone_instance_type_dict where deleted=0 and parse_ram > 0 order by parse_ram";
        Future<List<String>> parseCpuFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl.replace("parse_ram", "parse_core"));
        });
        Future<List<String>> parseRamFuture = executorService.submit(() -> {
            return demandDBHelper.getRaw(String.class, coreSpl);
        });

        List<String> zones = zonesFuture.get();
        List<String> demandScene = demandSceneFuture.get();
        List<String> billType = Lang.list("CDH", "包年包月", "包销", "按量计费", "竞价付费");
        List<String> demandType = demandTypeFuture.get();
        List<String> cityNames = citysFuture.get();
        List<String> parseCpu = parseCpuFuture.get();
        List<String> parseRam = parseRamFuture.get();
        List<String> gpuInstanceTypeList = gpuInstanceTypeFuture.get();

        List<String> bizSceneList = Lang.list("视觉计算", "AI计算场景", "科学计算场景");
        List<String> bizDetailList = Lang.list("云游戏", "大型规模训练", "工业仿真", "图像渲染", "渲染农场", "图像分类",
                "自动驾驶", "推荐系统");

        List<String> winRateRange = Lang.list();

        config.put("D", demandType.size());
        config.put("E", demandScene.size());
        config.put("F", bizSceneList.size());
        config.put("G", bizDetailList.size());
        config.put("H", billType.size());
        config.put("O", cityNames.size());
        config.put("P", zones.size());
        config.put("Q", gpuInstanceTypeList.size());

        config.put("S", parseCpu.size());
        config.put("T", parseRam.size());

        for (int cnt = 0; cnt <= 100; cnt++) {
            winRateRange.add(cnt + "%");
        }
        config.put("J", winRateRange.size());

        List<PplGpuImportExcelDTO> ret = Lang.list();
        for (int i = 0; i < 1000; i++) {
            PplGpuImportExcelDTO one = new PplGpuImportExcelDTO();

            if (i < parseCpu.size()) {
                one.setInstanceModelCpuCore(Integer.parseInt(parseCpu.get(i)));
            }
            if (i < parseRam.size()) {
                one.setInstanceModelRam(Integer.parseInt(parseRam.get(i)));
            }

            if (i < demandScene.size()) {
                one.setDemandScene(demandScene.get(i));
            }
            if (i < demandType.size()) {
                one.setDemandTypeName(demandType.get(i));
            }
            if (i == 0) {
                one.setBeginElasticDate("00:00");
                one.setEndElasticDate("23:59");
                one.setNote("备注");
                one.setZoneName("随机可用区");
            }

            if (i < billType.size()) {
                one.setBillType(billType.get(i));
            }
            if (i < cityNames.size()) {
                one.setRegionName(cityNames.get(i));
            }
            if (i > 0 && i < zones.size()) {
                one.setZoneName(zones.get(i));
            }
            if (i < winRateRange.size()) {
                one.setWinRate(winRateRange.get(i));
            }
            if (i < bizSceneList.size()) {
                one.setBizScene(bizSceneList.get(i));
            }
            if (i < bizDetailList.size()) {
                one.setBizDetail(bizDetailList.get(i));
            }
            if (i < gpuInstanceTypeList.size()) {
                one.setInstanceType(gpuInstanceTypeList.get(i));
            }
            ret.add(one);
        }

        return ret;
    }


    @Override
    @Transactional(value = "demandTransactionManager")
    public void importPplVersionItem(ImportVersionGroupReq req) {

        checkData(req);

        Long groupId = req.getGroupId();

        // 查询最大的record版本
        PplVersionGroupRecordDO latestRecord = demandDBHelper.getOne(PplVersionGroupRecordDO.class,
                "where version_group_id=? order by record_version desc limit 1", groupId);
        if (latestRecord == null) {
            throw new WrongWebParameterException("分组ID:" + groupId + "不存在审批记录");
        }

        // 找到所有没有 order 的数据，聚合
        // 创建单号 - 插入 version_group_item

        // 注意这里只插入空的数据
        // 2022-1-9 这里前端传插入数据到这里来
//        List<GroupItemDTO> emptyOrderItem = ListUtils.filter(req.getData(), (o) -> Strings.isBlank(o.getPplOrder()));

        Map<String, List<GroupItemDTO>> pplGroupByData = ListUtils.groupBy(req.getData(), (o) ->
                Strings.join("@", o.getYear(),
                        o.getMonth(),
                        o.getCustomerType(),
                        o.getCustomerUin(),
                        o.getCustomerShortName(),
                        o.getDemandType(),
                        o.getProjectName()));

        for (Entry<String, List<GroupItemDTO>> orderGroup : pplGroupByData.entrySet()) {
            String key = orderGroup.getKey();
//            log.info("deal key {}", key);
            List<GroupItemDTO> value = orderGroup.getValue();
            GroupItemDTO source = value.get(0);
            CreateOrderDTO createOrderDTO = new CreateOrderDTO();

            BigDecimal totalCoreNum = ListUtils.sum(value, GroupItemDTO::getTotalCoreNum);
            BigDecimal totalDiskNum = ListUtils.sum(value, GroupItemDTO::getTotalDiskNum);

            createOrderDTO.setYear(source.getYear());
            createOrderDTO.setMonth(source.getMonth());
            createOrderDTO.setIndustry(source.getIndustry());
            createOrderDTO.setWarZone(source.getWarZone());
            createOrderDTO.setCustomerType(source.getCustomerType());
            createOrderDTO.setCustomerName(source.getCustomerName());
            createOrderDTO.setCustomerUin(source.getCustomerUin());
            createOrderDTO.setCustomerShortName(source.getCustomerShortName());
            createOrderDTO.setCustomerSource(source.getCustomerSource());
            createOrderDTO.setIndustryDept(req.getIndustryDept());
            createOrderDTO.setSubmitUser(LoginUtils.getUserName());
            createOrderDTO.setAllCore(totalCoreNum.intValue());
            createOrderDTO.setAllDisk(totalDiskNum.intValue());
            createOrderDTO.setSourceTypeEnum(req.getSourceTypeEnum());
            createOrderDTO.setIsComd(req.getIsComd());
            createOrderDTO.setOriginPplOrder(req.getOriginPplOrder());
            createOrderDTO.setPrefix(req.getPrefix());
            PplOrderDO pplOrder = pplImportService.createPplOrder(createOrderDTO);

            List<PplVersionGroupRecordItemDO> transform = ListUtils.transform(value, (o) -> {
                PplVersionGroupRecordItemDO one = trans(o);
                one.setVersionGroupId(latestRecord.getVersionGroupId());
                one.setVersionGroupRecordId(latestRecord.getId());
                /*
                 *  这里个接口的状态都是  VERSION_IMPORT
                 */
                one.setStatus(PplItemStatusEnum.VERSION_IMPORT.getCode());
                if (Strings.isNotBlank(o.getStatus())) {
                    one.setStatus(o.getStatus());
                }
                one.setRecordVersion(latestRecord.getRecordVersion());
                if (Strings.isNotBlank(o.getSourcePplId())) {
                    one.setSourcePplId(o.getSourcePplId());
                    one.setPplId(req.getPrefix() + o.getSourcePplId());
                } else {
                    one.setPplId(pplCommonService.generatePplItemId(pplOrder.getPplOrder()));
                }
                one.setPplOrder(pplOrder.getPplOrder());
                one.setCreator(LoginUtils.getUserName());
                one.setImportType(o.getImportType());

                updateTotalAmount(one);
                one.setInstanceNumApplyAfter(o.getApplyInstanceNum());
                one.setTotalCoreApplyAfter(o.getApplyTotalCore());
                //2024-04-28, 把ppl是否为毛刺固化到DB 中
                if (Strings.isNotBlank(o.getIsSpikeName())
                        && PplOrderSourceTypeEnum.COMD_INTERVENE == req.getSourceTypeEnum()) {
                    PplProjectTypeEnum projectTypeEnum = PplProjectTypeEnum.getByName(o.getIsSpikeName());
                    assert projectTypeEnum != null;
                    one.setIsSpike(projectTypeEnum.getDbCode());
                }
                return one;
            });
            demandDBHelper.insertBatchWithoutReturnId(transform);
        }
    }

    private void checkData(ImportVersionGroupReq req) {

        Long groupId = req.getGroupId();

        WhereContent whereContent = new WhereContent();
        whereContent.andEqual(PplVersionGroupDO::getId, groupId);
        PplVersionGroupDO oneGroup = demandDBHelper.getOne(PplVersionGroupDO.class, whereContent.getSql(),
                whereContent.getParams());
        if (oneGroup == null) {
            throw BizException.makeThrow("分组ID: %s 不存在这个分组", groupId);
        }
        String versionCode = oneGroup.getVersionCode();
        PplVersionDO versionDO = demandDBHelper.getOne(PplVersionDO.class, "where version_code=?", versionCode);
        if (versionDO == null) {
            throw BizException.makeThrow("未找到版本： %s", versionCode);
        }

        YearMonth startYearMonth = new YearMonth(versionDO.getDemandBeginYear(), versionDO.getDemandBeginMonth());
        YearMonth endYearMonth = new YearMonth(versionDO.getDemandEndYear(), versionDO.getDemandEndMonth());

        List<GroupItemDTO> data = req.getData();
        for (GroupItemDTO datum : data) {
            LocalDate beginBuyDate = datum.getBeginBuyDate();
            LocalDate endBuyDate = datum.getEndBuyDate();
            if (beginBuyDate == null || endBuyDate == null) {
                throw BizException.makeThrow("开始/结束购买日期为空");
            }
            Period between = Period.between(datum.getBeginBuyDate().withDayOfMonth(1),
                    datum.getEndBuyDate().withDayOfMonth(1));
            if (Strings.equals(datum.getDemandType(), PplDemandTypeEnum.NEW.getCode())
                    || Strings.equals(datum.getDemandType(), PplDemandTypeEnum.RETURN.getCode())) {
                if (Math.abs(between.getMonths()) > 1) {
                    throw BizException.makeThrow("新增需求和退回需求： 结束购买日期年月 - 开始购买日期年月 > 1");
                }
            }

            int year = beginBuyDate.getYear();
            int month = beginBuyDate.getMonthValue();

            if (startYearMonth.getYear() > year ||
                    (startYearMonth.getYear() == year && startYearMonth.getMonth() > month)) {
                String info = "开始日期:" + beginBuyDate + "  <  版本开始日期:" + startYearMonth.toDateStr();
                throw new BizException(info);
            }
            // 2023-2-8  允许录入版本时间外的ppl
//            if (endYearMonth.getYear() < year ||
//                    (endYearMonth.getYear() == year && endYearMonth.getMonth() < month)) {
//                String info = "开始日期:" + beginBuyDate + "  >  版本结束日期:" + endYearMonth.toDateStr();
//                throw new BizException(info);
//            }

            if (beginBuyDate.compareTo(endBuyDate) > 0) {
                throw new BizException("结束日期比开始日期小");
            }

            if (Strings.isBlank(datum.getProduct())) {
                throw BizException.makeThrow("product 为空");
            }

            if (Strings.isNotBlank(datum.getIsSpikeName())
                    && !PplProjectTypeEnum.isValidName(datum.getIsSpikeName())) {
                throw BizException.makeThrow("项目类型填写错误： %s", datum.getIsSpikeName());
            }

        }


    }

    @Override
    public FileNameAndBytesDTO exportPplVersionItemExcelWithStockSupply(ExportPplVersionItemReq req) {

//        String templateName = "ppl13week_stock_supply_export.xlsx";
        InputStream templateIn = IOUtils.readClasspathResourceInputStream("excel/" + req.getExcelTemplateName());

        ByteArrayOutputStream out = new ByteArrayOutputStream();
//        Map<String, Collection<DataWrapper>> dataWrapperMap = dropMapUtil.dataWrapperMap(groupDO.getProduct());

        ExcelWriter excelWriter = EasyExcel.write(out)
                .withTemplate(templateIn).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("CVM").build();
        WriteSheet cbsSheet = EasyExcel.writerSheet("CBS").build();

        // 如果是横向的，加上这个
//        FillConfig fillConfig = FillConfig.builder().direction(WriteDirectionEnum.HORIZONTAL).build();

        List<PplItemStockSupplyExportCvmDTO> cvmData = Lang.list();
        List<PplItemStockSupplyExportCbsDTO> cbsData = Lang.list();

        Map<String, String> zoneNameMap = industryDemandDictService.getZoneNameMapByZoneStrId();
        for (GroupItemDTO datum : req.getData()) {

            List<PplStockSupplyReqWithRspVO> supplyData = datum.getSupplyData();

            List<PplStockSupplyReqWithRspVO> cvmSupply = ListUtils.filter(supplyData, o -> "CVM".equals(o.getType()));
            List<PplStockSupplyRspDO> cvmRsp = Lang.list();
            cvmSupply.forEach((o) -> cvmRsp.addAll(o.getRsp()));

            for (PplStockSupplyRspDO pplStockSupplyRspDO : cvmRsp) {
                PplItemStockSupplyExportCvmDTO cvm = transFrom(datum);

                PplStockSupplyMatchTypeEnum byCode = PplStockSupplyMatchTypeEnum.getByCode(
                        pplStockSupplyRspDO.getMatchType());
                cvm.setMatchInstanceType(pplStockSupplyRspDO.getMatchInstanceType());

                if (byCode == PplStockSupplyMatchTypeEnum.BUY) {
                    pplStockSupplyRspDO.handleHostType();
                    cvm.setHostType(pplStockSupplyRspDO.getHostType());
                    cvm.setHostNum(pplStockSupplyRspDO.getHostNum());
                }
                cvm.setMatchType(PplStockSupplyMatchTypeEnum.getNameByCode(pplStockSupplyRspDO.getMatchType()));
                cvm.setMatchCoreNum(pplStockSupplyRspDO.getInstanceTotalCore());
                String zoneName = zoneNameMap.get(pplStockSupplyRspDO.getZone());
                cvm.setMatchZone(zoneName != null ? zoneName : "");
                cvm.setStockRemark(pplStockSupplyRspDO.getRemark());
                cvm.setBizId(datum.getBizId());
                cvmData.add(cvm);
            }

            List<PplStockSupplyReqWithRspVO> cbsSupply = ListUtils.filter(supplyData, o -> "CBS".equals(o.getType()));
            List<PplStockSupplyRspDO> cbsRsp = Lang.list();
            cbsSupply.forEach((o) -> cbsRsp.addAll(o.getRsp()));
            for (PplStockSupplyRspDO pplStockSupplyRspDO : cbsRsp) {
                PplItemStockSupplyExportCbsDTO cbs = transFrom1(datum);
                cbs.setMatchType(PplStockSupplyMatchTypeEnum.getNameByCode(pplStockSupplyRspDO.getMatchType()));
                String matchDiskTypeName = pplStockSupplyRspDO.getMatchDiskTypeName();

                if (Strings.isBlank(matchDiskTypeName)) {
                    matchDiskTypeName = pplStockSupplyRspDO.getDiskTypeName();
                }
                cbs.setMatchDiskType(matchDiskTypeName);
                cbs.setHostType(pplStockSupplyRspDO.getHostType());
                cbs.setMatchDiskNum(pplStockSupplyRspDO.getDiskTotalSize());

                int systemDisk = getDiskTotalNum(datum.getSystemDiskType(), datum.getSystemDiskNum(),
                        datum.getSystemDiskStorage());
                int dataDisk = getDiskTotalNum(datum.getDataDiskType(), datum.getDataDiskNum(),
                        datum.getDataDiskStorage());

                int num1 = 0;
                int num2 = 0;
                if (Strings.equals(datum.getSystemDiskType(), PplDiskTypeEnum.CLOUD_PREMIUM.getName())) {
                    num1 += systemDisk;
                }
                if (Strings.equals(datum.getSystemDiskType(), PplDiskTypeEnum.CLOUD_SSD.getName())) {
                    num2 += systemDisk;
                }
                if (Strings.equals(datum.getDataDiskType(), PplDiskTypeEnum.CLOUD_PREMIUM.getName())) {
                    num1 += dataDisk;
                }
                if (Strings.equals(datum.getDataDiskType(), PplDiskTypeEnum.CLOUD_SSD.getName())) {
                    num2 += dataDisk;
                }
                cbs.setTotalDiskNum1(num1);
                cbs.setTotalDiskNum2(num2);
                cbsData.add(cbs);
            }
        }

        excelWriter.fill(new FillWrapper("item", cvmData), writeSheet)
                .fill(new FillWrapper("item", cbsData), cbsSheet)
                .finish();

        FileNameAndBytesDTO fileNameAndBytesDTO = new FileNameAndBytesDTO();
        fileNameAndBytesDTO.setBytes(out.toByteArray());
        fileNameAndBytesDTO.setFileName(
                req.getFileName() + com.pugwoo.wooutils.lang.DateUtils.format(new Date(), "-yyyyMMdd-HHmmss")
                        + ".xlsx");

        return fileNameAndBytesDTO;

    }

    @Override
    public List<PplVersionGroupDO> queryGroupByVersionCode(String versionCode, String product) {
        WhereContent where = new WhereContent();
        where.andEqual(PplVersionGroupDO::getVersionCode, versionCode);
        if (StringTools.isNotBlank(product)) {
            where.andEqual(PplVersionGroupDO::getProduct, product);
        }
        return demandDBHelper.getAll(PplVersionGroupDO.class, where.getSql(), where.getParams());
    }

    private int getDiskTotalNum(String systemDiskType, Integer systemDiskNum, Integer systemDiskStorage) {
        int ret = 0;
        if (Strings.isBlank(systemDiskType)) {
            return ret;
        }
        if (systemDiskStorage == null || systemDiskNum == null) {
            return ret;
        }
        return systemDiskNum * systemDiskStorage;
    }


    private PplItemStockSupplyExportCbsDTO transFrom1(GroupItemDTO datum) {
        PplItemStockSupplyExportCbsDTO pplItemStockSupplyExportCbsDTO = new PplItemStockSupplyExportCbsDTO();
        pplItemStockSupplyExportCbsDTO.setPplId(datum.getPplId());
        pplItemStockSupplyExportCbsDTO.setCustomerTypeName(datum.getCustomerTypeName());
        pplItemStockSupplyExportCbsDTO.setCustomerUin(datum.getCustomerUin());
        pplItemStockSupplyExportCbsDTO.setCustomerShortName(datum.getCustomerShortName());
        pplItemStockSupplyExportCbsDTO.setDemandTypeName(datum.getDemandTypeName());
        pplItemStockSupplyExportCbsDTO.setDemandScene(datum.getDemandScene());
        pplItemStockSupplyExportCbsDTO.setProjectName(datum.getProjectName());
        pplItemStockSupplyExportCbsDTO.setBillType(datum.getBillType());
        pplItemStockSupplyExportCbsDTO.setWinRate(datum.getWinRate() != null ? String.valueOf(datum.getWinRate()) : "");
        pplItemStockSupplyExportCbsDTO.setBeginBuyDate(String.valueOf(datum.getBeginBuyDate()));
        pplItemStockSupplyExportCbsDTO.setEndBuyDate(String.valueOf(datum.getEndBuyDate()));
        pplItemStockSupplyExportCbsDTO.setBeginElasticDate(
                datum.getBeginElasticDate() != null ? String.valueOf(datum.getBeginElasticDate()) : "");
        pplItemStockSupplyExportCbsDTO.setEndElasticDate(
                datum.getEndElasticDate() != null ? String.valueOf(datum.getEndElasticDate()) : "");
        pplItemStockSupplyExportCbsDTO.setNote(datum.getNote());
        pplItemStockSupplyExportCbsDTO.setRegionName(datum.getRegionName());
        pplItemStockSupplyExportCbsDTO.setZoneName(datum.getZoneName());
        pplItemStockSupplyExportCbsDTO.setInstanceNum(datum.getInstanceNum());
        pplItemStockSupplyExportCbsDTO.setSystemDiskType(datum.getSystemDiskType());
        pplItemStockSupplyExportCbsDTO.setSystemDiskStorage(datum.getSystemDiskStorage());
        pplItemStockSupplyExportCbsDTO.setDataDiskType(datum.getDataDiskType());
        pplItemStockSupplyExportCbsDTO.setDataDiskStorage(datum.getDataDiskStorage());
        pplItemStockSupplyExportCbsDTO.setDataDiskNum(datum.getDataDiskNum());
        pplItemStockSupplyExportCbsDTO.setIndustryDept(datum.getIndustryDept());
        pplItemStockSupplyExportCbsDTO.setProduct(datum.getProduct());
        pplItemStockSupplyExportCbsDTO.setGroupStatus(PplVersionGroupStatusEnum.getNameByCode(datum.getGroupStatus()));
//        pplItemStockSupplyExportCbsDTO.setTotalDiskNum1();
//        pplItemStockSupplyExportCbsDTO.setTotalDiskNum2();
//        pplItemStockSupplyExportCbsDTO.setMatchType();
//        pplItemStockSupplyExportCbsDTO.setMatchDiskType();
//        pplItemStockSupplyExportCbsDTO.setHostType();
//        pplItemStockSupplyExportCbsDTO.setMatchDiskNum();
        pplItemStockSupplyExportCbsDTO.setSource(datum.getAppliedSource() == 1 ? "预约单" : "PPL");
        pplItemStockSupplyExportCbsDTO.setBizId(datum.getBizId());
        return pplItemStockSupplyExportCbsDTO;
    }

    private PplItemStockSupplyExportCvmDTO transFrom(GroupItemDTO datum) {
        PplItemStockSupplyExportCvmDTO pplItemStockSupplyExportCvmDTO = new PplItemStockSupplyExportCvmDTO();
        pplItemStockSupplyExportCvmDTO.setPplId(datum.getPplId());
        pplItemStockSupplyExportCvmDTO.setCustomerTypeName(datum.getCustomerTypeName());
        pplItemStockSupplyExportCvmDTO.setCustomerUin(datum.getCustomerUin());
        pplItemStockSupplyExportCvmDTO.setCustomerShortName(datum.getCustomerShortName());
        pplItemStockSupplyExportCvmDTO.setDemandTypeName(datum.getDemandTypeName());
        pplItemStockSupplyExportCvmDTO.setDemandScene(datum.getDemandScene());
        pplItemStockSupplyExportCvmDTO.setProjectName(datum.getProjectName());
        pplItemStockSupplyExportCvmDTO.setBillType(datum.getBillType());
        pplItemStockSupplyExportCvmDTO.setWinRate(datum.getWinRate() != null ? String.valueOf(datum.getWinRate()) : "");
        pplItemStockSupplyExportCvmDTO.setBeginBuyDate(String.valueOf(datum.getBeginBuyDate()));
        pplItemStockSupplyExportCvmDTO.setYearMonth(
                com.pugwoo.wooutils.lang.DateUtils.format(datum.getBeginBuyDate(), "yyyy-MM"));
        pplItemStockSupplyExportCvmDTO.setEndBuyDate(String.valueOf(datum.getEndBuyDate()));
        pplItemStockSupplyExportCvmDTO.setBeginElasticDate(
                datum.getBeginElasticDate() != null ? String.valueOf(datum.getBeginElasticDate()) : "");
        pplItemStockSupplyExportCvmDTO.setEndElasticDate(
                datum.getEndElasticDate() != null ? String.valueOf(datum.getEndElasticDate()) : "");
        pplItemStockSupplyExportCvmDTO.setNote(datum.getNote());
        pplItemStockSupplyExportCvmDTO.setRegionName(datum.getRegionName());
        pplItemStockSupplyExportCvmDTO.setZoneName(datum.getZoneName());
        pplItemStockSupplyExportCvmDTO.setInstanceType(datum.getInstanceType());
        pplItemStockSupplyExportCvmDTO.setInstanceModel(datum.getInstanceModel());
        pplItemStockSupplyExportCvmDTO.setInstanceModelCpuCore(datum.getInstanceModelCoreNum());
        pplItemStockSupplyExportCvmDTO.setInstanceModelRam(datum.getInstanceModelRamNum());
        pplItemStockSupplyExportCvmDTO.setInstanceNum(datum.getInstanceNum());
        pplItemStockSupplyExportCvmDTO.setTotalCoreNum(datum.getTotalCoreNum());
//        pplItemStockSupplyExportCvmDTO.setMatchType();
//        pplItemStockSupplyExportCvmDTO.setMatchInstanceType();
//        pplItemStockSupplyExportCvmDTO.setMatchCoreNum();
        pplItemStockSupplyExportCvmDTO.setIndustryDept(datum.getIndustryDept());
        pplItemStockSupplyExportCvmDTO.setProduct(datum.getProduct());
        pplItemStockSupplyExportCvmDTO.setGroupStatus(PplVersionGroupStatusEnum.getNameByCode(datum.getGroupStatus()));
        pplItemStockSupplyExportCvmDTO.setSource(datum.getAppliedSource() == 1 ? "预约单" : "PPL");
        pplItemStockSupplyExportCvmDTO.setBizId(datum.getBizId());
        return pplItemStockSupplyExportCvmDTO;
    }

    public PplVersionGroupRecordItemDO trans(GroupItemDTO source) {
        PplVersionGroupRecordItemDO pplVersionGroupRecordItemDO = new PplVersionGroupRecordItemDO();
//        pplVersionGroupRecordItemDO.setVersionGroupId();
//        pplVersionGroupRecordItemDO.setRecordVersion();
//        pplVersionGroupRecordItemDO.setVersionGroupRecordId();
        pplVersionGroupRecordItemDO.setPplOrder(source.getPplOrder());
        pplVersionGroupRecordItemDO.setPplId(source.getPplId());
        pplVersionGroupRecordItemDO.setStatus(source.getStatus());
        pplVersionGroupRecordItemDO.setProduct(source.getProduct());
        pplVersionGroupRecordItemDO.setDemandType(source.getDemandType());
        pplVersionGroupRecordItemDO.setDemandScene(source.getDemandScene());
        pplVersionGroupRecordItemDO.setProjectName(source.getProjectName());
        pplVersionGroupRecordItemDO.setBillType(source.getBillType());
        pplVersionGroupRecordItemDO.setWinRate(source.getWinRate());
        pplVersionGroupRecordItemDO.setImportantDemand(source.getImportantDemand());
        pplVersionGroupRecordItemDO.setBeginBuyDate(source.getBeginBuyDate());
        pplVersionGroupRecordItemDO.setEndBuyDate(source.getEndBuyDate());
        pplVersionGroupRecordItemDO.setBeginElasticDate(source.getBeginElasticDate());
        pplVersionGroupRecordItemDO.setEndElasticDate(source.getEndElasticDate());
        pplVersionGroupRecordItemDO.setNote(source.getNote());
        pplVersionGroupRecordItemDO.setRegionName(source.getRegionName());
        pplVersionGroupRecordItemDO.setZoneName(source.getZoneName());
        pplVersionGroupRecordItemDO.setInstanceType(source.getInstanceType());
        pplVersionGroupRecordItemDO.setInstanceModel(source.getInstanceModel());
        pplVersionGroupRecordItemDO.setInstanceNum(source.getInstanceNum());
        pplVersionGroupRecordItemDO.setTotalCore(source.getTotalCoreNum());
        pplVersionGroupRecordItemDO.setAlternativeInstanceType(Strings.join(";", source.getAlternativeInstanceType()));
        pplVersionGroupRecordItemDO.setAffinityType(source.getAffinityType());
        pplVersionGroupRecordItemDO.setAffinityValue(source.getAffinityValue());
        pplVersionGroupRecordItemDO.setSystemDiskType(source.getSystemDiskType());
        pplVersionGroupRecordItemDO.setSystemDiskStorage(source.getSystemDiskStorage());
        pplVersionGroupRecordItemDO.setSystemDiskNum(source.getSystemDiskNum());
        pplVersionGroupRecordItemDO.setDataDiskType(source.getDataDiskType());
        pplVersionGroupRecordItemDO.setDataDiskStorage(source.getDataDiskStorage());
        pplVersionGroupRecordItemDO.setDataDiskNum(source.getDataDiskNum());
        pplVersionGroupRecordItemDO.setCbsIo(source.getCbsIo());

        //GPU新增
        pplVersionGroupRecordItemDO.setGpuProductType(source.getGpuProductType());
        pplVersionGroupRecordItemDO.setGpuType(source.getGpuType());
        pplVersionGroupRecordItemDO.setGpuNum(source.getGpuNum());
        pplVersionGroupRecordItemDO.setTotalGpuNum(source.getTotalGpuNum());
        pplVersionGroupRecordItemDO.setBizScene(source.getBizScene());
        pplVersionGroupRecordItemDO.setBizDetail(source.getBizDetail());
        pplVersionGroupRecordItemDO.setIsAcceptAdjust(source.getIsAcceptAdjust());
        pplVersionGroupRecordItemDO.setAcceptGpu(source.getAcceptGpu());
        pplVersionGroupRecordItemDO.setServiceTime(source.getServiceTime());
        pplVersionGroupRecordItemDO.setSaleDurationYear(source.getSaleDurationYear());
        pplVersionGroupRecordItemDO.setBusinessCpq(source.getBusinessCpq());
        pplVersionGroupRecordItemDO.setApplyDiscount(source.getApplyDiscount());

        pplVersionGroupRecordItemDO.setConsensusStatus(source.getConsensusStatus());
        pplVersionGroupRecordItemDO.setConsensusMatchType(source.getConsensusMatchType());
        pplVersionGroupRecordItemDO.setConsensusZoneName(source.getConsensusZoneName());
        pplVersionGroupRecordItemDO.setConsensusInstanceType(source.getConsensusInstanceType());
        pplVersionGroupRecordItemDO.setConsensusDemandDate(source.getConsensusDemandDate());

        pplVersionGroupRecordItemDO.setYunxiaoOrderId(source.getYunxiaoOrderId());
        if (source.getIsLock() != null) {
            pplVersionGroupRecordItemDO.setIsLock(source.getIsLock());
            pplVersionGroupRecordItemDO.setStatus(
                    source.getIsLock() ? PplItemStatusEnum.APPLIED.getCode() : PplItemStatusEnum.VALID.getCode());
        }
        return pplVersionGroupRecordItemDO;
    }


}
