package cloud.demand.app.modules.p2p.ppl13week_forecast.service.impl;

import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCdbMemGroupEnum.getMemGroupColumn;
import static cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceFromEnum.getFromSerialInterval;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.exception.WrongWebParameterException;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SQLAssemblyUtils;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.forecast_compute.enums.SerialIntervalEnum;
import cloud.demand.app.modules.forecast_compute.model.ForecastAlgorithms.Algorithm;
import cloud.demand.app.modules.operation_view.inventory_health.web.InventoryHealthConfigController;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigPubRegionConfigDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastConfigSpikeThresholdDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDetailDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputDetailLatestForMrpDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeCkDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastInputExcludedSpikeLatestDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DO.PplForecastPredictTaskDO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.DailyZoneAppidGinstypePaymodeApproleDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.DailyZoneAppidGinstypePaymodeApproleWithCustomerDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.ForecastInputSqlDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.SpikeDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.DTO.ZiyanDemandOriginDataDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastGroupDimsEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.entity.enums.PplForecastTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastBillTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCdbMemGroupEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastCustomerScopeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastProductEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastRangeEnumDTO;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastResourcePoolEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceFromEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.enums.Ppl13weekForecastSourceTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekInputService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekPredictService;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekQueryService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.cache.HiSpeedCache;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import com.pugwoo.wooutils.string.StringTools;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.nutz.lang.Lang;
import org.nutz.lang.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@SuppressWarnings("SpringTransactionalComponentInspection")
@Service
@Slf4j
public class Ppl13WeekInputServiceImpl implements Ppl13weekInputService {

    @Resource
    DBHelper demandDBHelper;
    @Resource
    private DBHelper obsDBHelper;
    @Resource
    private Ppl13weekPredictService ppl13weekPredictService;
    @Resource
    private Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;
    @Resource
    private Ppl13weekQueryService ppl13weekQueryService;
    private InventoryHealthConfigController config;

    @Override
    public List<String> getPublicZoneName() {
        String sql = "select distinct zone_name\n"
                + "                from ppl_forecast_config_pub_region_config\n"
                + "                where  zone_name != '' and !isnull(zone_name) and is_value=1\n"
                + "                order by zone_name";
        return DBList.demandDBHelper.getRaw(String.class, sql);
    }

    @Override
    public List<PplForecastConfigPubRegionConfigDO> getValuePublicRegionConfig() {
        return DBList.demandDBHelper.getAll(PplForecastConfigPubRegionConfigDO.class).stream()
                .filter(PplForecastConfigPubRegionConfigDO::getIsValue).collect(Collectors.toList());
    }

    private static boolean isAll(String str) {
        return StringTools.isBlank(str) || "所有".equals(str);
    }

    private List<PplForecastInputExcludedSpikeDO> getExcludedSpikesForMiddleNew(String predictDate,
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum) {
        List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold>0");
        if (thresholds.isEmpty()) { // 没有剔除规则，那就没有毛刺
            return Lang.list();
        }

        List<String> bizRangeType = getConditionValue(sourceTypeEnum);
        List<String> billType = getConditionValue(billTypeEnum);
        List<String> publicZoneName = getPublicZoneName();

        // 毛刺的黑名单，threshold<0的机型都是黑名单
        List<String> instanceTypeBlacklist = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        // 找到所有的毛刺
        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/common_condition.sql");
        commonCondition = commonCondition.replace("${headCustomerShortNameCondition}",
                getHeadCustomerNameCondition(true));
        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/get_spike.sql");
        getSpikeSql = getSpikeSql.replace("${commonCondition}", commonCondition);

        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        params.put("paymodeRangeType", billType);
        params.put("bizRangeType", bizRangeType);
        params.put("instanceTypeBlacklist", instanceTypeBlacklist);
        params.put("publicZoneName", publicZoneName);

        List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql, params);

        return getPplForecastInputExcludedSpike1(thresholds, spikesRaw);
    }

    /**
     * 查询头部客户的条件，不要iegg的uin条件
     */
    private String getHeadCustomerNameConditionWithoutIeggUin(boolean isExclude) {
        StringBuilder andSql = new StringBuilder(" and ");
        if (isExclude) {
            andSql.append(" not ");
        }

        Map<String, Set<String>> headCustomerShortNameGroup = ppl13weekCommonDataAccess.getHeadCustomerShortNameGroup();
        andSql.append(" (customer_short_name in (").append(toSqlParam(join(headCustomerShortNameGroup))).append(")) ");

        return andSql.toString();
    }

    /**
     * 获得查询头部客户的条件
     *
     * @param isExclude 如果是要剔除大客户，则传true；如果只是要大客户，则传false
     */
    private String getHeadCustomerNameCondition(boolean isExclude) {
        String ieggUin = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/iegg_uin.sql");
        StringBuilder andSql = new StringBuilder(" and ");
        if (isExclude) {
            andSql.append(" not ");
        }

        // 单独处理IEGG
        Map<String, Set<String>> headCustomerShortNameGroup = ppl13weekCommonDataAccess.getHeadCustomerShortNameGroup();
        if (headCustomerShortNameGroup.containsKey("IEGG")) {
            Set<String> ieggGroup = headCustomerShortNameGroup.get("IEGG");
            headCustomerShortNameGroup.remove("IEGG");

            andSql.append(" (\n");
            andSql.append("     customer_short_name in (").append(toSqlParam(ieggGroup)).append(")\n");
            andSql.append("     and uin in (").append(ieggUin).append(")\n");
            andSql.append("     or customer_short_name in (").append(toSqlParam(join(headCustomerShortNameGroup)))
                    .append(")\n");
            andSql.append(") ");
        } else {
            andSql.append(" (customer_short_name in (").append(toSqlParam(join(headCustomerShortNameGroup)))
                    .append(")) ");
        }

        return andSql.toString();
    }

    private Set<String> join(Map<String, Set<String>> headCustomerShortNameGroup) {
        Set<String> result = new HashSet<>();
        for (Set<String> values : headCustomerShortNameGroup.values()) {
            result.addAll(values);
        }
        return result;
    }

    private static String toSqlParam(Collection<String> names) {
        return StringTools.join(",", ListUtils.transform(names, o -> "'" + o.replace("'", "''") + "'"));
    }

    private List<PplForecastInputExcludedSpikeDO> getEMRExcludedSpikesForWhole(String predictDate,
            Integer customSpikeThreshold) {

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/emr/common_condition.sql");
        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/emr/get_spike.sql");
        getSpikeSql = getSpikeSql.replace("${CONDITION}", commonCondition);

        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        params.put("spikeThreshold", customSpikeThreshold);
        List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql, params);

        // 说明，目前国内外相同的阈值，因此简化实现，后续有变更再扩展
        return ListUtils.transform(spikesRaw, SpikeDTO::toSpikeDO);
    }

    private List<PplForecastInputExcludedSpikeDO> getCDBExcludedSpikesForWhole(String predictDate,
            Integer customSpikeThreshold) {

        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/cdb/get_spike.sql");

        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        params.put("spikeThreshold", customSpikeThreshold);
        List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql, params);

        // 说明，目前国内外相同的阈值，因此简化实现，后续有变更再扩展
        return ListUtils.transform(spikesRaw, SpikeDTO::toSpikeDO);
    }

    private List<PplForecastInputExcludedSpikeDO> getCBSExcludedSpikesForWhole(String predictDate) {
        // 目前CBS分境内外不同阈值，故先放到SQL中
        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/cbs/get_spike.sql");

        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql, params);

        return ListUtils.transform(spikesRaw, SpikeDTO::toSpikeDO);
    }

    private List<PplForecastInputExcludedSpikeDO> getEKSExcludedSpikesForWhole(String predictDate,
            Integer customSpikeThreshold) {

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/eks/common_condition.sql");
        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/eks/get_spike.sql");

        getSpikeSql = getSpikeSql.replace("${CONDITION}", commonCondition);

        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        params.put("spikeThreshold", customSpikeThreshold);
        List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql, params);

        // 说明，目前国内外相同的阈值，因此简化实现，后续有变更再扩展
        return ListUtils.transform(spikesRaw, SpikeDTO::toSpikeDO);
    }

    private List<PplForecastInputExcludedSpikeDO> getCvmExcludedSpikesForWhole(String predictDate,
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum,
            List<String> customHeadCustomers,
            Integer customSpikeThreshold) {
        List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                PplForecastConfigSpikeThresholdDO.class, "where threshold>0");
        if (thresholds.isEmpty()) { // 没有剔除规则，那就没有毛刺
            return Lang.list();
        }
        // 如果自定义了阈值，则以自定义为准
        if (customSpikeThreshold != null && customSpikeThreshold > 0) {
            thresholds.forEach(o -> {
                if (o.getThreshold() > 0) { // 只有大于0才是有效阈值
                    o.setThreshold(customSpikeThreshold);
                }
            });
        }

        List<String> bizRangeType = getConditionValue(sourceTypeEnum);
        List<String> billType = getConditionValue(billTypeEnum);
        List<String> publicZoneName = getPublicZoneName();

        Set<String> headCustomerShortName = null;
        if (customHeadCustomers != null) {
            headCustomerShortName = new HashSet<>();
            if (customHeadCustomers.isEmpty()) {
                headCustomerShortName.add("THIS_IS_NOT_EXIST_CUSTOMER_NAME"); // 故意设置一个不存在的客户名称，这样不管in还是not in都合适
            } else {
                headCustomerShortName.addAll(customHeadCustomers);
            }
        } else {
            headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();
        }

        // 毛刺的黑名单，threshold<0的机型都是黑名单
        List<String> instanceTypeBlacklist = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        // 由于全量的数据量太大，这里分段进行，从2021年1月1号起，每半年一段
        List<DateRange> dateRanges = getDateRanges();
        List<PplForecastInputExcludedSpikeDO> result = new ArrayList<>();

        // 找到所有的毛刺
        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/whole/common_condition.sql");
        // 说明：毛刺肯定是非头部里的
        commonCondition = commonCondition.replace("${headCustomerCondition}",
                "and customer_short_name not in (:headCustomerShortName)");
        String getSpikeSql = ORMUtils.getSql("/sql/ppl13week_forecast/whole/get_spike.sql");
        getSpikeSql = getSpikeSql.replace("${commonCondition}", commonCondition);

        for (DateRange dateRange : dateRanges) {
            String dateRangeSql =
                    " and stat_time between '" + dateRange.getStartDate() + "' and '" + dateRange.getEndDate() + "' ";
            String getSpikeSql2 = getSpikeSql.replace("${dateRange}", dateRangeSql);

            Map<String, Object> params = new HashMap<>();
            params.put("predictDate", predictDate);
            params.put("paymodeRangeType", billType);
            params.put("bizRangeType", bizRangeType);
            params.put("instanceTypeBlacklist", instanceTypeBlacklist);
            params.put("publicZoneName", publicZoneName);
            params.put("headCustomerShortName", headCustomerShortName);

            List<SpikeDTO> spikesRaw = DBList.ckcldStdCrpDBHelper.getRaw(SpikeDTO.class, getSpikeSql2, params);

            result.addAll(getPplForecastInputExcludedSpike1(thresholds, spikesRaw));
        }

        return result;
    }

    /**
     * 获得毛刺数据
     *
     * @param product 根据不同的产品来决定怎样划定毛刺数据
     */
    private List<PplForecastInputExcludedSpikeDO> getExcludedSpikesForWhole(Ppl13weekForecastProductEnum product,
            String predictDate,
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum,
            List<String> customHeadCustomers,
            Integer customSpikeThreshold
            ) {
        if (product == null || product == Ppl13weekForecastProductEnum.CVM) {
            return getCvmExcludedSpikesForWhole(predictDate, sourceTypeEnum, billTypeEnum, customHeadCustomers,
                    customSpikeThreshold);
        } else if (product == Ppl13weekForecastProductEnum.EMR) {
            return getEMRExcludedSpikesForWhole(predictDate, customSpikeThreshold);
        } else if (product == Ppl13weekForecastProductEnum.EKS) {
            return getEKSExcludedSpikesForWhole(predictDate, customSpikeThreshold);
        } else if (product == Ppl13weekForecastProductEnum.CDB) {
            return getCDBExcludedSpikesForWhole(predictDate, customSpikeThreshold);
        } else if (product == Ppl13weekForecastProductEnum.CBS) {
            return getCBSExcludedSpikesForWhole(predictDate);
        }

        return null;
    }

    @Data
    public static class DateRange {

        private String startDate;
        private String endDate;
    }

    /**
     * 从2021年1月1号开始每半年一个段，startDate和endDate是两端包含的
     *
     * @return 例如 [2021-01-01,2021-06-30],[2021-07-01,2021-12-31]这样
     */
    private static List<DateRange> getDateRanges() {
        LocalDate start = DateUtils.parseLocalDate("2021-01-01");

        List<DateRange> result = new ArrayList<>();
        do {
            DateRange dateRange = new DateRange();
            dateRange.setStartDate(DateUtils.format(start));
            dateRange.setEndDate(DateUtils.format(start.plusMonths(6).minusDays(1)));
            result.add(dateRange);

            start = start.plusMonths(6);
        } while (!start.isAfter(LocalDate.now()));

        return result;
    }

    /**
     * 从结果中找到毛刺并返回
     *
     * @param thresholds 有效阈值
     * @param spikesRaw 所有数据
     * @return 匹配数据
     */
    private List<PplForecastInputExcludedSpikeDO> getPplForecastInputExcludedSpike1(
            List<PplForecastConfigSpikeThresholdDO> thresholds, List<SpikeDTO> spikesRaw) {

        // 再次确保spikesRaw里的新增或退回必须是正数，新增或退回只能有一个是正数
        spikesRaw = ListUtils.filter(spikesRaw,
                o -> o.getNewCore().compareTo(BigDecimal.ZERO) > 0 || o.getRetCore().compareTo(BigDecimal.ZERO) > 0);

        // 3. 进一步剔除毛刺，对于一个毛刺，找到规则中匹配度最高的规则，以它为准确定是否满足剔除条件
        List<PplForecastInputExcludedSpikeDO> result = new ArrayList<>();

        ForSpikeParams<SpikeDTO> forSpikeParams = new ForSpikeParams<>();
        forSpikeParams.setData(spikesRaw);
        forSpikeParams.setMatchConfigFunc((items, config) -> {
            BigDecimal total = null;
            if (items.get(0).getNewCore().compareTo(BigDecimal.ZERO) > 0) { // 新增和退回分开来
                total = NumberUtils.sum(items, SpikeDTO::getNewCore);
            } else {
                total = NumberUtils.sum(items, SpikeDTO::getRetCore);
            }

            if (total.compareTo(BigDecimal.valueOf(config.getThreshold())) >= 0) {
                result.addAll(ListUtils.transform(items, SpikeDTO::toSpikeDO));
                return true;
            }
            return false;
        });

        forSpikeParams.setTotalConfig(thresholds);
        forSpikeParams.setRegionNameFunc(SpikeDTO::getRegionName);
        forSpikeParams.setCustomhouseTitleFunc(SpikeDTO::getCustomhouseTitle);

        // 按机型收敛进行分组
        forSpikeParams.setInstanceTypeFunc(
                o -> forSpikeParams.getInstanceTypeToGroup().getOrDefault(o.getInstanceType(), o.getInstanceType())
        );

        forSpikeParams.setOtherFunc((o) -> Strings.join("@", o.getYear(), o.getMonth(),
                (StringTools.isNotBlank(o.getCustomerShortName()) && !"(空值)".equals(o.getCustomerShortName()))
                        ? o.getCustomerShortName() : o.getCustomerUin(),
                o.getNewCore().compareTo(BigDecimal.ZERO) > 0 ? "新增" : "退回" // 新增退回分开
        ));

        Ppl13WeekInputServiceImpl.applyConfig(forSpikeParams);
        return result;
    }

    @Data
    public static class ForSpikeParams<D> {

        // 要打标的数据
        List<D> data;

        // 所有>0配置
        List<PplForecastConfigSpikeThresholdDO> totalConfig;

        Map<String, String> instanceTypeToGroup = new HashMap<>(); // 机型 -> 机型收敛


        // 匹配成功了就返回 true, 停止匹配
        BiFunction<List<D>, PplForecastConfigSpikeThresholdDO, Boolean> matchConfigFunc;

        // 下面每个参数返回一下对应的字段
        Function<D, String> regionNameFunc;
        Function<D, String> customhouseTitleFunc;
        Function<D, String> instanceTypeFunc;

        Function<D, String> otherFunc;

        // 设置全部config 的时候解析
        public void setTotalConfig(List<PplForecastConfigSpikeThresholdDO> totalConfig) {
            log.info("set instanceTypeToGroup map");
            this.totalConfig = totalConfig;
            for (PplForecastConfigSpikeThresholdDO threshold : totalConfig) {
                List<String> splitInstanceTypes = threshold.getSplitInstanceTypes();
                for (String type : splitInstanceTypes) {
                    instanceTypeToGroup.put(type, threshold.getCommonInstanceType());
                }
            }
        }
    }


    public static <D> void applyConfig(ForSpikeParams<D> params) {

        if (Lang.isEmpty(params.getTotalConfig())) {
            List<PplForecastConfigSpikeThresholdDO> thresholds = DBList.demandDBHelper.getAll(
                    PplForecastConfigSpikeThresholdDO.class, "where threshold>0");
            params.setTotalConfig(thresholds);
        }

        List<D> data = params.getData();

        @Data
        @AllArgsConstructor
        class Key {

            String regionName;
            String customhouseTitle;
            String instanceType;
            String yearMonthUin;
        }

        Map<Key, List<D>> groupedSpikeRaw = ListUtils.groupBy(data, (o) -> new Key(
                params.getRegionNameFunc().apply(o),
                params.getCustomhouseTitleFunc().apply(o),
                params.getInstanceTypeFunc().apply(o),
                params.getOtherFunc().apply(o)
        ));

        for (Key key : groupedSpikeRaw.keySet()) {

            List<D> spikeDTOList = groupedSpikeRaw.get(key);

            String regionName = key.getRegionName();
            String customhouseTitle = key.getCustomhouseTitle();
            String instanceType = key.getInstanceType();

            List<PplForecastConfigSpikeThresholdDO> thresholds = params.getTotalConfig();

            Function<List<PplForecastConfigSpikeThresholdDO>, Boolean> callIfMatch =
                    (List<PplForecastConfigSpikeThresholdDO> config) -> {
                        config = ListUtils.filter(config, o -> o.getThreshold() != null && o.getThreshold() > 0);
                        if (!config.isEmpty()) {
                            for (PplForecastConfigSpikeThresholdDO c : config) {
                                Boolean apply = params.getMatchConfigFunc().apply(spikeDTOList, c);
                                if (apply) {
                                    return true;
                                }
                            }
                        }
                        return false;
                    };

            // 1)先找机型和地域都匹配的
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> Objects.equals(o.getCommonInstanceType(), instanceType)
                                && Objects.equals(o.getRegionName(), regionName));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }

            // 2)再找机型和国家都匹配的
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> Objects.equals(o.getCommonInstanceType(), instanceType)
                                && Objects.equals(o.getCustomhouseTitle(), customhouseTitle));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }

            // 3)再找机型匹配，但是国家和地域都是空的
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> Objects.equals(o.getCommonInstanceType(), instanceType)
                                && isAll(o.getRegionName()) && isAll(o.getCustomhouseTitle()));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }

            // 4)再找机型是空的，但是地域匹配
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> isAll(o.getCommonInstanceType())
                                && Objects.equals(o.getRegionName(), regionName));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }

            // 5)再找机型是空的，但是国家匹配
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> isAll(o.getCommonInstanceType())
                                && Objects.equals(o.getCustomhouseTitle(), customhouseTitle));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }

            // 6)最后是机型、地域、国家都是空的
            {
                List<PplForecastConfigSpikeThresholdDO> config =
                        ListUtils.filter(thresholds, o -> isAll(o.getCommonInstanceType())
                                && isAll(o.getRegionName()) && isAll(o.getCustomhouseTitle()));
                if (callIfMatch.apply(config)) {
                    continue;
                }
            }
        }
    }

    private String insertSpikeToCkAndGetUuid(List<PplForecastInputExcludedSpikeDO> spikes) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        List<PplForecastInputExcludedSpikeCkDO> ckDOList = ListUtils.transform(spikes, o -> o.toCkDO(uuid));
        DBList.ckcldDBHelper.insertBatchWithoutReturnId(ckDOList);

        // 等待5秒后反查以确保ck已完全写入数据
        for (int i = 0; i < 10; i++) {
            try {
                Thread.sleep(5000);
            } catch (InterruptedException ignored) {
            }
            long count = DBList.ckcldDBHelper.getCount(PplForecastInputExcludedSpikeCkDO.class, "where batch_uuid=?",
                    uuid);
            if (count == spikes.size()) {
                break;
            }
        }

        return uuid;
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "createCloudNewMiddleForecastCompute")
    @Transactional("demandTransactionManager")
    public void createCloudNewMiddleForecastCompute(String category, String predictDate,
            SerialIntervalEnum serialInterval,
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum,
            Algorithm algorithm,
            boolean isEnable, boolean transInstanceType,
            PplForecastGroupDimsEnum groupDimsEnum,
            boolean isRemoveSpike) {
        serialInterval = Optional.ofNullable(serialInterval).orElse(SerialIntervalEnum.MONTH);

        Ppl13weekForecastSourceFromEnum sourceFromEnum = getFromSerialInterval(serialInterval);

        PplForecastInputDO pplForecastInputDO = new PplForecastInputDO();
        pplForecastInputDO.setPredictMonth(
                DateUtils.parseLocalDate(predictDate)); // 因为ppl_forecast_input表长期是要废弃了，这里就不纠结于这个字段表达是月份还是日期
        pplForecastInputDO.setSourceType(sourceTypeEnum.getCode());
        pplForecastInputDO.setSourceFrom(sourceFromEnum.getCode());
        DBList.demandDBHelper.insert(pplForecastInputDO);

        // 毛刺剔除处理，先找到毛刺
        String spikeUuid = "";
        List<PplForecastInputExcludedSpikeDO> excludedSpikes = new ArrayList<>();
        if (isRemoveSpike) {
            excludedSpikes = getExcludedSpikesForMiddleNew(predictDate, sourceTypeEnum, billTypeEnum);
            spikeUuid = insertSpikeToCkAndGetUuid(excludedSpikes);
        }

        ForecastInputSqlDTO sqlDTO = new ForecastInputSqlDTO();

        List<DailyZoneAppidGinstypePaymodeApproleDTO> dbData = new ArrayList<>();
        if (serialInterval == SerialIntervalEnum.WEEK) {
            throw new WrongWebParameterException("不支持按周预测");
        } else if (serialInterval == SerialIntervalEnum.MONTH) {
            if (billTypeEnum == Ppl13weekForecastBillTypeEnum.MONTHLY_SUBS
                    || billTypeEnum == Ppl13weekForecastBillTypeEnum.ALL) {
                // 查询剔除毛刺之后的数据
                dbData = getMonthDataForMiddleNew(sqlDTO, predictDate, sourceTypeEnum, billTypeEnum, spikeUuid,
                        groupDimsEnum);
            } else {
                throw new WrongWebParameterException("不支持的billTypeEnum:" + billTypeEnum);
            }
        }

        List<PplForecastInputDetailDO> details = transFromDbData(pplForecastInputDO, dbData);
        if (transInstanceType) {
            details = transInstanceType(details);
        }
        DBList.demandDBHelper.insertBatchWithoutReturnId(details);

        Ppl13weekForecastRangeEnumDTO rangeEnums = new Ppl13weekForecastRangeEnumDTO();
        rangeEnums.setBillTypeEnum(billTypeEnum)
                .setResourcePool(Ppl13weekForecastResourcePoolEnum.PUBLIC)
                .setCustomerScope(Ppl13weekForecastCustomerScopeEnum.MIDDLE)
                .setSourceTypeEnum(sourceTypeEnum)
                .setSourceFromEnum(sourceFromEnum);

        int predictN = serialInterval.getPredictN();

        Long taskId = ppl13weekPredictService.predict(rangeEnums,
                Ppl13weekForecastProductEnum.CVM,
                category, pplForecastInputDO.getId(), spikeUuid, algorithm.getName().getName(),
                Strings.join(",", algorithm.getParams()), predictN, isEnable, serialInterval, sqlDTO, null);

        // 更新一下taskId里的group by dims
        PplForecastPredictTaskDO task = DBList.demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        task.setInputGroupDims(groupDimsEnum.getCode());
        DBList.demandDBHelper.update(task);

        details.forEach((o) -> o.setTaskId(taskId));
        String updateSql = "update ppl_forecast_input_detail set task_id=? where input_id in(?)";
        DBList.demandDBHelper.executeRaw(updateSql, taskId, pplForecastInputDO.getId());

        // 将剔除的毛刺保存起来
        if (isRemoveSpike) {
            ListUtils.forEach(excludedSpikes, o -> o.setTaskId(taskId));
            DBList.demandDBHelper.insertBatchWithoutReturnId(excludedSpikes);
        }
    }

    /*
     * 返回35个大客户的执行量
     */
    @HiSpeedCache(expireSecond = 300, keyScript = "args[0]+args[1]+args[2]+args[3]")
    @Override
    public List<DailyZoneAppidGinstypePaymodeApproleWithCustomerDTO> getCloudWhole35HeadCustomer(
            PplForecastPredictTaskDO task, String appendWhereSql, Map<String, Object> appendParams,
            Boolean isShowHeaderCustomerDetail) {

        List<String> publicZoneName = getPublicZoneName();
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        List<String> bizRangeType = getConditionValue(Ppl13weekForecastSourceTypeEnum.getByCode(task.getSourceType()));
        List<String> paymodeRangeType =
                getConditionValue(Ppl13weekForecastBillTypeEnum.getByCode(task.getBillType()));

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/whole/common_condition.sql");
        commonCondition = commonCondition.replace("${headCustomerCondition}",
                getHeadCustomerNameConditionWithoutIeggUin(false)
                /*getHeadCustomerNameCondition(false)*/);

        String sqlCondition = ORMUtils.getSql("/sql/ppl13week_forecast/whole/monthly_data_condition.sql");
        sqlCondition = sqlCondition.replace("${commonCondition}", commonCondition);
        sqlCondition = sqlCondition.replace("${excludeSpike}", ""); // 不含毛刺
        sqlCondition += "\n" + appendWhereSql;

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/whole/monthly_data_with_customer.sql");

        // 去掉性能优化的range
        sql = sql.replace("${dateRange}", "");

        sql = sql.replace("${CONDITION}", sqlCondition);
        // 处理执行量的group by
        PplForecastGroupDimsEnum groupDimsEnum = PplForecastGroupDimsEnum.getByCode(task.getInputGroupDims());
        String sqlColumnJoinByComma = PplForecastGroupDimsEnum.getSqlColumnJoinByComma(groupDimsEnum);
        sql = sql.replace("${GROUP_BY_DIM}", sqlColumnJoinByComma);

        LocalDate start = LocalDate.of(2023, 1, 1);
        LocalDate predictDate = task.getInputDateEnd().plusDays(1);

        List<LocalDate> monthEndDates = new ArrayList<>();
        while (start.isBefore(predictDate)) {
            monthEndDates.add(start.withDayOfMonth(start.lengthOfMonth()));
            start = start.plusMonths(1);
        }
        sql = sql.replace("/*${WEB_CONDITION}*/", " and stat_time in (:predictDateWeb) ");

        if (isShowHeaderCustomerDetail != null && isShowHeaderCustomerDetail) {
            sql = sql.replace("${GROUP_BY_CUSTOMER1}", ",uin");
            sql = sql.replace("${GROUP_BY_CUSTOMER2}", ",uin1");
        } else {
            sql = sql.replace("${GROUP_BY_CUSTOMER1}", "");
            sql = sql.replace("${GROUP_BY_CUSTOMER2}", "");
        }

        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> params = new HashMap<>(appendParams);

        params.put("publicZoneName", publicZoneName);
        params.put("bizRangeType", bizRangeType);
        params.put("predictDate", predictDate);
        params.put("predictDateWeb", monthEndDates);
        params.put("instanceTypeBlacklist", allBlackInstanceType);
        params.put("paymodeRangeType", paymodeRangeType);

        return DBList.ckcldStdCrpDBHelper.getRaw(
                DailyZoneAppidGinstypePaymodeApproleWithCustomerDTO.class, sql, params);
    }


    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "createCloudWholeForecastCompute")
    @Transactional("demandTransactionManager")
    public void createCloudWholeForecastCompute(Ppl13weekForecastProductEnum product,
            String category, String predictDate,
            SerialIntervalEnum serialInterval,
            Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum,
            Algorithm algorithm,
            boolean isEnable, boolean transInstanceType,
            PplForecastGroupDimsEnum groupDimsEnum,
            boolean isRemoveSpike,
            String customSql,
            Integer forecastScope,
            String retAlgorithmParams,
            Integer predictN,
            List<String> extraZones,
            List<String> customHeadCustomers,
            Integer customSpikeThreshold,
            Ppl13weekForecastCdbMemGroupEnum cdbMemGroup) {

        serialInterval = Optional.ofNullable(serialInterval).orElse(SerialIntervalEnum.MONTH);
        Ppl13weekForecastSourceFromEnum sourceFromEnum = getFromSerialInterval(serialInterval);

        PplForecastInputDO pplForecastInputDO = new PplForecastInputDO();
        // 因为ppl_forecast_input表长期是要废弃了，这里就不纠结于这个字段表达是月份还是日期
        pplForecastInputDO.setPredictMonth(DateUtils.parseLocalDate(predictDate));
        pplForecastInputDO.setSourceType(sourceTypeEnum.getCode());
        pplForecastInputDO.setSourceFrom(sourceFromEnum.getCode());
        DBList.demandDBHelper.insert(pplForecastInputDO);

        // 毛刺剔除处理，先找到毛刺
        String spikeUuid = "";
        List<PplForecastInputExcludedSpikeDO> excludedSpikes = new ArrayList<>();
        if (isRemoveSpike) {
            excludedSpikes = getExcludedSpikesForWhole(product, predictDate, sourceTypeEnum, billTypeEnum,
                    customHeadCustomers, customSpikeThreshold);
            spikeUuid = insertSpikeToCkAndGetUuid(excludedSpikes);
        }

        ForecastInputSqlDTO sqlDTO = new ForecastInputSqlDTO();

        List<DailyZoneAppidGinstypePaymodeApproleDTO> dbData = new ArrayList<>();
        if (serialInterval == SerialIntervalEnum.WEEK) {
            throw new WrongWebParameterException("暂不支持按周预测");
        } else if (serialInterval == SerialIntervalEnum.MONTH) {
            if (billTypeEnum == Ppl13weekForecastBillTypeEnum.MONTHLY_SUBS
                    || billTypeEnum == Ppl13weekForecastBillTypeEnum.ALL) {
                // 查询剔除毛刺之后的数据
                dbData = getMonthDataForWhole(product, sqlDTO, predictDate, sourceTypeEnum, billTypeEnum,
                        spikeUuid, groupDimsEnum, customSql, forecastScope, extraZones,
                        customHeadCustomers,cdbMemGroup);
            } else {
                throw new WrongWebParameterException("不支持的billTypeEnum:" + billTypeEnum);
            }
        }

        List<PplForecastInputDetailDO> details = transFromDbData(pplForecastInputDO, dbData);
        if (transInstanceType) {
            if (product != Ppl13weekForecastProductEnum.CDB && product != Ppl13weekForecastProductEnum.CBS) {
                details = transInstanceType(details);
            }
        }
        DBList.demandDBHelper.insertBatchWithoutReturnId(details);

        Ppl13weekForecastRangeEnumDTO rangeEnums = new Ppl13weekForecastRangeEnumDTO();
        rangeEnums.setBillTypeEnum(billTypeEnum)
                .setResourcePool(Ppl13weekForecastResourcePoolEnum.PUBLIC)
                .setCustomerScope(Ppl13weekForecastCustomerScopeEnum.ALL)
                .setSourceTypeEnum(sourceTypeEnum)
                .setSourceFromEnum(sourceFromEnum);

        if (predictN == null || predictN <= 0) {
            predictN = serialInterval.getPredictN();
        }

        Long taskId = ppl13weekPredictService.predict(rangeEnums,
                product == null ? Ppl13weekForecastProductEnum.CVM : product,
                category, pplForecastInputDO.getId(), spikeUuid, algorithm.getName().getName(),
                Strings.join(",", algorithm.getParams()), predictN, isEnable, serialInterval, sqlDTO,
                retAlgorithmParams);

        // 更新一下taskId里的group by dims
        PplForecastPredictTaskDO task = DBList.demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        task.setInputGroupDims(groupDimsEnum == null ? "" : groupDimsEnum.getCode());
        DBList.demandDBHelper.update(task);

        details.forEach((o) -> o.setTaskId(taskId));
        String updateSql = "update ppl_forecast_input_detail set task_id=? where input_id in(?)";
        DBList.demandDBHelper.executeRaw(updateSql, taskId, pplForecastInputDO.getId());

        // 将剔除的毛刺保存起来
        if (isRemoveSpike) {
            ListUtils.forEach(excludedSpikes, o -> o.setTaskId(taskId));
            DBList.demandDBHelper.insertBatchWithoutReturnId(excludedSpikes);
        }
    }

    @Override
    @Synchronized(throwExceptionIfNotGetLock = false)
    @TaskLog(taskName = "createZiyanForecastCompute")
    @Transactional("demandTransactionManager")
    public void createZiyanForecastCompute(String category, String predictDate,
            SerialIntervalEnum serialInterval, Algorithm algorithm,
            boolean isEnable, boolean transInstanceType, boolean isByArea,
            int conditionType, int initConditionType, int predictN) {

        serialInterval = Optional.ofNullable(serialInterval).orElse(SerialIntervalEnum.MONTH);
        Ppl13weekForecastSourceFromEnum sourceFromEnum = getFromSerialInterval(serialInterval);

        PplForecastInputDO pplForecastInputDO = new PplForecastInputDO();
        pplForecastInputDO.setPredictMonth(DateUtils.parseLocalDate(predictDate));
        pplForecastInputDO.setSourceType("全量(不含运营资源中心和算力中心)");
        pplForecastInputDO.setSourceFrom(sourceFromEnum.getCode());
        DBList.demandDBHelper.insert(pplForecastInputDO);

        ForecastInputSqlDTO sqlDTO = new ForecastInputSqlDTO();

        List<ZiyanDemandOriginDataDTO> dbData = new ArrayList<>();
        if (serialInterval == SerialIntervalEnum.WEEK) {
            // TODO 暂时不支持按周预测
        } else {
            dbData = getMonthDataForZiyan(sqlDTO, predictDate, isByArea, conditionType, initConditionType);
        }

        List<PplForecastInputDetailDO> details = transFromDbDataForZiyan(pplForecastInputDO.getId(), dbData);
        if (transInstanceType) {
            details = transInstanceTypeForZiyan(details);
        }
        DBList.demandDBHelper.insertBatchWithoutReturnId(details);

        Ppl13weekForecastRangeEnumDTO rangeEnums = new Ppl13weekForecastRangeEnumDTO();
        rangeEnums.setBillTypeEnum(Ppl13weekForecastBillTypeEnum.ALL)
                .setResourcePool(Ppl13weekForecastResourcePoolEnum.ZIYAN)
                .setCustomerScope(Ppl13weekForecastCustomerScopeEnum.ALL)
                .setSourceFromEnum(sourceFromEnum);

        Long taskId = ppl13weekPredictService.predict(rangeEnums,
                Ppl13weekForecastProductEnum.CVM,
                category, pplForecastInputDO.getId(), "", algorithm.getName().getName(),
                Strings.join(",", algorithm.getParams()), predictN, isEnable, serialInterval, sqlDTO, null);

        // 更新一下task相关信息
        PplForecastPredictTaskDO task = DBList.demandDBHelper.getByKey(PplForecastPredictTaskDO.class, taskId);
        task.setSourceType("全量(不含运营资源中心和算力中心)");
        DBList.demandDBHelper.update(task);

        details.forEach((o) -> o.setTaskId(taskId));
        String updateSql = "update ppl_forecast_input_detail set task_id=? where input_id in(?)";
        DBList.demandDBHelper.executeRaw(updateSql, taskId, pplForecastInputDO.getId());


    }

    /**
     * 自研的机型收敛，这个逻辑放到代码来做也好，预留一个比较好做的实现点：可以记录下来收敛时的明细，这里暂不记录 mark
     *
     * 2024-01-09: 这里的收揽没有分为两步来做，第一步是关联上机型族的明细数据， 第二步是 group by
     * 拆分只需要关联的部分的数据
     */
    private List<PplForecastInputDetailDO> transInstanceTypeForZiyan(List<PplForecastInputDetailDO> details) {
        // 目前没有黑名单机制，如果有，则在这里filter过滤掉，不要在收敛之后再去过滤
        String sql = "select distinct CvmInstanceTypeCode,CvmInstanceGroup from bas_obs_cloud_cvm_type";
        Map<String, String> mapping = ORMUtils.db(obsDBHelper).getKVMap(sql);
        // 2024-06-12， 统一 I6 的机型族配置， 企未： I6 归属
        mapping.put("I6", "高IO型");

        Map<String, List<PplForecastInputDetailDO>> detailsMap = ListUtils.toMapList(details,
                o -> StringTools.join("&", mapping.getOrDefault(o.getGinsFamily(), o.getGinsFamily()),
                        o.getStatTime(), o.getRegionName(), o.getForecastSeqType()),
                o -> o);
        return ListUtils.transform(detailsMap.values(), o -> {
            PplForecastInputDetailDO first = JSON.clone(o.get(0)); // 不要影响原值
            first.setGinsFamily(mapping.getOrDefault(first.getGinsFamily(), first.getGinsFamily()));
            BigDecimal coreNum = NumberUtils.sum(o, PplForecastInputDetailDO::getCoreNum);
            BigDecimal diffCoreNum = NumberUtils.sum(o, PplForecastInputDetailDO::getDiffCoreNum);
            first.setCoreNum(coreNum);
            first.setDiffCoreNum(diffCoreNum);
            return first;
        });
    }

    @Override
    public List<PplForecastInputDetailDO> transInstanceType(List<PplForecastInputDetailDO> details) {

        HashMap<String, List<String>> configMap = ppl13weekCommonDataAccess.getConvergedToRawInstanceTypesMap();

        List<String> blackList = configMap.getOrDefault("黑名单", Lang.list());
        configMap.remove("黑名单");

        // 过滤掉黑名单
        details = details.stream().filter((o) -> !blackList.contains(o.getGinsFamily()))
                .collect(Collectors.toList());

        //details ginsFamily字段， 将 configMap value list 一类全部转为 configMap 的 key
        //相同 value 中的维度要合成一条， diffCoreNum 和 coreNum 相加
        // Create a map to store the aggregated results
        Map<String, PplForecastInputDetailDO> resultMap = new HashMap<>();

        // Create a reverse lookup map for configMap
        Map<String, String> reverseConfigMap = new HashMap<>();
        for (Map.Entry<String, List<String>> entry : configMap.entrySet()) {
            for (String ginsFamily : entry.getValue()) {
                reverseConfigMap.put(ginsFamily, entry.getKey());
            }
        }

        for (PplForecastInputDetailDO detail : details) {
            String ginsFamily = detail.getGinsFamily();

            // Update ginsFamily using reverseConfigMap
            if (reverseConfigMap.containsKey(ginsFamily)) {
                detail.setGinsFamily(reverseConfigMap.get(ginsFamily));
            }

            String compositeKey =
                    Strings.join("@",
                            detail.getInputId(),
                            detail.getGinsFamily(), detail.getForecastSeqType(),
                            detail.getRegion(), detail.getRegionName(),
                            detail.getStatTime());
            if (resultMap.containsKey(compositeKey)) {
                PplForecastInputDetailDO existingDetail = resultMap.get(compositeKey);
                existingDetail.setCoreNum(existingDetail.getCoreNum().add(detail.getCoreNum()));
                existingDetail.setDiffCoreNum(existingDetail.getDiffCoreNum().add(detail.getDiffCoreNum()));
            } else {
                resultMap.put(compositeKey, detail);
            }
        }

        return new ArrayList<>(resultMap.values());
    }


    private List<PplForecastInputDetailLatestForMrpDO> transInstanceType1
            (List<PplForecastInputDetailLatestForMrpDO> details) {

        List<String> blackList = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        // 过滤掉黑名单
        details = details.stream().filter((o) -> !blackList.contains(o.getGinsFamily()))
                .collect(Collectors.toList());

        //details ginsFamily字段， 将 configMap value list 一类全部转为 configMap 的 key
        //相同 value 中的维度要合成一条， diffCoreNum 和 coreNum 相加

        // Create a map to store the aggregated results
        Map<String, PplForecastInputDetailLatestForMrpDO> resultMap = new HashMap<>();

        // Create a reverse lookup map for configMap
        Map<String, String> reverseConfigMap = ppl13weekCommonDataAccess.getRawToConvergedInstanceTypeMap();

        for (PplForecastInputDetailLatestForMrpDO detail : details) {
            String ginsFamily = detail.getGinsFamily();

            // Update ginsFamily using reverseConfigMap
            if (reverseConfigMap.containsKey(ginsFamily)) {
                detail.setGinsFamily(reverseConfigMap.get(ginsFamily));
            }

            String compositeKey =
                    Strings.join("@",
                            detail.getGinsFamily(), detail.getType(),
                            detail.getRegionName(),
                            detail.getStatTime());
            if (resultMap.containsKey(compositeKey)) {
                PplForecastInputDetailLatestForMrpDO existingDetail = resultMap.get(compositeKey);
                existingDetail.setCoreNum(existingDetail.getCoreNum().add(detail.getCoreNum()));
                existingDetail.setDiffCoreNum(existingDetail.getDiffCoreNum().add(detail.getDiffCoreNum()));
            } else {
                resultMap.put(compositeKey, detail);
            }
        }
        // Convert the resultMap values to a list and return
        return new ArrayList<>(resultMap.values());
    }

    /**
     * 自研的月度数据
     */
    private List<ZiyanDemandOriginDataDTO> getMonthDataForZiyan(ForecastInputSqlDTO sqlDTO, String predictDate,
            boolean isByArea, int conditionType, int initConditionType) {

        String conditionSql = ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan_new_condition.sql");
        if (initConditionType == 1) {
            conditionSql = ORMUtils.getSql(
                    "/sql/ppl13week_forecast/ziyan/monthly_ziyan_main_region_instancetype_new_condition.sql");
        }

        // 0 是不做额外限制
        // 1 不要春保
        // 2 不要机房裁撤
        // 3 两个都不要
        // 4 只要春保
        // 5 只要裁撤
        // 6 只要春保和裁撤
        // 7 只要常规项目的

        String chunbaoSql = " and project_name in ('2020春节保障', '2021春节保障', '2022春节保障', '2023春节保障',\n"
                + "                               '2024春节保障', '2024机房裁撤','2025春节保障') ";
        String caicheSql = " and project_name in ('机房裁撤')";
        String chunbaoCaicheSql = " and project_name in ('2020春节保障', '2021春节保障', '2022春节保障', '2023春节保障',\n"
                + "                               '2024春节保障', '2024机房裁撤','2025春节保障','机房裁撤')";
        String excludeChunbaoSql = " and  project_name not in ('2020春节保障', '2021春节保障', '2022春节保障', '2023春节保障',\n"
                + "                               '2024春节保障', '2024机房裁撤','2025春节保障') ";
        String excludeCaicheSql = " and project_name not in ('机房裁撤')";
        String excludeChunbaoCaicheSql = " and project_name not in ('2020春节保障', '2021春节保障', '2022春节保障', '2023春节保障',\n"
                + "                               '2024春节保障', '2024机房裁撤','2025春节保障','机房裁撤')";

        String normalProject = " and project_name in ('常规项目')";

        switch (conditionType) {
            case 1:
                conditionSql += excludeChunbaoSql;
                break;
            case 2:
                conditionSql += excludeCaicheSql;
                break;
            case 3:
                conditionSql += excludeChunbaoCaicheSql;
                break;
            case 4:
                conditionSql += chunbaoSql;
                break;
            case 5:
                conditionSql += caicheSql;
                break;
            case 6:
                conditionSql += chunbaoCaicheSql;
                break;
            case 7:
                conditionSql += normalProject;
            default:
                break;
        }
        sqlDTO.setInputSqlConditionNew(conditionSql);

        String sql = isByArea ?
                ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan_by_area.sql")
                : ORMUtils.getSql("/sql/ppl13week_forecast/ziyan/monthly_ziyan.sql");
        sql = sql.replace("${CONDITION}", conditionSql);

        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> params = new HashMap<>();
        params.put("predictDate", predictDate);
        params.put("predictDate1", LocalDate.parse(predictDate).minusDays(1));

        sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(sql, params));

        return DBList.ckcldyuntiDBHelper.getRaw(ZiyanDemandOriginDataDTO.class, sql, params);
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getMonthDataForMiddleNew(
            ForecastInputSqlDTO sqlDTO,
            String predictDate, Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum, String spikeUuid,
            PplForecastGroupDimsEnum groupDimsEnum) {

        List<String> publicZoneName = getPublicZoneName();
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        List<String> bizRangeType = getConditionValue(sourceTypeEnum);
        List<String> paymodeRangeType = getConditionValue(billTypeEnum);

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/common_condition.sql");
        commonCondition = commonCondition.replace("${headCustomerShortNameCondition}",
                getHeadCustomerNameCondition(true));

        String sqlCondition = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/monthly_data_condition.sql");
        sqlCondition = sqlCondition.replace("${commonCondition}", commonCondition);

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/new_middle/monthly_data.sql");

        // 处理执行量的group by
        String sqlColumnJoinByComma = PplForecastGroupDimsEnum.getSqlColumnJoinByComma(groupDimsEnum);
        sql = sql.replace("${GROUP_BY_DIM}", sqlColumnJoinByComma);

        if (StringTools.isNotBlank(spikeUuid)) {
            sqlCondition = sqlCondition.replace("${excludeSpike}",
                    "and (year,month,uin,zone_name,instance_type) global not in (select year,month,customer_uin,zone_name,instance_type "
                            +
                            " from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)");
        } else {
            sqlCondition = sqlCondition.replace("${excludeSpike}", "");
        }

        sql = sql.replace("${CONDITION}", sqlCondition);

        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> params = new HashMap<>();
        Map<String, String> paramsAsString = new HashMap<>();

        params.put("publicZoneName", publicZoneName);
        paramsAsString.put("publicZoneName",
                Strings.join(",", ListUtils.transform(publicZoneName, o -> "'" + o + "'")));

        params.put("bizRangeType", bizRangeType);
        paramsAsString.put("bizRangeType", Strings.join(",", ListUtils.transform(bizRangeType, o -> "'" + o + "'")));

        params.put("predictDate", predictDate);
        paramsAsString.put("predictDate", "'" + predictDate + "'");

        params.put("instanceTypeBlacklist", allBlackInstanceType);
        paramsAsString.put("instanceTypeBlacklist",
                Strings.join(",", ListUtils.transform(allBlackInstanceType, o -> "'" + o + "'")));

        params.put("paymodeRangeType", paymodeRangeType);
        paramsAsString.put("paymodeRangeType",
                Strings.join(",", ListUtils.transform(paymodeRangeType, o -> "'" + o + "'")));

        if (StringTools.isNotBlank(spikeUuid)) {
            params.put("spikeUuid", spikeUuid);
            paramsAsString.put("spikeUuid", "'" + spikeUuid + "'");
        }

        sqlDTO.setInputSqlNew(fillNamedSql(sql, paramsAsString));
        sqlDTO.setInputSqlConditionNew(fillNamedSql(sqlCondition, paramsAsString));

        return DBList.ckcldStdCrpDBHelper.getRaw(DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getMonthDataForWhole(
            Ppl13weekForecastProductEnum product,
            ForecastInputSqlDTO sqlDTO,
            String predictDate, Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum, String spikeUuid,
            PplForecastGroupDimsEnum groupDimsEnum, String customSql,
            Integer forecastScope, List<String> extraZones,
            List<String> customHeadCustomers, Ppl13weekForecastCdbMemGroupEnum cdbMemGroup) {

        if (StringTools.isNotBlank(customSql)) {
            Map<String, Object> params = new HashMap<>();
            params.put("predictDate", predictDate);

            sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(customSql, params));
            return DBList.ckcldStdCrpDBHelper.getRaw(DailyZoneAppidGinstypePaymodeApproleDTO.class, customSql, params);
        }

        if (product == null || Ppl13weekForecastProductEnum.CVM == product) {
            return getCvmMonthDataForWhole(sqlDTO, predictDate, sourceTypeEnum, billTypeEnum, spikeUuid, groupDimsEnum,
                    forecastScope, extraZones, customHeadCustomers);
        } else if (product == Ppl13weekForecastProductEnum.EMR) {
            return getEmrMonthData(sqlDTO, predictDate, spikeUuid);
        } else if (product == Ppl13weekForecastProductEnum.EKS) {
            return getEksMonthData(sqlDTO, predictDate, spikeUuid);
        } else if (product == Ppl13weekForecastProductEnum.CDB) {
            return getCdbMonthData(sqlDTO, predictDate, spikeUuid,cdbMemGroup);
        } else if (product == Ppl13weekForecastProductEnum.CBS) {
            return getCbsMonthData(sqlDTO, predictDate, spikeUuid);
        }

        return null;
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getEmrMonthData(ForecastInputSqlDTO sqlDTO,
            String predictDate, String spikeUuid) {
        Map<String, Object> params = new HashMap<>();

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/emr/common_condition.sql");
        StringBuilder sqlCondition = new StringBuilder(commonCondition);
        // 添加毛刺剔除
        if (StringTools.isNotBlank(spikeUuid)) {
            sqlCondition.append(
                    "and (year,month,(case when customer_short_name='(空值)' then concat('uin:',toString(uin)) else customer_short_name end),region_name,instance_type) global not in (select year,month,customer_short_name,region_name,instance_type "
                            + " from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)");
            params.put("spikeUuid", spikeUuid);
        }

        // 剔除不参与预测的机型
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        if (ListUtils.isNotEmpty(allBlackInstanceType)) {
            sqlCondition.append(" and instance_type not in (");
            sqlCondition.append(StringTools.join(",", ListUtils.transform(allBlackInstanceType, o -> "'" + o + "'")));
            sqlCondition.append(") ");
        }

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/emr/monthly_data.sql");
        sql = sql.replace("${CONDITION}", sqlCondition.toString());
        params.put("predictDate", predictDate);

        sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(sql, params));
        sqlDTO.setInputSqlConditionNew(sqlCondition.toString().replace(":spikeUuid", "'" + spikeUuid + "'"));

        return DBList.ckcldStdCrpDBHelper.getRaw(DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);
    }

    @SuppressWarnings("Convert2MethodRef")
    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getCdbMonthData(ForecastInputSqlDTO sqlDTO,
            String predictDate, String spikeUuid, Ppl13weekForecastCdbMemGroupEnum cdbMemGroup) {

        Map<String, Object> params = new HashMap<>();
        StringBuilder sqlCondition = new StringBuilder();

        // 添加毛刺剔除
        if (StringTools.isNotBlank(spikeUuid)) {
            String str =
                    " and (year,month,toString(uin),zone_name,toString(instance_model_mem)) global not in (select year,month,customer_uin,zone_name,instance_type from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)";
            sqlCondition.append( str);
            params.put("spikeUuid", spikeUuid);
        }

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/cdb/monthly_data.sql");
        sql = sql.replace("${CONDITION}", sqlCondition.toString());
        sql = sql.replace("${memGroup}", getMemGroupColumn(cdbMemGroup));
        params.put("predictDate", predictDate);

        sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(sql, params));
        sqlDTO.setInputSqlConditionNew(sqlCondition.toString().replace(":spikeUuid", "'" + spikeUuid + "'"));

        List<DailyZoneAppidGinstypePaymodeApproleDTO> raw = DBList.ckcldStdCrpDBHelper.getRaw(
                DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);

        return raw;
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getCbsMonthData(ForecastInputSqlDTO sqlDTO,
                                                                          String predictDate, String spikeUuid) {

        Map<String, Object> params = new HashMap<>();
        StringBuilder sqlCondition = new StringBuilder();

        // 添加毛刺剔除
        if (StringTools.isNotBlank(spikeUuid)) {
            String str =
                    " and (year,month,toString(uin),zone_name,disk_volume_type_name) global not in (select year,month,customer_uin,zone_name,instance_type from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)";
            sqlCondition.append(str);
            params.put("spikeUuid", spikeUuid);
        }

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/cbs/monthly_data.sql");
        sql = sql.replace("${CONDITION}", sqlCondition.toString());
        params.put("predictDate", predictDate);

        sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(sql, params));
        sqlDTO.setInputSqlConditionNew(sqlCondition.toString().replace(":spikeUuid", "'" + spikeUuid + "'"));

        List<DailyZoneAppidGinstypePaymodeApproleDTO> raw = DBList.ckcldStdCrpDBHelper.getRaw(
                DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);
        return raw;
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getEksMonthData(ForecastInputSqlDTO sqlDTO,
            String predictDate, String spikeUuid) {
        Map<String, Object> params = new HashMap<>();

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/eks/common_condition.sql");
        StringBuilder sqlCondition = new StringBuilder(commonCondition);
        // 添加毛刺剔除
        if (StringTools.isNotBlank(spikeUuid)) {
            sqlCondition.append(
                    " and (year,month,(case when customer_short_name='(空值)' then concat('uin:',toString(uin)) else customer_short_name end),zone_name,instance_type) global not in (select year,month,customer_short_name,zone_name,instance_type "
                            + " from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)");
            params.put("spikeUuid", spikeUuid);
        }

        // 剔除不参与预测的机型
        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();
        if (ListUtils.isNotEmpty(allBlackInstanceType)) {
            sqlCondition.append(" and instance_type not in (");
            sqlCondition.append(StringTools.join(",", ListUtils.transform(allBlackInstanceType, o -> "'" + o + "'")));
            sqlCondition.append(") ");
        }

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/eks/monthly_data.sql");
        sql = sql.replace("${CONDITION}", sqlCondition.toString());
        params.put("predictDate", predictDate);

        sqlDTO.setInputSqlNew(SQLAssemblyUtils.assembleSql(sql, params));
        sqlDTO.setInputSqlConditionNew(sqlCondition.toString().replace(":spikeUuid", "'" + spikeUuid + "'"));

        return DBList.ckcldStdCrpDBHelper.getRaw(DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);
    }

    private List<DailyZoneAppidGinstypePaymodeApproleDTO> getCvmMonthDataForWhole(
            ForecastInputSqlDTO sqlDTO,
            String predictDate, Ppl13weekForecastSourceTypeEnum sourceTypeEnum,
            Ppl13weekForecastBillTypeEnum billTypeEnum, String spikeUuid,
            PplForecastGroupDimsEnum groupDimsEnum,
            Integer forecastScope, List<String> extraZones,
            List<String> customHeadCustomers) {

        List<String> publicZoneName = getPublicZoneName();
        if (extraZones != null) {
            publicZoneName.addAll(ListUtils.filter(extraZones, StringTools::isNotBlank));
        }

        List<String> allBlackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        List<String> bizRangeType = getConditionValue(sourceTypeEnum);
        List<String> paymodeRangeType = getConditionValue(billTypeEnum);

        Set<String> headCustomerShortName = null;
        if (customHeadCustomers != null) {
            headCustomerShortName = new HashSet<>();
            if (customHeadCustomers.isEmpty()) {
                headCustomerShortName.add("THIS_IS_NOT_EXIST_CUSTOMER_NAME"); // 故意设置一个不存在的客户名称，这样不管in还是not in都合适
            } else {
                headCustomerShortName.addAll(customHeadCustomers);
            }
        } else {
            headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();
        }

        String commonCondition = ORMUtils.getSql("/sql/ppl13week_forecast/whole/common_condition.sql");
        if (forecastScope == 1) {
            commonCondition = commonCondition.replace("${headCustomerCondition}",
                    "and customer_short_name not in (:headCustomerShortName)");
        } else if (forecastScope == 2) {
            commonCondition = commonCondition.replace("${headCustomerCondition}",
                    "and customer_short_name in (:headCustomerShortName)");
        } else if (forecastScope == 3) {
            commonCondition = commonCondition.replace("${headCustomerCondition}",
                    "and (customer_short_name in (:headCustomerShortName)"); // 留着拼凑毛刺or
        }

        String sqlCondition = ORMUtils.getSql("/sql/ppl13week_forecast/whole/monthly_data_condition.sql");
        sqlCondition = sqlCondition.replace("${commonCondition}", commonCondition);

        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/whole/monthly_data.sql");

        // 处理执行量的group by
        String sqlColumnJoinByComma = PplForecastGroupDimsEnum.getSqlColumnJoinByComma(groupDimsEnum);
        sql = sql.replace("${GROUP_BY_DIM}", sqlColumnJoinByComma);

        if (forecastScope == 1) {
            if (StringTools.isNotBlank(spikeUuid)) {
                sqlCondition = sqlCondition.replace("${excludeSpike}",
                        "and (year,month,uin,zone_name,instance_type) global not in (select year,month,customer_uin,zone_name,instance_type "
                                +
                                " from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid)");
            } else {
                sqlCondition = sqlCondition.replace("${excludeSpike}", "");
            }
        } else if (forecastScope == 2) {
            sqlCondition = sqlCondition.replace("${excludeSpike}", "");
        } else if (forecastScope == 3) {
            sqlCondition = sqlCondition.replace("${excludeSpike}",
                    "or (year,month,uin,zone_name,instance_type) global in (select year,month,customer_uin,zone_name,instance_type "
                            +
                            " from cloud_demand.ppl_forecast_input_excluded_spike where batch_uuid=:spikeUuid) )"); // 这个连上上面的or条件
        }

        sql = sql.replace("${CONDITION}", sqlCondition);

        // 将参数转换成Map，增加可读性和复用性
        Map<String, Object> params = new HashMap<>();
        Map<String, String> paramsAsString = new HashMap<>();

        params.put("publicZoneName", publicZoneName);
        paramsAsString.put("publicZoneName",
                Strings.join(",", ListUtils.transform(publicZoneName, o -> "'" + o + "'")));

        params.put("bizRangeType", bizRangeType);
        paramsAsString.put("bizRangeType", Strings.join(",", ListUtils.transform(bizRangeType, o -> "'" + o + "'")));

        params.put("predictDate", predictDate);
        paramsAsString.put("predictDate", "'" + predictDate + "'");

        params.put("instanceTypeBlacklist", allBlackInstanceType);
        paramsAsString.put("instanceTypeBlacklist",
                Strings.join(",", ListUtils.transform(allBlackInstanceType, o -> "'" + o + "'")));

        params.put("paymodeRangeType", paymodeRangeType);
        paramsAsString.put("paymodeRangeType",
                Strings.join(",", ListUtils.transform(paymodeRangeType, o -> "'" + o + "'")));

        params.put("headCustomerShortName", headCustomerShortName);
        paramsAsString.put("headCustomerShortName",
                Strings.join(",", ListUtils.transform(headCustomerShortName, o -> "'" +
                        o.replace("'", "''") + "'")));

        if (StringTools.isNotBlank(spikeUuid)) {
            params.put("spikeUuid", spikeUuid);
            paramsAsString.put("spikeUuid", "'" + spikeUuid + "'");
        }

        sqlDTO.setInputSqlNew(fillNamedSql(sql.replace("${dateRange}", ""), paramsAsString)); // 这里保存到db的故意不要分批查询
        sqlDTO.setInputSqlConditionNew(fillNamedSql(sqlCondition, paramsAsString));

        // 因为查询量大，所以这里按时间进行分配查询
        List<DateRange> dateRanges = getDateRanges();
        List<DailyZoneAppidGinstypePaymodeApproleDTO> result = new ArrayList<>();
        for (DateRange dateRange : dateRanges) {

            String dateRangeSql =
                    " and stat_time between '" + dateRange.getStartDate() + "' and '" + dateRange.getEndDate() + "' ";
            String sql2 = sql.replace("${dateRange}", dateRangeSql);

            List<DailyZoneAppidGinstypePaymodeApproleDTO> tmp = DBList.ckcldStdCrpDBHelper.getRaw(
                    DailyZoneAppidGinstypePaymodeApproleDTO.class, sql2, params);
            result.addAll(tmp);
        }

        return result;
    }


    private String fillNamedSql(String sql, Map<String, String> paramsAsString) {
        for (Map.Entry<String, String> entry : paramsAsString.entrySet()) {
            sql = sql.replace(":" + entry.getKey(), entry.getValue());
        }
        return sql;
    }

    @NotNull
    private List<PplForecastInputDetailDO> transFromDbData(PplForecastInputDO pplForecastInputDO,
            List<DailyZoneAppidGinstypePaymodeApproleDTO> allData) {
        List<PplForecastInputDetailDO> details = Lang.list();
        for (DailyZoneAppidGinstypePaymodeApproleDTO leftDatum : allData) {
            PplForecastInputDetailDO newDatum = transToDetail(leftDatum);
            PplForecastInputDetailDO retDatum = JSON.clone(newDatum);

            newDatum.setInputId(pplForecastInputDO.getId());
            newDatum.setType(PplForecastTypeEnum.NEW.getType());
            newDatum.setDiffCoreNum(leftDatum.getNewDiff());
            retDatum.setInputId(pplForecastInputDO.getId());
            retDatum.setType(PplForecastTypeEnum.RET.getType());
            retDatum.setDiffCoreNum(leftDatum.getRetDiff());

            details.add(newDatum);
            details.add(retDatum);
        }
        return details;
    }

    /**
     * 目前只有新增
     */
    private List<PplForecastInputDetailDO> transFromDbDataForZiyan(Long pplForecastInputId,
            List<ZiyanDemandOriginDataDTO> allData) {
        List<PplForecastInputDetailDO> details = Lang.list();
        for (ZiyanDemandOriginDataDTO data : allData) {
            PplForecastInputDetailDO newDatum = new PplForecastInputDetailDO();
            newDatum.setInputId(pplForecastInputId);
            newDatum.setStatTime(data.getLastDay());
            newDatum.setYear(data.getYear());
            newDatum.setMonth(data.getMonth());
            newDatum.setCustomhouseTitle(""); // 自研的先不要境内外
            newDatum.setGinsFamily(data.getGinsFamily());
            newDatum.setRegion(data.getRegion());
            newDatum.setRegionName(data.getRegionName());
            newDatum.setCustomhouseTitle(data.getCustomhouseTitle());
            newDatum.setCoreNum(BigDecimal.ZERO); // 自研目前不看存量的数值
            newDatum.setType(PplForecastTypeEnum.NEW.getType());
            newDatum.setDiffCoreNum(data.getNewDiff());
            details.add(newDatum);
        }
        return details;
    }

    public PplForecastInputDetailDO transToDetail(DailyZoneAppidGinstypePaymodeApproleDTO source) {
        PplForecastInputDetailDO pplForecastInputDetailDO = new PplForecastInputDetailDO();
        pplForecastInputDetailDO.setYear(source.getYear());
        pplForecastInputDetailDO.setMonth(source.getMonth());
        pplForecastInputDetailDO.setStatTime(source.getLastDay());
        pplForecastInputDetailDO.setCustomhouseTitle(source.getCustomhouseTitle());
        pplForecastInputDetailDO.setGinsFamily(source.getGinsFamily());
        pplForecastInputDetailDO.setRegion(source.getRegion());
        pplForecastInputDetailDO.setRegionName(source.getRegionName());
        pplForecastInputDetailDO.setCoreNum(source.getLastDayNum());
        return pplForecastInputDetailDO;
    }

    @Override
    @Transactional("demandTransactionManager")
    @TaskLog(taskName = "genLatestInputDetailForMrpData")
    public void genLatestInputDetailForMrpData() {
        demandDBHelper.executeRaw("truncate ppl_forecast_input_detail_latest_for_mrp");

        List<String> publicZoneName = getPublicZoneName();
        Set<String> headCustomerShortName = ppl13weekCommonDataAccess.getHeadCustomerShortName();
        // 毛刺的黑名单，threshold<0的机型都是黑名单
        List<String> instanceTypeBlacklist = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        String predictDate = DateUtils.format(new Date(), "yyyy-MM-dd");
        String sql = ORMUtils.getSql("/sql/ppl13week_forecast/whole/monthly_data_cur_month.sql");
        Map<String, Object> params = new HashMap<>();
        params.put("publicZoneName", publicZoneName);
        params.put("predictDate", predictDate);
        params.put("headCustomerShortName", headCustomerShortName);
        params.put("instanceTypeBlacklist", instanceTypeBlacklist);
        List<DailyZoneAppidGinstypePaymodeApproleDTO> raw = DBList.prodReadOnlyCkStdCrpDBHelper.getRaw(
                DailyZoneAppidGinstypePaymodeApproleDTO.class, sql, params);
        List<PplForecastInputDetailLatestForMrpDO> details = transFromDbData1(raw);
        details = transInstanceType1(details);
        DBList.demandDBHelper.insertBatchWithoutReturnId(details);
    }

    private List<PplForecastInputDetailLatestForMrpDO> transFromDbData1(
            List<DailyZoneAppidGinstypePaymodeApproleDTO> latestData) {
        List<PplForecastInputDetailLatestForMrpDO> details = Lang.list();
        for (DailyZoneAppidGinstypePaymodeApproleDTO leftDatum : latestData) {
            PplForecastInputDetailLatestForMrpDO newDatum = transToDetail1(leftDatum);
            PplForecastInputDetailLatestForMrpDO retDatum = JSON.clone(newDatum);
            newDatum.setType(PplForecastTypeEnum.NEW.getType());
            newDatum.setDiffCoreNum(leftDatum.getNewDiff());
            retDatum.setType(PplForecastTypeEnum.RET.getType());
            retDatum.setDiffCoreNum(leftDatum.getRetDiff());
            details.add(newDatum);
            details.add(retDatum);
        }
        return details;
    }

    private PplForecastInputDetailLatestForMrpDO transToDetail1(
            DailyZoneAppidGinstypePaymodeApproleDTO dailyZoneAppidGinstypePaymodeApproleDTO) {
        PplForecastInputDetailLatestForMrpDO pplForecastInputDetailLatestForMrpDO = new PplForecastInputDetailLatestForMrpDO();
        pplForecastInputDetailLatestForMrpDO.setStatTime(dailyZoneAppidGinstypePaymodeApproleDTO.getFirstDay());
        pplForecastInputDetailLatestForMrpDO.setYear(dailyZoneAppidGinstypePaymodeApproleDTO.getYear());
        pplForecastInputDetailLatestForMrpDO.setMonth(dailyZoneAppidGinstypePaymodeApproleDTO.getMonth());
        pplForecastInputDetailLatestForMrpDO.setCustomhouseTitle(
                dailyZoneAppidGinstypePaymodeApproleDTO.getCustomhouseTitle());
        pplForecastInputDetailLatestForMrpDO.setGinsFamily(dailyZoneAppidGinstypePaymodeApproleDTO.getGinsFamily());
        pplForecastInputDetailLatestForMrpDO.setRegionName(dailyZoneAppidGinstypePaymodeApproleDTO.getRegionName());
        pplForecastInputDetailLatestForMrpDO.setCoreNum(dailyZoneAppidGinstypePaymodeApproleDTO.getLastDayNum());
        return pplForecastInputDetailLatestForMrpDO;
    }

    private static List<String> getConditionValue(Ppl13weekForecastSourceTypeEnum sourceTypeEnum) {
        if (sourceTypeEnum == null) {
            return new ArrayList<>();
        }
        List<String> bizRangeType = Lang.list();
        switch (sourceTypeEnum) {
            case INDUSTRY_INNER:
                bizRangeType.add("外部业务");
                bizRangeType.add("内部业务");
                break;
            case INDUSTRY:
                bizRangeType.add("外部业务");
                break;
            case INNER:
                bizRangeType.add("内部业务");
                break;
            default:
                bizRangeType.add("");
        }
        return bizRangeType;
    }

    private static List<String> getConditionValue(Ppl13weekForecastBillTypeEnum billTypeEnum) {
        if (billTypeEnum == null) {
            return new ArrayList<>();
        }
        List<String> billType = Lang.list();
        switch (billTypeEnum) {
            case ALL:
                billType.add("包年包月");
                billType.add("弹性");
                break;
            case MONTHLY_SUBS:
                billType.add("包年包月");
                break;
            case ELASTIC:
                billType.add("弹性");
                break;
            default:
                billType.add("");
        }
        return billType;
    }

    @Override
    @Transactional("demandTransactionManager")
    public void reinitSpikeLatestData() {
        // 特别说明：这个毛刺是给数据行业看板用的，目前只有CVM，因此不要改变这两个方案的名称
        demandDBHelper.delete(PplForecastInputExcludedSpikeLatestDO.class, "where 1=1");
        List<PplForecastInputExcludedSpikeLatestDO> industry = getSpikeLatest("方案705", false);
        List<PplForecastInputExcludedSpikeLatestDO> inner = getSpikeLatest("方案715", true);
        industry.addAll(inner);
        demandDBHelper.insertBatchWithoutReturnId(industry);
    }

    private List<PplForecastInputExcludedSpikeLatestDO> getSpikeLatest(String categoryPrefix, boolean isInner) {
        PplForecastPredictTaskDO task = ppl13weekQueryService.getLastEnabledPredictTask(categoryPrefix, false);
        if (task == null) {
            return new ArrayList<>();
        }
        List<PplForecastInputExcludedSpikeDO> spikes = demandDBHelper.getAll(PplForecastInputExcludedSpikeDO.class,
                "where task_id=?", task.getId());
        return ListUtils.transform(spikes, (spike) -> {
            PplForecastInputExcludedSpikeLatestDO latest = new PplForecastInputExcludedSpikeLatestDO();
            latest.setIsInner(isInner);
            latest.setCreateTime(spike.getCreateTime());
            latest.setUpdateTime(spike.getUpdateTime());
            latest.setTaskId(spike.getTaskId());
            latest.setYear(spike.getYear());
            latest.setMonth(spike.getMonth());
            latest.setIndustryDept(spike.getIndustryDept());
            latest.setCustomerUin(spike.getCustomerUin());
            latest.setCustomerShortName(spike.getCustomerShortName());
            latest.setInstanceType(spike.getInstanceType());
            latest.setRegionName(spike.getRegionName());
            latest.setZoneName(spike.getZoneName());
            latest.setNewCore(spike.getNewCore());
            latest.setRetCore(spike.getRetCore());
            return latest;
        });
    }

}
