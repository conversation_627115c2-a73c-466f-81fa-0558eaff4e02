package cloud.demand.app.modules.p2p.longterm.service;


import cloud.demand.app.modules.p2p.longterm.controller.req.LongtermDemandStatsReq;
import cloud.demand.app.modules.p2p.longterm.controller.resp.DownloadExcelResp;
import cloud.demand.app.modules.p2p.longterm.controller.resp.LongtermDemandStatsResp;
import yunti.boot.web.jsonrpc.JsonrpcParam;

/**
 * 需求波动分析
 */
public interface LongtermStatsService {

    /**
     * 需求分析，拿到所有的明细数据
     */
    LongtermDemandStatsResp getDemandStatsDetail(@JsonrpcParam LongtermDemandStatsReq req);

    /**
     * 需求分析，拿到所有的明细数据 excel 导出
     * 对 getDemandStatsDetail 数据的导出
     *
     * @param req req
     * @return excel
     */
    DownloadExcelResp getDemandStatsDetailExcel(LongtermDemandStatsReq req);
}
