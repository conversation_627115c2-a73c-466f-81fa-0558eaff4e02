package cloud.demand.app.modules.p2p.ppl13week.enums;

import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.nutz.lang.Lang;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum PplProjectTypeEnum {


    /**
     * 项目类型枚举：
     * 1、常规项目
     * 2、重点项目
     */
    NOT_INIT(-1,"未指定"),

    NORMAL(0,"常规项目"),

    IMPORTANCE(1,"重点项目"),

    NOT_CONTAIN(2,"不参与划分"),
    ;

    /**
     * is_spike 字段的值来源
     */
    final private Integer dbCode;
    final private String name;

    public static String getNameByCode(Integer code) {
        for (PplProjectTypeEnum e : PplProjectTypeEnum.values()) {
            if (Objects.equals(code, e.getDbCode())) {
                return e.getName();
            }
        }
        return NOT_CONTAIN.getName();
    }


    public static PplProjectTypeEnum getByName(String name) {
        for (PplProjectTypeEnum e : PplProjectTypeEnum.values()) {
            if (Objects.equals(name, e.getName())) {
                return e;
            }
        }
        return null;
    }



    public static boolean isValidName(String name){
        return Lang.list(NOT_CONTAIN.name,NORMAL.name, IMPORTANCE.name).contains(name);
    }

}
