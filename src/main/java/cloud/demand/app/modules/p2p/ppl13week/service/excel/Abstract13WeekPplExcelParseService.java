package cloud.demand.app.modules.p2p.ppl13week.service.excel;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public abstract class Abstract13WeekPplExcelParseService extends AbstractGeneralPplExcelParseService implements
        PplExcelParseService {

    @Resource
    private PplExcelParseServiceAdapter adapter;

    @PostConstruct
    public void init() {
        adapter.registry(this);
    }

}
