package cloud.demand.app.modules.p2p.ppl13week.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.CustomerTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplDemandTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeEventEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.PplRecordChangeTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("ppl_item_change_record")
public class PplItemChangeRecordDO extends BaseDO {

    @Column(value = "ppl_order")
    private String pplOrder;

    @Column(value = "ppl_id")
    private String pplId;

    @Column(value = "parent_ppl_id")
    private String parentPplId;

    @Column(value = "yunxiao_order_id")
    private String yunxiaoOrderId;

    @Column(value = "yunxiao_detail_id")
    private Long yunxiaoDetailId;

    /** 审批版本id（操作发生时所在的审批版本）*/
    @Column(value = "version_id")
    private Long versionId;

    /**
     * 变更类型
     * @see PplRecordChangeTypeEnum
     */
    @Column(value = "change_type")
    private String changeType;

    /**
     * 变更事件
     * @see PplRecordChangeEventEnum
     */
    @Column(value = "change_event")
    private String changeEvent;

    /** 变化内容概览 */
    @Column(value = "change_overview_json")
    private String changeOverviewJson;

    /** 操作人 */
    @Column(value = "operate_user")
    private String operateUser;

    /** 操作备注 */
    @Column(value = "operate_note")
    private String operateNote;

    @Column(value = "industry_dept")
    private String industryDept;

    @Column(value = "product")
    private String product;

    /**
     * 需求类型
     * @see PplDemandTypeEnum
     * */
    @Column(value = "demand_type")
    private String demandType;

    /** 需求年*/
    @Column(value = "demand_year")
    private Integer demandYear;

    /** 需求月*/
    @Column(value = "demand_month")
    private Integer demandMonth;

    @Column(value = "project_name")
    private String projectName;

    @Column("customer_name")
    private String customerName;

    /** 客户简称 */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户类型
     *  @see CustomerTypeEnum
     *  */
    @Column(value = "customer_type")
    private String customerType;

    @Column(value = "customer_uin")
    private String customerUin;

    @Column(value = "customer_source")
    private String customerSource;

    @Column(value = "war_zone")
    private String warZone;

    /** 地域 */
    @Column(value = "region_name")
    private String regionName;

    /** 可用区 */
    @Column(value = "zone_name")
    private String zoneName;

    /** 实例类型 */
    @Column(value = "instance_type")
    private String instanceType;

    /** 实例规格 */
    @Column(value = "instance_model")
    private String instanceModel;

    /** 变化前item */
    @Column(value = "before_item_json")
    private String beforeItemJson;

    /** 变化后item */
    @Column(value = "after_item_json")
    private String afterItemJson;

}
