package cloud.demand.app.modules.soe.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv2.entity.PplForecastInputExcludedSpikeLatestDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.model.ProductUin;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week_forecast.service.Ppl13weekCommonDataAccess;
import cloud.demand.app.modules.soe.service.SoeCommonService;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import yunti.boot.exception.ITException;

/** 日规模表物化视图任务（屏蔽中长尾） */
@Component
@Slf4j(topic = "日规模物化视图日刷")
public class DwdTxyScaleDfViewWork extends AbstractSopWork<SimpleCommonTask> {

    /** 新ck的std_crp */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SimpleCommonTaskProcess process;

    @Resource
    private DictService dictService;

    @Resource
    private MrpV2DictService mrpV2DictService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private Ppl13weekCommonDataAccess ppl13weekCommonDataAccess;

    @Resource
    private SoeCommonService commonService;


    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    /** 8:10生成 */
    @Scheduled(cron = "0 10 8 * * ?")
    public void initForecastRateTask(){
        initTask(null);
    }

    /** 初始化任务 */
    public void initTask(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now().plusDays(-1);
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask,getEnum());
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.DWD_TXY_SCALE_DF_VIEW_FLUSH;
    }

    /** 两分钟一次 */
    @Scheduled(fixedRate = 30 * 1000)//TODO :临时跑数据，记得改回来
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        flushOneDay(LocalDate.parse(simpleCommonTask.getVersion()));
    }

    /** 重建日规模视图，清空然后全量插入 */
    @Deprecated
    public void rebuild(){
//        String sql = ORMUtils.getSql("/sql/soe/task/dwd_txy_scale_df_view_rebuild.sql");
//        sql = getSqlBuilder().build(sql);
//        // 重建视图
//        ckcldStdCrpDBHelper.executeRaw("TRUNCATE TABLE IF EXISTS std_crp.dwd_txy_scale_df_view_local ON CLUSTER default_cluster sync;");
//        ckcldStdCrpDBHelper.executeRaw(sql);
    }

    /** 刷新一天的数据，只更新某一天的数据 */
    public void flushOneDay(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now().plusDays(-1); // 默认昨天
        }
        String format = DateUtils.format(statTime);
        String sql = ORMUtils.getSql("/sql/soe/task/dwd_txy_scale_df_view_latest.sql");
        SimpleSqlBuilder sqlBuilder = getSqlBuilder(statTime); // sql 构建器
        sqlBuilder.addParam("stat_time", format);
        sql = sqlBuilder.build(sql);
        ckcldStdCrpDBHelper.executeRaw("ALTER TABLE std_crp.dwd_txy_scale_df_view_local ON CLUSTER default_cluster DROP PARTITION ?",format);
        ckcldStdCrpDBHelper.executeRaw(sql);
        // 校验是否有数据
        Long currCount = ckcldStdCrpDBHelper.getRawOne(Long.class, "select count(0) from std_crp.dwd_txy_scale_df_view where stat_time = ?", format);
        if (currCount == 0){
            throw new ITException(String.format("日规模视图底表数据为 0,切片时间:[%s]", format));
        }
        String latestStatTime = ckcldStdCrpDBHelper.getRawOne(String.class, "select max(stat_time) from std_crp.dwd_txy_scale_df_view where stat_time < ?",format);
        if (latestStatTime == null){
            return;
        }
        Long latestCount = ckcldStdCrpDBHelper.getRawOne(Long.class, "select count(0) from std_crp.dwd_txy_scale_df_view where stat_time = ?", format);
        double diff = Math.abs(latestCount - currCount);
        double diffRate = diff/currCount;
        log.info(String.format("切片时间:[%s]，当前数据量:[%s]，最新数据量:[%s]，差异:[%s], 差异比:[%s]", format,currCount,latestCount,diff, diffRate));
    }

    public SimpleSqlBuilder getSqlBuilder(LocalDate statTime){
        // 获取通用客户简称
        List<IndustryDemandIndustryWarZoneDictDO> customerConfig = dictService.queryEnableIndustryWarZoneCustomerConfig();
        // 过滤非空客户且启用的
        customerConfig = customerConfig.stream().filter(item ->
                StringUtils.isNotBlank(item.getCustomerName())
                        && StringUtils.isNotBlank(item.getCommonCustomerName())
                        && BooleanUtils.isTrue(item.getIsEnable())).collect(Collectors.toList());

        // 通过客户简称映射 crp战区，通用客户简称
        Map<String, List<IndustryDemandIndustryWarZoneDictDO>> commonCustomerShortNameMap = ListUtils.groupBy(customerConfig, IndustryDemandIndustryWarZoneDictDO::getCommonCustomerName);

        Map<List<String>, String> customerShortNameMap = new HashMap<>();
        commonCustomerShortNameMap.forEach((k,v)->{
            List<String> customerShortNameList = v.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).collect(Collectors.toList());
            // 如果只有一个客户简称且和通用客户简称相同，则不做映射(默认通用客户简称 = 客户简称),这么做的目的是减少 sql 代码量,防止 sql 过长
            if (customerShortNameList.size() == 1 && Objects.equals(customerShortNameList.get(0),k)){
                return;
            }
            customerShortNameMap.put(customerShortNameList,k);
        });

        Map<String, List<IndustryDemandIndustryWarZoneDictDO>> crpWarZoneGroupMap = ListUtils.groupBy(customerConfig, IndustryDemandIndustryWarZoneDictDO::getWarZoneName);
        Map<List<String>, String> crpWarZoneMap = new HashMap<>();
        crpWarZoneGroupMap.forEach((k,v)->{
            List<String> customerShortNameList = v.stream().map(IndustryDemandIndustryWarZoneDictDO::getCustomerName).collect(Collectors.toList());
            crpWarZoneMap.put(customerShortNameList,k);
        });

        String unCustomerShortName = SopSelectCaseBuilder.build("customer_short_name", "customer_short_name", customerShortNameMap);
        String crpWarZone = SopSelectCaseBuilder.build("customer_short_name", "war_zone", crpWarZoneMap);

        // 合并机型
        Map<List<String>, String> unInstanceTypeMap = new HashMap<>();
        Map<String, List<String>> tempUnInstanceTypeMap = new HashMap<>();
        Map<String, String> instanceType2Un = commonService.getInstanceType2Un();
        instanceType2Un.forEach((k,v)->{
            tempUnInstanceTypeMap.computeIfAbsent(v,item->new ArrayList<>()).add(k);
        });
        tempUnInstanceTypeMap.forEach((k,v)->{
            unInstanceTypeMap.put(v,k);
        });

        // 新客户分类
        List<String> bigCustomerShortName = mrpV2DictService.getBigCustomerShortName();

        YearMonth yearMonth = YearMonth.from(statTime);
        // 长期尾毛刺
        List<PplForecastInputExcludedSpikeLatestDO> tailBurr = mrpV2DictService.getLongTailBurr(yearMonth, yearMonth);
        // 当月没有数据很正常,但是历史月份不能为空,策略表维护人[nick]
        if (ListUtils.isEmpty(tailBurr) && yearMonth.isBefore(YearMonth.now())){
            throw new ITException(String.format("长期尾毛刺数据不存在, 年月:[%s]", yearMonth));
        }
        List<String> tailBurrList = new ArrayList<>();
        if (ListUtils.isEmpty(tailBurr)){
            tailBurr = new ArrayList<>();
            tailBurr.add(PplForecastInputExcludedSpikeLatestDO.empty());
        }
        for (PplForecastInputExcludedSpikeLatestDO aDo : tailBurr) {
            tailBurrList.add(aDo.getSqlKey());
        }

        // 产品 uin 集合
        List<ProductUin> productUin = mrpV2DictService.getProductUin();
        List<String> uinList = new ArrayList<>(); // uin 集合
        List<String> eksUinList = new ArrayList<>(); // eks uin 集合
        List<String> emrUinList = new ArrayList<>(); // emr uin 集合
        Map<List<String>, String> elseProductClassMap = new HashMap<>(); // 其他产品分类
        productUin.forEach(item -> {
            uinList.addAll(item.getUin());
            if (Objects.equals(item.getProduct(), Ppl13weekProductTypeEnum.EKS.getName())){
                eksUinList.addAll(item.getUin());
            }else if (Objects.equals(item.getProduct(), Ppl13weekProductTypeEnum.EMR.getName())){
                emrUinList.addAll(item.getUin());
            }else {
                elseProductClassMap.put(item.getUin(),item.getProduct());
            }
        });

        String elseProductClass = SopSelectCaseBuilder.build("toString(uin)", "'(空值)'", elseProductClassMap);
        String unInstanceType = SopSelectCaseBuilder.build("instance_type", "instance_type", unInstanceTypeMap);
        List<String> blackInstanceType = ppl13weekCommonDataAccess.getAllBlackInstanceTypeForMiddleForecast();

        // 设置参数
        SimpleSqlBuilder sqlBuilder = new SimpleSqlBuilder();
        sqlBuilder.addParam("un_customer_short_name",unCustomerShortName); // 通用客户简称
        sqlBuilder.addParam("crp_war_zone",crpWarZone); // crp战区
        sqlBuilder.addParam("un_instance_type",unInstanceType); // 通用机型
        sqlBuilder.addParam("header_customer_short_name",StringUtils.join(bigCustomerShortName,"','")); // 头部客户简称
        sqlBuilder.addParam("product_class_uin",StringUtils.join(uinList,"','")); // 产品 uin
        sqlBuilder.addParam("eks_uin",StringUtils.join(eksUinList,"','"));  // eks uin
        sqlBuilder.addParam("emr_uin",StringUtils.join(emrUinList,"','")); // emr uin
        sqlBuilder.addParam("else_product_class",elseProductClass); // 其他产品
        sqlBuilder.addParam("black_instance_type",StringUtils.join(blackInstanceType,"','")); // 黑名单机型
        sqlBuilder.addParam("long_tail_burr",StringUtils.join(tailBurrList,"), (")); // 中长尾毛刺

        return sqlBuilder;
    }
}
