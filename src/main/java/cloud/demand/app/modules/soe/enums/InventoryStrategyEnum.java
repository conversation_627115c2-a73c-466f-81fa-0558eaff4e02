package cloud.demand.app.modules.soe.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/** 库存口径 */
@Getter
@AllArgsConstructor
public enum InventoryStrategyEnum {
    Current("当前量"),
    Forecast("未来预测量"),

    ;

    private final String name;

    public static InventoryStrategyEnum getDef(){
        return Current;
    }

    public static InventoryStrategyEnum getByName(String name){
        for (InventoryStrategyEnum value : values()) {
            if (Objects.equals(name,value.getName())){
                return value;
            }
        }
        return getDef();
    }
}
