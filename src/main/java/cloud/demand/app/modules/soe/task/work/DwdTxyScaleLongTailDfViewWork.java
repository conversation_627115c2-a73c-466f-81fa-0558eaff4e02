package cloud.demand.app.modules.soe.task.work;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.mrpv2.Constant;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.mrpv2.task.process.SimpleCommonTaskProcess;
import cloud.demand.app.modules.sop.service.alert.SopRtxRobotHttpService;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import cloud.demand.app.modules.sop_return.utils.SopSelectCaseBuilder;
import cloud.demand.app.modules.sop_util.process.utils.sql.SimpleSqlBuilder;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.lang.DateUtils;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import yunti.boot.exception.ITException;

/** 日规模表物中长尾化视图任务 */
@Component
@Slf4j(topic = "日规模中长尾视图")
public class DwdTxyScaleLongTailDfViewWork extends AbstractSopWork<SimpleCommonTask> {

    /** 新ck的std_crp */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    @Resource
    private SimpleCommonTaskProcess process;

    @Resource
    private DictService dictService;

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private DBHelper rrpDBHelper;

    @Resource
    private DwdTxyScaleDfViewWork dwdTxyScaleDfViewWork;

    @Resource
    private SopRtxRobotHttpService sopRtxRobotHttpService;

    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    /** 8:10生成 */
    @Scheduled(cron = "0 10 8 * * ?")
    public void initForecastRateTask(){
        initTask(null);
    }

    /** 初始化任务 */
    public void initTask(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now().plusDays(-1);
        }
        SimpleCommonTask simpleCommonTask = new SimpleCommonTask();
        simpleCommonTask.setVersion(DateUtils.format(statTime));
        process.initTask(simpleCommonTask,getEnum());
    }

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.DWD_TXY_SCALE_LONG_TAIL_DF_VIEW_FLUSH;
    }

    /** 两分钟一次 */
    @Scheduled(fixedRate = 120 * 1000)
    @Override
    public void work() {
        super.work();
    }

    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        flushOneDay(LocalDate.parse(simpleCommonTask.getVersion()));
    }

    /** 重建日规模视图，清空然后全量插入 */
    @Deprecated
    public void rebuild(){
//        String sql = ORMUtils.getSql("/sql/soe/task/dwd_txy_scale_long_tail_df_view_rebuild.sql");
//        sql = getSqlBuilder().build(sql);
//        // 重建视图
//        ckcldStdCrpDBHelper.executeRaw("TRUNCATE TABLE IF EXISTS std_crp.dwd_txy_scale_long_tail_df_view_local ON CLUSTER default_cluster sync;");
//        ckcldStdCrpDBHelper.executeRaw(sql);
    }

    /** 刷新一天的数据，只更新某一天的数据 */
    public void flushOneDay(LocalDate statTime){
        if (statTime == null){
            statTime = LocalDate.now().plusDays(-1); // 默认昨天
        }
        String format = DateUtils.format(statTime);
        String sql = ORMUtils.getSql("/sql/soe/task/dwd_txy_scale_long_tail_df_view_latest.sql");
        SimpleSqlBuilder sqlBuilder = getSqlBuilder(statTime);// sql 构建器
        sqlBuilder.addParam("stat_time", format);
        sql = sqlBuilder.build(sql);
        ckcldStdCrpDBHelper.executeRaw("ALTER TABLE std_crp.dwd_txy_scale_long_tail_df_view_local ON CLUSTER default_cluster DROP PARTITION ?",format);
        ckcldStdCrpDBHelper.executeRaw(sql);
        // 校验是否有数据
        Long currCount = ckcldStdCrpDBHelper.getRawOne(Long.class, "select count(0) from std_crp.dwd_txy_scale_long_tail_df_view where stat_time = ?", format);
        if (currCount == 0){
            throw new ITException(String.format("日规模视图底表数据为 0,切片时间:[%s]", format));
        }
        String latestStatTime = ckcldStdCrpDBHelper.getRawOne(String.class, "select max(stat_time) from std_crp.dwd_txy_scale_long_tail_df_view where stat_time < ?",format);
        if (latestStatTime == null){
            return;
        }
        Long latestCount = ckcldStdCrpDBHelper.getRawOne(Long.class, "select count(0) from std_crp.dwd_txy_scale_long_tail_df_view where stat_time = ?", format);
        double diff = Math.abs(latestCount - currCount);
        double diffRate = diff/currCount;
        log.info(String.format("切片时间:[%s]，当前数据量:[%s]，最新数据量:[%s]，差异:[%s], 差异比:[%s]", format,currCount,latestCount,diff, diffRate));
    }

    private SimpleSqlBuilder getSqlBuilder(LocalDate statTime){
        // 卡类型转卡型
        List<GpuTypeDO> raw = rrpDBHelper.getRaw(GpuTypeDO.class,
                "select distinct gpu_type,gpu_card_type from report_config_gpu_type where deleted = 0");

        Map<String, Set<String>> gpuType2CardType = new HashMap<>();
        for (GpuTypeDO gpuTypeDO : raw) {
            if (StringUtils.isNotBlank(gpuTypeDO.getGpuType()) && StringUtils.isNotBlank(gpuTypeDO.getGpuCardType())){
                gpuType2CardType.computeIfAbsent(gpuTypeDO.getGpuType(),k -> new HashSet<>()).add(gpuTypeDO.getGpuCardType());
            }
        }

        Map<List<String>,String> gpuCardType2GpuType = new HashMap<>();
        gpuType2CardType.forEach((k,v)-> gpuCardType2GpuType.put(new ArrayList<>(v),k));

        String gpuType = SopSelectCaseBuilder.build("gpu_card_type","'" + Constant.EMPTY_VALUE + "'", gpuCardType2GpuType);

        SimpleSqlBuilder sqlBuilder = dwdTxyScaleDfViewWork.getSqlBuilder(statTime);
        sqlBuilder.addParam("gpu_type",gpuType); // 卡类型清洗卡型
        return sqlBuilder;
    }

    @Data
    public static class GpuTypeDO{
        @Column("gpu_type")
        private String gpuType;
        @Column("gpu_card_type")
        private String gpuCardType;
    }
}
