package cloud.demand.app.modules.soe.utils.log;

import cloud.demand.app.modules.sop.enums.Constant;
import cloud.demand.app.modules.sop.util.MDCUtil;
import cloud.demand.app.modules.sop.util.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/** 日志上报 */
@Slf4j
public class RtxLogReport implements ILogReport{

    @Override
    public void log(String key, String msg) {
        String format = String.format("日志上报：关键字：【%s】，内容：【%s】，traceId：【%s】", ObjectUtils.defaultIfNull(key, Constant.EMPTY_VALUE_STR),
                ObjectUtils.defaultIfNull(msg,Constant.EMPTY_VALUE_STR), ObjectUtils.defaultIfNull(MDCUtil.getTraceId(),""));
        log.info(format);
        TaskUtil.simpleAlertError(format);
    }
}
