package cloud.demand.app.modules.soe.task.work.distribute.dws;

import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.common.utils.StdUtils;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.entity.ResPlanHolidayWeekDO;
import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv2.enums.SimpleTaskEnum;
import cloud.demand.app.modules.operation_view.inventory_health.dto.HolidayWeekInfoDTO;
import cloud.demand.app.modules.operation_view.inventory_health.service.InventoryHealthDictService;
import cloud.demand.app.modules.soe.entitiy.distribute.IOrderForecastGroupKey;
import cloud.demand.app.modules.soe.entitiy.distribute.dwd.DwdSoeForecastDatumItemDfDO;
import cloud.demand.app.modules.soe.entitiy.distribute.dwd.DwdSoeYunxiaoApplyItemDfDO;
import cloud.demand.app.modules.soe.entitiy.distribute.dws.DwsSoeYunxiaoApplyItemDfDO;
import cloud.demand.app.modules.soe.enums.DistributeDimEnum;
import cloud.demand.app.modules.soe.enums.score.SoeWeekNEnum;
import cloud.demand.app.modules.soe.task.work.distribute.DwsDistributeWork;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import cloud.demand.app.modules.sop.util.CkDBUtils;
import cloud.demand.app.modules.sop_device.sopTask.frame.enums.ITaskEnum;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 预约单dws任务
 */
@Slf4j(topic = "分货看板-dws层-预约单&提前期数据：")
@Service
public class DwsOrderWork extends DwsDistributeWork {

    /**
     * 新ck的std_crp
     */
    @Resource
    private DBHelper ckcldStdCrpDBHelper;

    /**
     * 查节假周
     */
    @Resource
    private InventoryHealthDictService healthDictService;

    @Override
    public ITaskEnum getEnum() {
        return SimpleTaskEnum.DWS_SOE_DISTRIBUTE;
    }

    @Override
    public void doWork(SimpleCommonTask simpleCommonTask) {
        String version = simpleCommonTask.getVersion(); // 版本即切片时间，格式：yyyy-MM-dd
        // 需要用到TaskLog，所以用bean取调用
        SpringUtil.getBean(DwsOrderWork.class).genOrderForecastData(version);
    }

    /**
     * 生成预约单&提前期
     */
    @TaskLog(taskName = "soeDistribute@DWS@OrderForecast")
    public void genOrderForecastData(String version) {

        StopWatch stopWatch = new StopWatch();

        // step1：获取dwd预约单数据
        stopWatch.start(String.format("获取dwd预约单数据，切片时间：【%s】", version));
        List<DwdSoeYunxiaoApplyItemDfDO> orderData = ckcldStdCrpDBHelper.
                getAll(DwdSoeYunxiaoApplyItemDfDO.class, "where stat_time = ?", version);

        log.info(String.format("获取dwd预约单数据,切片时间：【%s】，size：【%s】", version, orderData.size()));
        stopWatch.stop();

        // step2：获取dwd预测基准数据
        stopWatch.start(String.format("获取dwd预测基准数据，切片时间：【%s】", version));
        List<DwdSoeForecastDatumItemDfDO> forecastData = ckcldStdCrpDBHelper.
                getAll(DwdSoeForecastDatumItemDfDO.class, "where stat_time = ?", version);

        // 调整基准数据（基准修正：N周预测基准 = MIN(N周版本预测，最新版本预测) & 分摊预约量）
        forecastData.forEach(DwdSoeForecastDatumItemDfDO::adjustment);

        log.info(String.format("获取dwd预测基准数据,切片时间：【%s】，size：【%s】", version, forecastData.size()));
        stopWatch.stop();

        stopWatch.start(String.format("合并预测基准-->预约单，切片时间：【%s】", version));
        // step3：预约单分组，key：起始购买时间年月 @ 国家 @地域 @ 客户简称 @ 实例类型
        List<DwsSoeYunxiaoApplyItemDfDO> saveData = new ArrayList<>();
        forecastData.stream().collect(Collectors.groupingBy(item -> item.getDistributeType()))
                .forEach((k, list) -> {
                    saveData.addAll(matchOrderAndForecast(orderData, list, DistributeDimEnum.getByCode(k)));
                });
        stopWatch.stop();

        // 填充id
        AtomicLong id = new AtomicLong(0);
        for (DwsSoeYunxiaoApplyItemDfDO item : saveData) {
            item.setId(id.getAndIncrement());
        }

        // step6：清理当前分区
        stopWatch.start(String.format("清理当前分区&写入ck，切片时间：【%s】", version));
        CkDBUtils.delete(ckcldStdCrpDBHelper, version, DwsSoeYunxiaoApplyItemDfDO.class);
        // step7：写入ck
        log.info(String.format("写入ck,切片时间：【%s】，size：【%s】", version, forecastData.size()));
        CkDBUtils.saveBatch(ckcldStdCrpDBHelper, saveData);
        stopWatch.stop();
        log.info(stopWatch.prettyPrint());
    }

    private List<DwsSoeYunxiaoApplyItemDfDO> matchOrderAndForecast(List<DwdSoeYunxiaoApplyItemDfDO> orderData, List<DwdSoeForecastDatumItemDfDO> forecastData, DistributeDimEnum distributeDim) {
        // 是否按国家分货
        boolean distributeCountry = distributeDim == DistributeDimEnum.Country;

        Function<IOrderForecastGroupKey, String> orderKeyFun = (item) ->
                StringUtils.joinWith("@", item.getCountryName(), distributeCountry ? StdUtils.EMPTY_STR : item.getRegionName(), item.getYearMonth(), item.getCustomerShortName(), item.getInstanceType());

        Map<String, List<DwdSoeYunxiaoApplyItemDfDO>> orderGroupMap = ListUtils.groupBy(orderData, orderKeyFun); // 预约单分组

        // step4：需求预测数据分组，key：国家 @地域 @ 客户简称 @ 实例类型
        Function<IOrderForecastGroupKey, String> forecastKeyFun = (item) ->
                StringUtils.joinWith("@", item.getCountryName(), distributeCountry ? StdUtils.EMPTY_STR : item.getRegionName(), item.getCustomerShortName(), item.getInstanceType());

        Map<String, List<DwdSoeForecastDatumItemDfDO>> forecastGroupMap = ListUtils.groupBy(forecastData, forecastKeyFun); // 预测量分组

        // step5：匹配w0_4 ~ w13提前预测量和提前预测信息
        List<DwsSoeYunxiaoApplyItemDfDO> ret = new ArrayList<>();
        orderGroupMap.forEach((k, ls) -> { // key：起始购买时间年月 @ 国家 @地域 @ 客户简称 @ 实例类型，ls：同维度预约单
            DwdSoeYunxiaoApplyItemDfDO item = ls.get(0);
            List<DwdSoeForecastDatumItemDfDO> forecastItem = forecastGroupMap.get(forecastKeyFun.apply(item)); // 同维度预测数据

            // 合并预约单 & 预测基准
            ret.addAll(mergeOrderAndForecast(ls, ObjectUtils.defaultIfNull(forecastItem, new ArrayList<>()), distributeDim));

        });
        return ret;
    }

    /**
     * 合并预约单 & 预测基准
     *
     * @param orderData    预约单
     * @param forecastData 预测基准
     * @return dws层数据，保留原有预约单信息，类似 预约单 left join 预测基准
     */
    private List<DwsSoeYunxiaoApplyItemDfDO> mergeOrderAndForecast(List<DwdSoeYunxiaoApplyItemDfDO> orderData, List<DwdSoeForecastDatumItemDfDO> forecastData, DistributeDimEnum distributeDim) {
        // step1：预约单先转dws层
        List<DwsSoeYunxiaoApplyItemDfDO> ret = ListUtils.transform(orderData, DwdSoeYunxiaoApplyItemDfDO::transform);

        // step2：预测基准分组，key：起始购买年周
        Function<DwdSoeForecastDatumItemDfDO, String> yearWeekKey = (item) ->
                StringUtils.joinWith("@", item.getYearWeek());

        Map<String, List<DwdSoeForecastDatumItemDfDO>> yearWeekMap = ListUtils.groupBy(forecastData, yearWeekKey); // 年周（提单年周）分组

        for (DwsSoeYunxiaoApplyItemDfDO item : ret) {
            String yearWeek = item.getYearWeek(); // 实际匹配预测基准是用该年周所在月的首周

            BigDecimal totalCore = item.getTotalCore(); // 预约总量，提前周W13~W0的类和不能超过totalCore

            List<DwdSoeForecastDatumItemDfDO> dwdSoeForecastDatumItemDfDOS = yearWeekMap.get(yearWeek);

            if (ListUtils.isNotEmpty(dwdSoeForecastDatumItemDfDOS)) {
                List<String> ids = dwdSoeForecastDatumItemDfDOS.stream().map(DwdSoeForecastDatumItemDfDO::getOrderIds)
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList());

                // W13 ~ W0
                List<SoeWeekNEnum> collect = Arrays.stream(SoeWeekNEnum.values())
                        .sorted((o1, o2) -> Integer.compare(o2.getWeekN(), o1.getWeekN())).collect(Collectors.toList());
                t:
                for (SoeWeekNEnum value : collect) {

                    for (DwdSoeForecastDatumItemDfDO dwdSoeForecastDatumItemDfDO : dwdSoeForecastDatumItemDfDOS) {
                        BigDecimal weekNNum = value.getGetter().apply(dwdSoeForecastDatumItemDfDO); // 提前WeekN周的需求量
                        BigDecimal changeNum = SoeCommonUtils.min(weekNNum, totalCore); // 取最小值

                        totalCore = totalCore.subtract(changeNum); // 总量扣减掉需求的量
                        value.getSetter().accept(dwdSoeForecastDatumItemDfDO, weekNNum.subtract(changeNum)); // 需求预测Wn的量同时扣减
                        value.addWn(item, changeNum); // 累加到Wn中
                        if (totalCore.compareTo(BigDecimal.ZERO) == 0) {
                            break t; // 总量扣减完跳出循环
                        }
                    }
                }
                // 设置wInfo，包含提前周的的预测基准dwd层预约单集合
                item.setWInfo(StringUtils.join(ids, ","));
            }
            // 设置分货类型
            item.setDistributeType(distributeDim.getCode());
            // 还有结余存在W0里面
            if (totalCore.compareTo(BigDecimal.ZERO) > 0) {
                SoeWeekNEnum.w0.addWn(item, totalCore);
            }

        }

        return ret;
    }

    @Data
    public static class ForecastWeekInfo {

        /**
         * dwd层需求预测id集合
         */
        private List<Long> ids = new ArrayList<>();

        /**
         * 总核数
         */
        private BigDecimal totalCore = BigDecimal.ZERO;
    }
}
