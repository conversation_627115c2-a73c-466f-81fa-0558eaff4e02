package cloud.demand.app.modules.flow.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.flow.constant.TrueOrFalseIntCodeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import lombok.Data;
import lombok.ToString;

/**
 *  流程节点链定义表
 */
@Data
@ToString
@Table("conf_flow_node_chain")
public class ConfFlowNodeChainDO extends BaseDO {

    /** 流程版本id<br/>Column: [flow_version_id] */
    @Column(value = "flow_version_id")
    private Long flowVersionId;

    /** 流程编码<br/>Column: [flow_code] */
    @Column(value = "flow_code")
    private String flowCode;

    /** 流程节点编码<br/>Column: [node_code] */
    @Column(value = "node_code")
    private String nodeCode;

    /** 流程节点名称<br/>Column: [node_name] */
    @Column(value = "node_name")
    private String nodeName;

    /** 流程节点返回值，0（否、不通过），1（是，通过），-999（节点操作异常），其他值开发自定义与业务操作保持一致<br/>Column: [node_return] */
    @Column(value = "node_return")
    private Integer nodeReturn;

    /** 流程节点返回值描述<br/>Column: [node_return_remark] */
    @Column(value = "node_return_remark")
    private String nodeReturnRemark;

    /** 下一个节点编码<br/>Column: [next_node_code] */
    @Column(value = "next_node_code")
    private String nextNodeCode;

    /** 操作名称<br/>Column: [operate_name] */
    @Column(value = "operate_name")
    private String operateName;

    /**
     *  是否流程启动节点，0否，1是<br/>Column: [is_flow_begin]
     * @see TrueOrFalseIntCodeEnum
     */
    @Column(value = "is_flow_begin")
    private Integer isFlowBegin;

    /**
     *  是否默认的节点链，一个流程节点只能有一条此字段值为1的记录，用于常规流程链的查询显示，0否，1是<br/>Column: [is_default_chain]
     * @see TrueOrFalseIntCodeEnum
     */
    @Column(value = "is_default_chain")
    private Integer isDefaultChain;

    /**
     *  排序号，越小流程节点越靠前，同一个node_code节点的is_default_chain值为1的数据的排序号要在相同node_code的其他数据前面
     *  <br/>Column: [is_default_chain]
     */
    @Column(value = "sort_order")
    private Integer sortOrder;

}
