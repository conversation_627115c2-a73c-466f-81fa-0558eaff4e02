package cloud.demand.app.modules.flow.constant;

import cloud.demand.app.modules.flow.entity.ConfFlowNodeChainDO;
import cloud.demand.app.modules.flow.entity.ConfFlowNodeDO;
import cloud.demand.app.modules.flow.entity.FlowNodeRecordDO;

/**
 *  默认占用的几个固定的节点返回值
 * @see ConfFlowNodeChainDO#getNodeReturn()
 * @see FlowNodeRecordDO#getNodeReturn()
 */
public enum FlowNodeDefaultReturnValueEnum {

    /**
     *  -999，表示节点操作异常
     */
    ERROR(-999),

    /**
     *  0，表示否，不通过等等，<br/>
     *  若当前节点为审批节点{@link ConfFlowNodeDO#getIsApprovalNode()}，且返回值为 0 表示审批拒绝，前端会显示节点为 红叉叉
     */
    NOT_PASS(0),

    /**
     *  1，表示是，通过等等
     */
    PASS(1),

    /**
     *  888，配置的结束流程操作（红叉叉专用），用于流程正常结束且需要前端显示红叉叉时配置此值，<br/>
     *  此返回值需要配置到节点链表中，且必须为流程结束操作({{@link ConfFlowNodeChainDO#getNextNodeCode()}} 为null)，<br/>
     *  使用此节点返回值时前端显示的节点会是 红叉叉
     */
    RED_DONE(888),

    /**
     *  998，强制结束流程，此返回值为内程序内部默认值，此返回值不能配置到节点链表中，使用此节点返回值时前端显示的节点会是 红叉叉
     */
    FORCE_DONE(998),

    /**
     *  999，用这个这个节点返回值程序内部会使节点自旋一次，表示节点内进行一次操作，从流程上来看就是从节点 A -> A，此返回值不能配置到节点链表中
     */
    SPIN(999);

    private final int code;

    FlowNodeDefaultReturnValueEnum(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
