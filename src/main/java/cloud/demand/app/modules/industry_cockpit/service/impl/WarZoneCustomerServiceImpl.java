package cloud.demand.app.modules.industry_cockpit.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.entity.WarnZoneCustomerNameVO;
import cloud.demand.app.modules.industry_cockpit.service.WarZoneCustomerService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/5/13
 */
@Service
public class WarZoneCustomerServiceImpl implements WarZoneCustomerService {

    @Resource
    private DBHelper ckcldDBHelper;

    @Override
    public List<String> getWarZoneCustomerList(List<String> authIndustry, List<String> authWarZone) {
        WhereSQL customerNameWhereSQL = new WhereSQL();
        if (!CollectionUtils.isEmpty(authIndustry)) {
            customerNameWhereSQL.and("industry_dept in (?) ", authIndustry);
        }
        if (!CollectionUtils.isEmpty(authWarZone)) {
            customerNameWhereSQL.and("war_zone_name in (?) ", authWarZone);
        }
        String sql = ORMUtils.getSql("/sql/industry_cockpit/14_get_customer_short_name_by_auth.sql");
        sql = sql.replace("${FILTER}", customerNameWhereSQL.getSQL().replaceAll("WHERE", " and "));
        List<WarnZoneCustomerNameVO> warnZoneCustomerNameVOS = ckcldDBHelper.getRaw(WarnZoneCustomerNameVO.class, sql, customerNameWhereSQL.getParams());
        List<String> customerNameList = warnZoneCustomerNameVOS.stream().map(WarnZoneCustomerNameVO::getCustomerShortName).collect(Collectors.toList());
        return customerNameList;
    }
}
