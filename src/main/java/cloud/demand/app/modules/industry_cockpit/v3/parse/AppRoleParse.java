package cloud.demand.app.modules.industry_cockpit.v3.parse;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.industry_cockpit.v3.constant.IndustryCockpitV3Constant;
import cloud.demand.app.modules.industry_cockpit.v3.enums.AppRoleEnum;
import cloud.demand.app.modules.sop_return.frame.where.IWhereParser;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import com.pugwoo.wooutils.collect.ListUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/14 14:19
 */
public class AppRoleParse implements IWhereParser {
    @Override
    public void parse(ORMUtils.WhereContent content, SopWhereBuilder.SopWhere sopWhere, Object t) {
        List<String> appRoleList = (List<String>) sopWhere.getV();
        if (ListUtils.isNotEmpty(appRoleList)) {
            if (appRoleList.contains(IndustryCockpitV3Constant.OTHER)) {
                List<String> notInAppRoleList = AppRoleEnum.getWithoutOther().stream().map(AppRoleEnum::getName).collect(Collectors.toList());
                List<String> inAppRoleList = appRoleList.stream().filter(e -> !e.equals(IndustryCockpitV3Constant.OTHER)).collect(Collectors.toList());
                content.addAnd(" app_role not in (?) or app_role in (?) ", notInAppRoleList, inAppRoleList);
            } else {
                content.addAnd(" app_role in (?) ", appRoleList);
            }
        }
    }
}
