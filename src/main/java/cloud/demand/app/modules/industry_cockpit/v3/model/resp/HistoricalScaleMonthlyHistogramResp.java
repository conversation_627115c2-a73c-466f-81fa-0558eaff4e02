package cloud.demand.app.modules.industry_cockpit.v3.model.resp;

import cloud.demand.app.modules.industry_cockpit.v3.model.vo.HistoricalScaleMonthlyVO;
import cloud.demand.app.modules.industry_cockpit.v3.model.vo.KeyValueItemVO;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2024/8/21 10:13
 */
@Data
public class HistoricalScaleMonthlyHistogramResp {
    private List<MonthlyHistogramItem> data = ListUtils.newArrayList();
    private int count;

    @Data
    private static class MonthlyHistogramItem {
        private String yearMonth;

        private List<KeyValueItemVO> items;

        private BigDecimal total;

        private int count;
    }

    public static HistoricalScaleMonthlyHistogramResp parse(List<HistoricalScaleMonthlyVO> voList) {
        HistoricalScaleMonthlyHistogramResp resp = new HistoricalScaleMonthlyHistogramResp();
        List<MonthlyHistogramItem> data = ListUtils.newArrayList();
        if (ListUtils.isNotEmpty(voList)) {
            Map<String, List<HistoricalScaleMonthlyVO>> group = voList.stream().collect(Collectors.groupingBy(HistoricalScaleMonthlyVO::getYearMonth));
            for (Map.Entry<String, List<HistoricalScaleMonthlyVO>> entry : group.entrySet()) {
                MonthlyHistogramItem item = new MonthlyHistogramItem();
                item.setYearMonth(entry.getKey());
                List<KeyValueItemVO> items = entry.getValue().stream().map(o -> new KeyValueItemVO(HistoricalScaleMonthlyVO.getKey(o),o.getAmount())).collect(Collectors.toList());
                items.sort(Comparator.comparing(KeyValueItemVO::getValue).reversed());
                item.setItems(items);
                item.setTotal(items.stream().map(KeyValueItemVO::getValue).reduce(BigDecimal.ZERO, BigDecimal::add));
                item.setCount(items.size());
                data.add(item);
            }
        }
        data.sort(Comparator.comparing(MonthlyHistogramItem::getYearMonth));

        resp.setData(data);
        resp.setCount(data.size());
        return resp;
    }
}
