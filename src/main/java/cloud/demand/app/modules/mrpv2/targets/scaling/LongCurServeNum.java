package cloud.demand.app.modules.mrpv2.targets.scaling;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.NotShowIndex;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;

import java.util.List;
import java.util.Map;

public class LongCurServeNum extends CurServeNum implements NotShowIndex {
    public static String staticTargetName() {
        return "long_cur_serve_num";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }


    @Override
    public List<MrpV2DataDO> loadData(Map<String, Object> shares) {
        return null;
    }

    @Override
    public TargetTemplate dailyNotDistinct() {
        return new LongDailyNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate monthAvgNotDistinct() {
        return new LongMonthAvgNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate dailyDistinct() {
        return new LongDailyNotDistinctCurServeNum();
    }

    @Override
    public TargetTemplate monthAvgDistinct() {
        return new LongMonthAvgNotDistinctCurServeNum();
    }
}
