package cloud.demand.app.modules.mrpv2.targets.forecast_rate.origin;

import cloud.demand.app.modules.mrpv2.entity.MrpV2DataDO;
import cloud.demand.app.modules.mrpv2.targets.DynamicTargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.forecast_rate.BenchMAPE;
import cloud.demand.app.modules.mrpv2.web.RspFieldHeader;

import java.util.List;
import java.util.Map;

public class OriginBenchMAPE extends BenchMAPE {

    public static String staticTargetName() {
        return "origin_bench_mape";
    }

    @Override
    public String targetName() {
        return staticTargetName();
    }

    @Override
    public RspFieldHeader header() {
        return new RspFieldHeader("", "MAPE偏差率(原始)",
                new RspFieldHeader("", "预测指标", null));
    }
}
