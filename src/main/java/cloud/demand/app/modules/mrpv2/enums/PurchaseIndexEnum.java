package cloud.demand.app.modules.mrpv2.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;


/** name 关联 targetName */
@AllArgsConstructor
@Getter
public enum PurchaseIndexEnum {
    product_order_num("已执行预测","已执行预测"), // ERP采购下单量 ---> 已执行预测
    host_delivered_num("已交付量（需求月份）","已交付量（需求月份）"),
    host_delivered_real_num("已交付量（交付月份）","已交付量（交付月份）"),

    ;

    private final String name;
    private final String index;
}
