package cloud.demand.app.modules.mrpv2.targets.ppl.origin;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.SpringUtil;
import cloud.demand.app.modules.mrpv2.entity.DailyMrpV2DataMap;
import cloud.demand.app.modules.mrpv2.service.CleanService;
import cloud.demand.app.modules.mrpv2.targets.TargetTemplate;
import cloud.demand.app.modules.mrpv2.targets.ppl.V2AvgForecastCoreByDayNum;
import cloud.demand.app.modules.mrpv2.utils.MyMapUtils;
import cloud.demand.app.modules.mrpv2.utils.PaasUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class V2AvgForecastCoreByDayOriginNum extends V2AvgForecastCoreByDayNum {
    @Override
    public String targetName() {
        return "origin_"+super.targetName();
    }

    @Override
    public List<TargetTemplate> complexNode() {
        // TODO 支持干预前后，共识前后核天预测准确率
        return ListUtils.newList(new V2AvgForecastCoreByDayOriginNumMin(), new V2AvgForecastCoreByDayOriginNumMax());
    }

    @Override
    public Map<String, DailyMrpV2DataMap> loadComplexData(Map<String, Object> shares) {
        // 从 532新版-核天预测量中取值
        String yearMonthStart = (String) shares.get("year_month_start");
        String sql = loadSql();
        MyMapUtils.putTargetGenTime(shares, "预测量-核天");
        List<DDO> all = ckcldStdCrpDBHelper.getRaw(DDO.class, sql, yearMonthStart);

        CleanService bean = SpringUtil.getBean(CleanService.class);

        // 遍历拿到的数据，补充缺少的指标
        Map<String, DailyMrpV2DataMap> result = new HashMap<>();

        for (DDO ddo : all) {

            bean.clean(ddo); // 清洗数据

            String customerType = MyMapUtils.getDefCustomerType(ddo.getCustomerType(),ddo.getIndustryDept());
            ddo.setCustomerType(MyMapUtils.customerType(shares, ddo.getUin(),
                    ddo.getYearMonth(), customerType));

            String key = ddo.withTimeKey();
            DailyMrpV2DataMap dataMap = result.get(key);

            if (dataMap == null) {
                dataMap = DDO.originCopy(ddo);
                result.put(key, dataMap);
            } else {
                DDO.originMerge(dataMap,ddo);
                log.error("核天预测量数据重复，key=" + key);
            }
        }

        // 暂时忽略中长尾数据

        return result;
    }


    @Override
    public String loadSql() {
        return PaasUtils.sqlReplacePAAS(ORMUtils.getSql("/sql/mrp/origin_avg_forecast_num_std_crp_core_by_day.sql"));
    }
}
