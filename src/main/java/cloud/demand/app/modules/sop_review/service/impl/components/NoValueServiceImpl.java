package cloud.demand.app.modules.sop_review.service.impl.components;

import cloud.demand.app.modules.sop_review.model.clean.ICleanCoreComponentsBit;
import cloud.demand.app.modules.sop_review.service.ComponentsFieldService;
import java.util.List;
import org.springframework.stereotype.Service;

@Service("noValueService")
public class NoValueServiceImpl implements ComponentsFieldService {

    @Override
    public List<String> getEnumValues() {
        return null;
    }

    @Override
    public boolean hasEnumValues() {
        return false;
    }

    @Override
    public String[] getEnumValue(ICleanCoreComponentsBit bit) {
        return new String[0];
    }
}
