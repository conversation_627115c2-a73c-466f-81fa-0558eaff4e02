package cloud.demand.app.modules.sop_review.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/** 需求类型 */
@AllArgsConstructor
@Getter
public enum SopReviewDemandType {

    NET("净增",null),

    NEW("申领",new SopDataIndexEnum[]{SopDataIndexEnum.SOP_DEMAND_EXECUTED,SopDataIndexEnum.SOP_DEMAND_NOT_EXECUTED}),

    RETURN("退回",new SopDataIndexEnum[]{SopDataIndexEnum.SOP_RETURN_EXECUTED,SopDataIndexEnum.SOP_RETURN_NOT_EXECUTED})


    ;
    private final String name;

    private final SopDataIndexEnum[] sopDataIndexEnum; // null 表示所以

    /**
     * 包含指标（按需求类型分）
     * @param isNew 是否为新增
     * @param index 指标名称
     * @return
     */
    public static boolean containsIndex(boolean isNew,String index){
        if (StringUtils.isBlank(index)){
            return false;
        }
        SopReviewDemandType type = isNew ? NEW : RETURN;
        SopDataIndexEnum[] enums = type.getSopDataIndexEnum();
        for (SopDataIndexEnum anEnum : enums) {
            if (Objects.equals(index,anEnum.getName())){
                return true;
            }
        }
        return false;
    }

    public static SopReviewDemandType getByName(String name){
        for (SopReviewDemandType value : values()) {
            if (Objects.equals(name,value.getName())){
                return value;
            }
        }
        return null;
    }
}
