package cloud.demand.app.modules.sop_review.service.impl;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.modules.soe.dto.req.PageReq;
import cloud.demand.app.modules.sop.domain.report.VersionDTO;
import cloud.demand.app.modules.sop_return.frame.where.SopWhereBuilder;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewVersionPageReq;
import cloud.demand.app.modules.sop_review.dto.req.SopReviewVersionUpdateReq;
import cloud.demand.app.modules.sop_review.dto.req.YearWeekReq;
import cloud.demand.app.modules.sop_review.dto.resp.SopVersionRespList;
import cloud.demand.app.modules.sop_review.entity.BasSopReviewVersionDO;
import cloud.demand.app.modules.sop_review.service.SopReviewCommonService;
import cloud.demand.app.modules.sop_review.service.SopReviewVersionService;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.model.PageData;
import com.pugwoo.wooutils.lang.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SopReviewVersionServiceImpl implements SopReviewVersionService {

    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private SopReviewCommonService commonService;


    @Override
    public PageData<BasSopReviewVersionDO> pageList(SopReviewVersionPageReq req) {
        SopWhereBuilder whereBuilder = new SopWhereBuilder(req, BasSopReviewVersionDO.class);
        ORMUtils.WhereContent where = whereBuilder.where();
        where.orderDesc("version"); // 版本降序
        return ORMUtils.db(demandDBHelper)
                .getPage(BasSopReviewVersionDO.class, new ORMUtils.PageWrapper(PageReq.toPage(req.getPage())), where);
    }

    @Override
    public List<SopVersionRespList> getVersionList(YearWeekReq req) {
        String yearWeek = req.getYearWeek();
        List<VersionDTO> versionList = commonService.getVersionList(yearWeek);
        List<SopVersionRespList> ret = new ArrayList<>();
        for (VersionDTO versionDTO : versionList) {
            SopVersionRespList e = new SopVersionRespList();
            Date time = versionDTO.getTime();
            Boolean delivery = versionDTO.getDelivery();
            String version = versionDTO.getVersion();
            e.setVersion(version);
            e.setTime(DateUtils.toLocalDateTime(time));
            e.setIsDelivery(delivery);
            e.setYearWeek(yearWeek);
            e.setWeekIndex(e.getTime().getDayOfWeek().getValue());
            ret.add(e);
        }
        return ret;
    }

    @Transactional(transactionManager = "demandTransactionManager")
    @Override
    public void update(SopReviewVersionUpdateReq req) {
        BasSopReviewVersionDO item = demandDBHelper.getOne(BasSopReviewVersionDO.class, "where id = ?", req.getId());
        if (item == null){
            throw new IllegalArgumentException(String.format("该版本记录不存在，id：【%s】", req.getId()));
        }
        item.setCasVersion(req.getCasVersion());
        if (req.getW13StartYearMonth()!=null){
            item.setW13StartYearMonth(req.getW13StartYearMonth());
        }
        if (req.getW13EndYearMonth() != null){
            item.setW13EndYearMonth(req.getW13EndYearMonth());
        }
        if (req.getCvmDemandVersion() != null){
            item.setCvmDemandVersion(req.getCvmDemandVersion());
        }
        if (req.getDeviceDemandVersion() != null){
            item.setCvmReturnVersion(req.getCvmReturnVersion());
        }
        if (req.getCvmReturnVersion() != null){
            item.setDeviceDemandVersion(req.getDeviceDemandVersion());
        }
        if (req.getDeviceReturnVersion() != null){
            item.setDeviceReturnVersion(req.getDeviceReturnVersion());
        }
        check(item);
        demandDBHelper.update(item);
    }

    private void check(BasSopReviewVersionDO item) {
        String w13StartYearMonth = item.getW13StartYearMonth();
        String w13EndYearMonth = item.getW13EndYearMonth();
        try {
            YearMonth.parse(w13EndYearMonth);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("年月格式错误，13 周结束年月【%s】", w13EndYearMonth));
        }
        try {
            YearMonth.parse(w13StartYearMonth);
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format("年月格式错误，13 周起始年月【%s】", w13StartYearMonth));
        }
        if (StringUtils.compare(w13StartYearMonth,w13EndYearMonth) > 0){
            throw new IllegalArgumentException(String.format("13 周起始年月不能大于结束年月,起始年月：【%s】，结束年月：【%s】", w13StartYearMonth,w13EndYearMonth));
        }
        String cvmDemandVersion = item.getCvmDemandVersion();
        String deviceDemandVersion = item.getDeviceDemandVersion();
        String cvmReturnVersion = item.getCvmReturnVersion();
        String deviceReturnVersion = item.getDeviceReturnVersion();
        List<VersionDTO> versionDTOS = commonService.getAllVersionListForReview();
        Set<String> allVersion = versionDTOS.stream().map(VersionDTO::getVersion).collect(Collectors.toSet());
        if (!allVersion.contains(cvmDemandVersion)){
            throw new IllegalArgumentException(String.format("cvm 需求版本号错误【%s】", cvmDemandVersion));
        }
        if (!allVersion.contains(deviceDemandVersion)){
            throw new IllegalArgumentException(String.format("物理机 需求版本号错误【%s】", deviceDemandVersion));
        }
        if (!allVersion.contains(cvmReturnVersion)){
            throw new IllegalArgumentException(String.format("cvm 退回版本号错误【%s】", cvmReturnVersion));
        }
        if (!allVersion.contains(deviceReturnVersion)){
            throw new IllegalArgumentException(String.format("物理机 退回版本号错误【%s】", deviceReturnVersion));
        }
    }
}
