package cloud.demand.app.modules.sop_review.job;

import cloud.demand.app.common.utils.Alert;
import cloud.demand.app.common.utils.EnvUtils;
import cloud.demand.app.modules.sop_review.service.SopReviewDictService;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class SyncVersionTask {

    @Resource
    private Alert alert;

    @Resource
    private SopReviewDictService sopReviewDictService;

    /** 每 1 小时执行一次 */
    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 0 0/1 * * * ")
    public void syncSopReviewVersionScheduled() throws InterruptedException {
        // 非生产环境不执行
        if (!EnvUtils.isProduction()){
            return;
        }
        log.info("start syncSopReviewVersion ");
        try {
            sopReviewDictService.syncSopReviewVersion();
        } catch (Exception e) {
            alert.sendRtx("xmingzzhang", "syncSopReviewVersion error", e.getMessage());
        }

    }
}
