package cloud.demand.app.modules.order.dto.req;

import java.util.List;
import lombok.Data;

@Data
public class QuotaMonitorReq {

    private List<String> statTime;

    private List<String> demandYearMonth;

    private List<String> customerUin;

    private List<String> industryDept;

    private List<String> appId;

    private List<String> customerShortName;

    private List<String> regionName;

    private List<String> zoneName;

    private List<String> customhouseTitle;

    private List<String> instanceType;

    private List<String> quotaType;

    private List<String> billType;

    private Boolean isPurchaseInstanceType;

    private List<String> quotaManageType;

}
