package cloud.demand.app.modules.order.dto.resp.supply.process;

import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple2;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import lombok.Data;

@Data
public class OrderSupplyProcessVO {

    //Q20****，R20****
    String orderId;
    //单据类型，星云复用，星云采购
    String type;
    /**
     * 设备类型
     */
    String deviceType;
    /**
     * 申领总量
     */
    Integer number;
    /**
     * 已提货的数量
     */
    Integer usedNumber;

    /**
     * 预计交付时间汇总,如
     * 2023-06-15: 20
     * 2023-07-15: 10
     */
    Map<String, Integer> allExpectDeliveryTime;

    /**
     * 未交付部分的预计交付时间汇总,如
     * 2023-06-15: 20
     * 2023-07-15: 10
     */
    Map<String, Integer> noBufferExpectDeliveryTime;

    List<ProcessItemVO> process;

    /**
     *   解析获取最晚的预计交付日期，只要一条数据有问题，都表示预计交付日期不明确并返回 null
     */
    public Tuple2<LocalDate, Integer> parseGetLastExpectDeliveryDate() {
        if (ListUtils.isEmpty(allExpectDeliveryTime)) {
            return null;
        }
        LocalDate result = null;
        Integer core = 0;
        // 2000 年
        LocalDate day = LocalDate.of(2000, 1, 1);
        for (Map.Entry<String, Integer> entry : allExpectDeliveryTime.entrySet()) {
            String date = entry.getKey();
            Integer value = entry.getValue();
            LocalDate item = null;
            try {
                item = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
            } catch (Exception e) {
                // 解析失败，表示当前数据预计交付日期不明确
                return null;
            }
            if (item == null) {
                // item为null，表示当前数据预计交付日期不明确
                return null;
            }
            if (item.isBefore(day)) {
                // 存在部分数据为 1900-01-01，等价与 未反馈 预计交付日期
                return null;
            }
            if (result == null) {
                result = item;
                core = value;
            } else if (result.isBefore(item)) {
                result = item;
                core = value;
            }
        }
        if (result == null) {
            return null;
        }
        return new Tuple2<>(result, core);
    }

}
