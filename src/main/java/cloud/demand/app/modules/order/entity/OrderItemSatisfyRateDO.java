package cloud.demand.app.modules.order.entity;

import cloud.demand.app.common.BaseDO;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig;
import cloud.demand.app.modules.order.dto.ElasticCycleConfig.PreDeductPlanTime;
import cloud.demand.app.modules.order.dto.PreDeductGridSimpleDTO;
import cloud.demand.app.modules.order.dto.resp.OrderSupplyPlanDetailWithPlanDTO;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderSupplyPlanMatchTypeEnum;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import com.pugwoo.wooutils.collect.ListUtils;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.Tuple3;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.Data;
import org.nutz.lang.Strings;
import yunti.boot.exception.BizException;

/**
 *  不同满足方式下的订单明细满足度信息
 */
@Data
@Table("order_item_satisfy_rate")
public class OrderItemSatisfyRateDO extends BaseDO {

    @Column("order_number")
    private String orderNumber;

    @Column("instance_type")
    private String instanceType;


    @Column("zone_name")
    private String zoneName;

    @Column("zone")
    private String zone;

    /**
     * 地域code<br/>Column: [region]
     */
    @Column(value = "region")
    private String region;

    /**
     * 地域名称<br/>Column: [region_name]
     */
    @Column(value = "region_name")
    private String regionName;

    /**
     * 战区
     * Column: [war_zone]
     */
    @Column(value = "war_zone")
    private String warZone;

    /**
     * 产品
     * Column: [product]
     */
    @Column(value = "product")
    private String product;

    /**
     * 客户简称<br/>Column: [customer_short_name]
     */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /**
     * 客户uin<br/>Column: [customer_uin]
     */
    @Column(value = "customer_uin")
    private String customerUin;

    /**
     * APPID<br/>Column: [app_id]
     */
    @Column(value = "app_id")
    private String appId;

    /**
     * 行业部门<br/>Column: [industry_dept]
     */
    @Column(value = "industry_dept")
    private String industryDept;

    /**
     * 单据类型<br/>Column: [order_type]
     */
    @Column(value = "order_type")
    private String orderType;

    @Column("match_type")
    private String matchType;

    @Column("match_type_name")
    private String matchTypeName;

    @Column("consensus_core")
    private BigDecimal consensusCore;

    // 参与对冲的需求核心数
    @Column("wait_stock_core")
    private BigDecimal waitStockCore;

    // 已实际预扣核心数
    @Column("pre_deduct_core")
    private BigDecimal preDeductCore;

    // 已履约核心数
    @Column("keep_promise_core")
    private BigDecimal keepPromiseCore;

    // 参与对冲部分的满足核心数
    @Column("match_core")
    private BigDecimal matchCore;

    // 按规则计算总体数据分摊到此维度的，总体的满足核心数（包含了已履约、已预扣的认为满足）
    @Column("total_system_match_core")
    private BigDecimal totalSystemMatchCore;

    // 最终总体的满足核心数（供应方案明细满足度修改后的）
    @Column("total_match_core")
    private BigDecimal totalMatchCore;

    // 累计预扣满足量
    @Column("cumulative_pre_deduct_core")
    private BigDecimal cumulativePreDeductCore;

    /**
     * 当前计算满足度时的订单开始购买日期
     */
    @Column("begin_buy_date")
    private LocalDate beginBuyDate;

    @Column("calc_date")
    private LocalDate calcDate;

    /** 是否主要有效的满足度结果 */
    @Column("main_result")
    private Boolean mainResult;

    /** 需求GPU卡数 */
    @Column("consensus_gpu")
    private BigDecimal consensusGpu;

    // 最终总体的满足GPU卡数
    @Column("total_match_gpu")
    private BigDecimal totalMatchGpu;

    // 累计预扣满足GPU卡数
    @Column("cumulative_pre_deduct_gpu")
    private BigDecimal cumulativePreDeductGpu;


    public static List<OrderItemSatisfyRateDO> fromGpuConsensusDetails(List<OrderSupplyPlanDetailWithPlanDTO> list,
            OrderInfoDO order, LocalDate statDate, List<PreDeductOrderItemDO> dudectList)  {
        if (ListUtils.isEmpty(list) || order == null) {
            return new ArrayList<>();
        }
        String orderNumber = order.getOrderNumber();
        List<OrderSupplyPlanDetailDO> datas = list.stream().filter(o ->
                o != null && Strings.isNotBlank(o.getOrderNumber()) && Objects.equals(o.getOrderNumber(), orderNumber)
                        && o.getPlan() != null
                        && Objects.equals(o.getPlan().getMatchType(), OrderSupplyPlanMatchTypeEnum.SATISFY.getCode()))
                .collect(Collectors.toList());
        ListUtils.sortAscNullLast(datas, OrderSupplyPlanDetailDO::getConsensusBeginBuyDate);
        if (ListUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        Map<String, BigDecimal> deductCoreMap = new HashMap<>();
        Map<String, BigDecimal> deductGpuMap = new HashMap<>();
        for (PreDeductOrderItemDO itemDO : dudectList) {
            if (itemDO == null || !Objects.equals(orderNumber, itemDO.getOrderNumber())) {
                continue;
            }
            String key = String.join("@", itemDO.getZoneName(), itemDO.getInstanceType());
            BigDecimal gpu = deductGpuMap.getOrDefault(key, BigDecimal.ZERO)
                    .add(new BigDecimal(itemDO.totalGpuActualCountGetter()));
            deductGpuMap.put(key, gpu);
            BigDecimal core = deductCoreMap.getOrDefault(key, BigDecimal.ZERO)
                    .add(new BigDecimal(itemDO.getTotalReservedCpuCount()));
            deductCoreMap.put(key, core);
        }

        Map<String, OrderItemSatisfyRateDO> resMap = new HashMap<>();
        List<OrderItemSatisfyRateDO> res = new ArrayList<>();
        for (OrderSupplyPlanDetailDO detail : datas) {
            String supplyKey =  String.join("@", detail.getDemandZoneName(), detail.getDemandInstanceType(),
                    detail.getConsensusBeginBuyDate().toString());
            OrderItemSatisfyRateDO item = resMap.get(supplyKey);
            if (item == null) {
                 item = new OrderItemSatisfyRateDO();
                 resMap.put(supplyKey, item);
                 res.add(item);
            }
            item.setInstanceType(detail.getDemandInstanceType());
            item.setBeginBuyDate(detail.getConsensusBeginBuyDate());
            item.setOrderNumber(detail.getOrderNumber());
            item.setAppId(order.getAppId());
            item.setCustomerShortName(order.getCustomerShortName());
            item.setCustomerUin(order.getCustomerUin());
            item.setIndustryDept(order.getIndustryDept());
            item.setProduct(order.getProduct());
            item.setOrderType(order.getOrderType());
            item.setWarZone(order.getWarZone());
            item.setZoneName(detail.getDemandZoneName());
            item.setRegionName(detail.getSupplyRegionName());
            item.setRegion(detail.getSupplyRegion());
            item.setZone(detail.getSupplyZone());
            item.setCalcDate(statDate);
            item.setMainResult(true);
            item.setMatchType(OrderSupplyPlanMatchTypeEnum.SATISFY.getCode());
            item.setMatchTypeName(OrderSupplyPlanMatchTypeEnum.SATISFY.getName());

            BigDecimal supplyCore = detail.getSupplyCoreNum() == null ? BigDecimal.ZERO : new BigDecimal(detail.getSupplyCoreNum());
            BigDecimal normalCore = item.getConsensusCore() == null ? BigDecimal.ZERO : item.getConsensusCore();
            item.setConsensusCore(normalCore.add(supplyCore));

            item.setKeepPromiseCore(BigDecimal.ZERO);
            item.setPreDeductCore(BigDecimal.ZERO);
            item.setWaitStockCore(item.getConsensusCore());
            item.setMatchCore(BigDecimal.ZERO);
            item.setTotalMatchCore(BigDecimal.ZERO);
            item.setTotalSystemMatchCore(BigDecimal.ZERO);

            BigDecimal supplyGpu = detail.getSupplyGpuNum() == null ? BigDecimal.ZERO : new BigDecimal(detail.getSupplyGpuNum());
            BigDecimal normalGpu = item.getConsensusGpu() == null ? BigDecimal.ZERO : item.getConsensusGpu();
            item.setConsensusGpu(normalGpu.add(supplyGpu));
            item.setTotalMatchGpu(BigDecimal.ZERO);
        }

        // 实际累计预扣量、满足量
        for (OrderItemSatisfyRateDO item : res) {
            String key = String.join("@", item.getZoneName(), item.getInstanceType());
            BigDecimal deductCore = deductCoreMap.getOrDefault(key, BigDecimal.ZERO);
            BigDecimal tempCore;
            if (deductCore.compareTo(item.getConsensusCore()) > 0) {
                tempCore = item.getConsensusCore();
                deductCore = deductCore.subtract(item.getConsensusCore());
                item.setTotalMatchCore(tempCore.max(item.getTotalMatchCore()));
                item.setCumulativePreDeductCore(tempCore);
            }else {
                if (deductCore.compareTo(BigDecimal.ZERO) > 0) {
                    tempCore = deductCore;
                    deductCore = BigDecimal.ZERO;
                    item.setTotalMatchCore(tempCore.max(item.getTotalMatchCore()));
                    item.setCumulativePreDeductCore(tempCore);
                }else {
                    item.setCumulativePreDeductCore(BigDecimal.ZERO);
                }
            }
            deductCoreMap.put(key, deductCore);

            BigDecimal deductGpu = deductGpuMap.getOrDefault(key, BigDecimal.ZERO);
            BigDecimal tempGpu;
            if (deductGpu.compareTo(item.getConsensusGpu()) > 0) {
                tempGpu = item.getConsensusGpu();
                deductGpu = deductGpu.subtract(item.getConsensusGpu());
                item.setTotalMatchGpu(tempGpu.max(item.getTotalMatchGpu()));
                item.setCumulativePreDeductGpu(item.getTotalMatchGpu());
            }else {
                if (deductGpu.compareTo(BigDecimal.ZERO) > 0) {
                    tempGpu = deductGpu;
                    deductGpu = BigDecimal.ZERO;
                    item.setTotalMatchGpu(tempGpu.max(item.getTotalMatchGpu()));
                    item.setCumulativePreDeductGpu(item.getTotalMatchGpu());
                }else {
                    item.setCumulativePreDeductGpu(BigDecimal.ZERO);
                }
            }
            deductGpuMap.put(key, deductGpu);
        }
        return res;
    }

    /**
     *  日弹性订单计算满足度<br/>
     *  1、从开始购买时间开始，每一天统计预扣成功数量 <br/>
     *  2、共识维度满足量 = Sum(预扣成功量)/天数 <br/><br/>
     *
     *  周弹性、月弹性订单计算满足度<br/>
     *  1、按周期维度计算，有预扣单的才算有效周期 <br/>
     *  2、单个周期满足量 = 周期内，预扣单累计预扣成功数量 <br/>
     *  3、共识维度满足量 = Sum(有效周期的满足量) / 有效周期数量 <br/><br/>
     *
     *  计算共识需求每一天的预扣成功量，每一天的预扣成功量不会大于共识需求的需求量，不在共识需求开始结束范围内的预扣不用计算 <br/>
     *  <a href="https://iwiki.woa.com/p/4013891009#%E8%A7%84%E5%88%99%E6%A6%82%E8%A7%88">规则概览</a>
     * @param list 供应方案
     * @param order 订单
     * @param statDate 统计日期，一般是今天计算前一天的数据， statDate = LocalDate.now().minusDays(1)
     * @param deductList 预扣块
     * @param config 弹性周期配置，周弹性、月弹性使用
     */
    public static List<OrderItemSatisfyRateDO> fromPreDeductGridList(List<OrderSupplyPlanDetailWithPlanDTO> list,
            OrderInfoDO order, LocalDate statDate, List<PreDeductGridSimpleDTO> deductList, ElasticCycleConfig config)  {
        if (ListUtils.isEmpty(list) || order == null) {
            return new ArrayList<>();
        }
        if (deductList == null) {
            deductList = new ArrayList<>();
        }
        OrderElasticType elasticType = OrderElasticType.getByTypeName(order.getElasticType());
        if (elasticType == null || !order.cycleElasticReturnTrue()) {
            throw BizException.makeThrow("非日弹性、周弹性、月弹性订单：%s", order.getOrderNumber());
        }
        String orderNumber = order.getOrderNumber();
        List<OrderSupplyPlanDetailWithPlanDTO> datas = list.stream().filter(o ->
                        o != null && o.getPlan() != null
                                && Strings.isNotBlank(o.getOrderNumber()) && Objects.equals(o.getOrderNumber(), orderNumber))
                .collect(Collectors.toList());
        ListUtils.sortAscNullLast(datas, OrderSupplyPlanDetailDO::getConsensusBeginBuyDate);
        if (ListUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        Map<String, List<PreDeductGridSimpleDTO>> deuctMap = new HashMap<>();
        for (PreDeductGridSimpleDTO itemDO : deductList) {
            if (itemDO == null || !Objects.equals(orderNumber, itemDO.getOrderNumber())) {
                continue;
            }
            itemDO.initSomeData();
            String key = String.join("@", itemDO.getPlanZoneName(), itemDO.getPlanInstanceType());
            List<PreDeductGridSimpleDTO> gridList = deuctMap.computeIfAbsent(key, k -> new ArrayList<>());
            gridList.add(itemDO);
        }

        Map<String, OrderItemSatisfyRateDO> resMap = new HashMap<>();
        List<OrderItemSatisfyRateDO> res = new ArrayList<>();
        for (OrderSupplyPlanDetailWithPlanDTO detail : datas) {
            String supplyKey =  String.join("@", detail.getSupplyZoneName(), detail.getSupplyInstanceType(),
                    detail.getConsensusBeginBuyDate().toString(), detail.getPlan().getMatchType());

            OrderItemSatisfyRateDO item = resMap.get(supplyKey);
            if (item == null) {
                item = new OrderItemSatisfyRateDO();
                resMap.put(supplyKey, item);
                res.add(item);

                item.setInstanceType(detail.getSupplyInstanceType());
                item.setBeginBuyDate(detail.getConsensusBeginBuyDate());
                item.setOrderNumber(detail.getOrderNumber());
                item.setAppId(order.getAppId());
                item.setCustomerShortName(order.getCustomerShortName());
                item.setCustomerUin(order.getCustomerUin());
                item.setIndustryDept(order.getIndustryDept());
                item.setProduct(order.getProduct());
                item.setOrderType(order.getOrderType());
                item.setWarZone(order.getWarZone());
                item.setZoneName(detail.getSupplyZoneName());
                item.setRegionName(detail.getSupplyRegionName());
                item.setRegion(detail.getSupplyRegion());
                item.setZone(detail.getSupplyZone());
                item.setCalcDate(statDate);
                item.setMainResult(true);
                item.setMatchType(detail.getPlan().getMatchType());
                item.setMatchTypeName(detail.getPlan().getMatchTypeName());

                item.setKeepPromiseCore(BigDecimal.ZERO);
                item.setPreDeductCore(BigDecimal.ZERO);
                item.setWaitStockCore(item.getConsensusCore());
                item.setMatchCore(BigDecimal.ZERO);
                item.setTotalMatchCore(BigDecimal.ZERO);
                item.setTotalSystemMatchCore(BigDecimal.ZERO);

                item.setTotalMatchGpu(BigDecimal.ZERO);
            }

            BigDecimal supplyCore = detail.getSupplyCoreNum() == null ? BigDecimal.ZERO : new BigDecimal(detail.getSupplyCoreNum());
            BigDecimal normalCore = item.getConsensusCore() == null ? BigDecimal.ZERO : item.getConsensusCore();
            item.setConsensusCore(normalCore.add(supplyCore));

            BigDecimal supplyGpu = detail.getSupplyGpuNum() == null ? BigDecimal.ZERO : new BigDecimal(detail.getSupplyGpuNum());
            BigDecimal normalGpu = item.getConsensusGpu() == null ? BigDecimal.ZERO : item.getConsensusGpu();
            item.setConsensusGpu(normalGpu.add(supplyGpu));
        }

        for (OrderItemSatisfyRateDO item : res) {
            String deductKey = String.join("@", item.getZoneName(), item.getInstanceType());
            List<PreDeductGridSimpleDTO> gridList = deuctMap.getOrDefault(deductKey, new ArrayList<>());
            // 每天需要的核心数、卡数
            BigDecimal dayWaitAllocateCore = item.getConsensusCore().subtract(item.getTotalSystemMatchCore());
            BigDecimal dayWaitAllocateGpu = item.getConsensusGpu().subtract(item.getTotalMatchGpu());

            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap = ListUtils.toMapList(gridList,
                    PreDeductGridSimpleDTO::getDate, Function.identity());

            if (elasticType == OrderElasticType.BY_DAY) {
                LocalDate endDate = order.getEndBuyDate();
                LocalDate end =  endDate.isAfter(statDate) ?  statDate : endDate;
                // 日弹性：共识维度满足量 = Sum(预扣成功量)/共识需求天数
                Tuple2<BigDecimal, BigDecimal> oneCycleRes = oneCycleAvg(item.getBeginBuyDate(),
                        end, dayWaitAllocateCore, dayWaitAllocateGpu, deductDateMap);
                BigDecimal totalMatchCore = oneCycleRes._1.add(item.getTotalMatchCore());
                BigDecimal totalMatchGpu = oneCycleRes._2.add(item.getTotalMatchGpu());
                item.setTotalMatchCore(totalMatchCore);
                item.setTotalSystemMatchCore(totalMatchCore);
                item.setCumulativePreDeductCore(totalMatchCore);
                item.setTotalMatchGpu(totalMatchGpu);
                item.setCumulativePreDeductGpu(totalMatchGpu);
            } else {
                // 周弹性、月弹性：共识维度满足量 = Sum(有效周期的满足量) / 有效周期数量 。
                // 其中 单个周期满足量 = 周期内，预扣单累计预扣成功数量
                int cycleCount = 0;
                BigDecimal cycleTotalDeductCore = BigDecimal.ZERO;
                BigDecimal cycleTotalDeductGpu = BigDecimal.ZERO;
                for (PreDeductPlanTime planTime : config.getPreDeductPlanTimeList()) {
                    if (statDate.isBefore(planTime.getStartPreDeductDate())) {
                        // 未到周期预扣开始日期，不纳入周期计算
                        continue;
                    }
                    LocalDate end =  planTime.getEndPreDeductDate().isAfter(statDate)
                            ?  statDate : planTime.getEndPreDeductDate();
                    Tuple3<BigDecimal, BigDecimal, Boolean> oneCycleRes = oneCycleMax(planTime.getStartPreDeductDate(),
                            end, dayWaitAllocateCore, dayWaitAllocateGpu, deductDateMap);
                    cycleTotalDeductCore = cycleTotalDeductCore.add(oneCycleRes._1);
                    cycleTotalDeductGpu = cycleTotalDeductGpu.add(oneCycleRes._2);
                    if (oneCycleRes._3 != null && oneCycleRes._3) {
                        // 有预扣才算有效周期
                        cycleCount++;
                    }
                }
                BigDecimal totalMatchCore =  BigDecimal.ZERO;
                BigDecimal totalMatchGpu =  BigDecimal.ZERO;
                if (cycleCount > 0) {
                    totalMatchCore = cycleTotalDeductCore.divide(new BigDecimal(cycleCount),
                            2, RoundingMode.HALF_UP);
                    totalMatchGpu = cycleTotalDeductGpu.divide(new BigDecimal(cycleCount),
                            2, RoundingMode.HALF_UP);
                }
                totalMatchCore = totalMatchCore.add(item.getTotalMatchCore());
                item.setTotalMatchCore(totalMatchCore);
                item.setTotalSystemMatchCore(totalMatchCore);
                item.setCumulativePreDeductCore(totalMatchCore);

                totalMatchGpu = totalMatchGpu.add(item.getTotalMatchGpu());
                item.setTotalMatchGpu(totalMatchGpu);
                item.setCumulativePreDeductGpu(totalMatchGpu);
            }
        }
        return res;
    }

    /**
     * 日弹性：计算共识需求单个预扣周期内每一天的预扣成功量，每一天的预扣成功量不会大于共识需求的需求量，不在共识需求开始结束范围内的预扣不用计算<br/>
     * 单个周期内共识维度满足量 = Sum(单个周期内预扣成功量)/单个周期内天数
     * @param begin 单个周期开始日期
     * @param end 单个周期结束日期
     * @param dayWaitAllocateCore 每天待分配的核心数
     * @param dayWaitAllocateGpu 每天待分配的卡数
     * @param deductDateMap 预扣日期-预扣块列表
     * @return 返回二元组，第一个元素为单个周期内核心数满足量，第二个元素为单个周期内GPU满足量
     */
    public static Tuple2<BigDecimal, BigDecimal> oneCycleAvg(LocalDate begin, LocalDate end,
            BigDecimal dayWaitAllocateCore, BigDecimal dayWaitAllocateGpu,
            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap) {
        int days = 0;
        LocalDate date = begin;
        BigDecimal totalDeductCore = BigDecimal.ZERO;
        BigDecimal totalDeductGpu = BigDecimal.ZERO;
        while (!date.isAfter(end)) {
            if (days > 1000) {
                throw BizException.makeThrow(
                        "弹性订单单周期内预扣天数超过1000，超过满足度按天计算循环阈值，联系开发dotyou解决");
            }
            List<PreDeductGridSimpleDTO> gridDateList = deductDateMap.get(date);
            date = date.plusDays(1);
            days++;
            if (ListUtils.isEmpty(gridDateList)) {
                continue;
            }
            Tuple2<BigDecimal, BigDecimal> oneDay = oneDayTotalDeduct(gridDateList,
                    dayWaitAllocateCore, dayWaitAllocateGpu);
            totalDeductCore = totalDeductCore.add(oneDay._1);
            totalDeductGpu = totalDeductGpu.add(oneDay._2);
        }
        if (days <= 0) {
            return Tuple.of(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        BigDecimal totalMatchCore = totalDeductCore.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
        BigDecimal totalMatchGpu = totalDeductGpu.divide(new BigDecimal(days), 2, RoundingMode.HALF_UP);
        return Tuple.of(totalMatchCore, totalMatchGpu);
    }

    /**
     * 周弹性、月弹性：单个周期满足量 = 周期内，预扣成功峰值（不大于共识需求的需求量）
     * @param begin 单个周期开始日期
     * @param end 单个周期结束日期
     * @param dayWaitAllocateCore 每天待分配的核心数
     * @param dayWaitAllocateGpu 每天待分配的卡数
     * @param deductDateMap 预扣日期-预扣块列表
     * @return 返回二元组，第一个元素为单个周期内核心数满足量，第二个元素为单个周期内GPU满足量，第三个元素为是否有预扣
     */
    public static Tuple3<BigDecimal, BigDecimal, Boolean> oneCycleMax(LocalDate begin, LocalDate end,
            BigDecimal dayWaitAllocateCore, BigDecimal dayWaitAllocateGpu,
            Map<LocalDate, List<PreDeductGridSimpleDTO>> deductDateMap) {
        int days = 0;
        LocalDate date = begin;
        BigDecimal maxCore = BigDecimal.ZERO;
        BigDecimal maxGpu = BigDecimal.ZERO;
        boolean hasDeduct = false;
        while (!date.isAfter(end)) {
            if (days > 1000) {
                throw BizException.makeThrow(
                        "弹性订单单周期内预扣天数超过1000，超过满足度按天计算循环阈值，联系开发dotyou解决");
            }
            List<PreDeductGridSimpleDTO> gridDateList = deductDateMap.get(date);
            days++;
            date = date.plusDays(1);
            if (ListUtils.isEmpty(gridDateList)) {
                continue;
            } else {
                hasDeduct = true;
            }
            Tuple2<BigDecimal, BigDecimal> oneDay = oneDayTotalDeduct(gridDateList,
                    dayWaitAllocateCore, dayWaitAllocateGpu);
            maxCore = maxCore.max(oneDay._1);
            maxGpu = maxGpu.max(oneDay._2);
        }
        // 不能大于待分配量（不能大于共识需求量）
        return Tuple.of(maxCore.min(dayWaitAllocateCore), maxGpu.min(dayWaitAllocateGpu), hasDeduct);
    }

    private static Tuple2<BigDecimal, BigDecimal> oneDayTotalDeduct(List<PreDeductGridSimpleDTO> gridDateList,
            BigDecimal dayWaitAllocateCore, BigDecimal dayWaitAllocateGpu) {
        BigDecimal waitAllocateCore = dayWaitAllocateCore;
        BigDecimal waitAllocateGpu = dayWaitAllocateGpu;
        BigDecimal deductCore = BigDecimal.ZERO;
        BigDecimal deductGpu = BigDecimal.ZERO;
        for (PreDeductGridSimpleDTO grid : gridDateList) {
            if (waitAllocateCore.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal gridMatchCore = waitAllocateCore.subtract(grid.getWaitAllocateCpu());
                if (gridMatchCore.compareTo(BigDecimal.ZERO) <= 0) {
                    deductCore = deductCore.add(waitAllocateCore);
                    grid.setWaitAllocateCpu(grid.getWaitAllocateCpu().subtract(waitAllocateCore));
                    waitAllocateCore = BigDecimal.ZERO;
                } else {
                    deductCore = deductCore.add(grid.getWaitAllocateCpu());
                    waitAllocateCore = waitAllocateCore.subtract(grid.getWaitAllocateCpu());
                    grid.setWaitAllocateCpu(BigDecimal.ZERO);
                }
            }

            if (waitAllocateGpu.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal gridMatchGpu = waitAllocateGpu.subtract(grid.getWaitAllocateGpu());
                if (gridMatchGpu.compareTo(BigDecimal.ZERO) <= 0) {
                    deductGpu = deductGpu.add(waitAllocateGpu);
                    grid.setWaitAllocateGpu(grid.getWaitAllocateGpu().subtract(waitAllocateGpu));
                    waitAllocateGpu = BigDecimal.ZERO;
                } else {
                    deductGpu = deductGpu.add(grid.getWaitAllocateGpu());
                    waitAllocateGpu = waitAllocateGpu.subtract(grid.getWaitAllocateGpu());
                    grid.setWaitAllocateGpu(BigDecimal.ZERO);
                }
            }

            if (waitAllocateCore.compareTo(BigDecimal.ZERO) <= 0
                    && waitAllocateGpu.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
        }
        return Tuple.of(deductCore, deductGpu);
    }

}
