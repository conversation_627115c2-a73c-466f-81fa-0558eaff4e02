package cloud.demand.app.modules.order.service.impl;

import cloud.demand.app.common.config.DBList;
import cloud.demand.app.common.excel.DotExcelReadUtil;
import cloud.demand.app.common.excel.DotExcelReadUtil.DotExcelBuilder;
import cloud.demand.app.common.excel.ExcelGroupEnum;
import cloud.demand.app.common.excel.LocalDateStringConverter;
import cloud.demand.app.common.excel.LocalTimeStringConverter;
import cloud.demand.app.common.excel.checker.CustomerUinColumnChecker;
import cloud.demand.app.common.excel.checker.NotBlankColumnChecker;
import cloud.demand.app.common.excel.checker.NotInStringListColumnChecker;
import cloud.demand.app.common.excel.checker.NumberColumnChecker;
import cloud.demand.app.common.excel.checker.RegexpColumnChecker;
import cloud.demand.app.common.excel.core.ErrorMessage;
import cloud.demand.app.common.excel.core.ReadResult;
import cloud.demand.app.common.utils.BatchUtil;
import cloud.demand.app.common.utils.LoginUtils;
import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.common.utils.ORMUtils.WhereContent;
import cloud.demand.app.entity.plan.StaticZoneDO;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.industry_cockpit.v3.entity.DwdTxyAppidInfoCfDO;
import cloud.demand.app.modules.mrpv2.service.MrpV2DictService;
import cloud.demand.app.modules.order.constant.OrderConstant;
import cloud.demand.app.modules.order.dto.OrderQuotaDTO;
import cloud.demand.app.modules.order.dto.OrderQuotaExcelDTO;
import cloud.demand.app.modules.order.dto.OrderQuotaExcelDTO.QuotaResultHandler;
import cloud.demand.app.modules.order.dto.OrderQuotaExcelDTO.QuotaRowHandler;
import cloud.demand.app.modules.order.dto.req.DeliverRecordQueryReq;
import cloud.demand.app.modules.order.dto.req.OrderExportReq;
import cloud.demand.app.modules.order.dto.req.OrderQuotaQueryReq;
import cloud.demand.app.modules.order.dto.req.QuotaDetailReq;
import cloud.demand.app.modules.order.dto.req.QuotaMonitorReq;
import cloud.demand.app.modules.order.dto.resp.DeliverSummaryDTO;
import cloud.demand.app.modules.order.dto.resp.OrderQuotaResp;
import cloud.demand.app.modules.order.dto.resp.QuotaDetailResp;
import cloud.demand.app.modules.order.dto.resp.QuotaMonitorResp;
import cloud.demand.app.modules.order.entity.DeliverQuotaRecordDO;
import cloud.demand.app.modules.order.entity.DeliverQuotaRecordItemDO;
import cloud.demand.app.modules.order.entity.OrderInfoDO;
import cloud.demand.app.modules.order.entity.OrderQuotaDO;
import cloud.demand.app.modules.order.entity.std_table.AdsOrderQuotaDfDO;
import cloud.demand.app.modules.order.enums.OrderStatusEnum;
import cloud.demand.app.modules.order.service.OrderQuotaService;
import cloud.demand.app.modules.p2p.common.P2PInstanceModelParse;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandAuthDO;
import cloud.demand.app.modules.p2p.industry_demand.entity.IndustryDemandIndustryWarZoneDictDO;
import cloud.demand.app.modules.p2p.industry_demand.enums.IndustryDemandAuthRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CrpQuotaCreateReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CrpQuotaCreateReq.QuotaItem;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.CrpQuotaCreateResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryReq;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryResp;
import cloud.demand.app.modules.p2p.ppl13week.dto.yunxiao.QuotaQueryResp.QuotaDetail;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum;
import cloud.demand.app.modules.p2p.ppl13week.service.PplDictService;
import cloud.demand.app.modules.p2p.ppl13week.service.YunxiaoAPIService;
import cloud.demand.app.modules.sop.domain.ReturnT;
import cloud.demand.app.web.model.common.DownloadBean;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillWrapper;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import com.pugwoo.wooutils.lang.NumberUtils;
import com.pugwoo.wooutils.redis.Synchronized;
import io.vavr.Tuple2;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import yunti.boot.exception.BizException;

@Slf4j
@Service
public class OrderQuotaServiceImpl implements OrderQuotaService {

    @Resource
    private DictService dictService;
    @Resource
    private PplDictService pplDictService;
    @Resource
    private DBHelper demandDBHelper;

    @Resource
    private YunxiaoAPIService yunxiaoAPIService;

    @Resource
    private MrpV2DictService mrpV2DictService;


    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_QUOTA_LOCK_NAMESPACE,
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000)
    public void initOrderQuota() {
        WhereSQL whereSQL = new WhereSQL();
//        whereSQL.and("begin_buy_date >= ?", "2024-08-01");
//        whereSQL.and("begin_buy_date <= ?", "2024-10-31");
        whereSQL.and("order_status not in (?)",
                Arrays.asList(OrderStatusEnum.DRAFT.getCode(), OrderStatusEnum.CANCELED.getCode()));
        whereSQL.and("order_category = ?", Ppl13weekProductTypeEnum.CVM.getCode());
        whereSQL.and("app_role = ?", Ppl13weekProductTypeEnum.CVM.getCode());
        String quotaSql = ORMUtils.getSql("/sql/order/order_quota.sql");
        quotaSql = quotaSql.replace("${whereCondition}", whereSQL.getSQL());
        List<OrderQuotaDTO> orderQuotaDTOList = DBList.ckcldStdCrpDBHelper.getRaw(OrderQuotaDTO.class, quotaSql,
                whereSQL.getParams());

        Map<String, List<OrderQuotaDTO>> groupMap = orderQuotaDTOList.stream().collect(Collectors.groupingBy(
                v -> String.join("@", v.getDemandYear().toString(), v.getDemandMonth().toString(), v.getCustomerUin(),
                        v.getZoneName(), v.getBillType(), v.getInstanceType(),
                        v.getQuotaType())));

        List<OrderQuotaDO> orderQuotaDOList = new ArrayList<>();
        for (Entry<String, List<OrderQuotaDTO>> entry : groupMap.entrySet()) {
            String k = entry.getKey();
            List<OrderQuotaDTO> v = entry.getValue();
            OrderQuotaDO orderQuotaDO = new OrderQuotaDO();
            OrderQuotaDTO orderQuotaDTO = v.get(0);
            BeanUtils.copyProperties(orderQuotaDTO, orderQuotaDO);
            int instanceNum = v.stream().mapToInt(OrderQuotaDTO::getInstanceNum).sum();
            int totalCore = v.stream().mapToInt(OrderQuotaDTO::getTotalCore).sum();
            int buySatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getBuySatisfyCore).sum();
            int moveSatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getMoveSatisfyCore).sum();
            int stockSatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getStockSatisfyCore).sum();
            orderQuotaDO.setInstanceNum(instanceNum);
            orderQuotaDO.setTotalCore(totalCore);
            orderQuotaDO.setBuySatisfyCore(buySatisfyCore);
            orderQuotaDO.setStockSatisfyCore(stockSatisfyCore + moveSatisfyCore);
//            orderQuotaDO.setQuotaNum(orderQuotaDTO.getQuotaType().contains(".") ? instanceNum : totalCore);
            orderQuotaDO.setInstanceNumQuota(0);
            orderQuotaDO.setCoreQuota(0);
            orderQuotaDO.setDemandYearMonth(
                    orderQuotaDTO.getDemandYear().toString() + "年" + orderQuotaDTO.getDemandMonth() + "月");
            orderQuotaDO.setSource("ORDER");
            orderQuotaDO.setOrderNumberIds(
                    Strings.join(";", v.stream().map(OrderQuotaDTO::getOrderNumberId).collect(Collectors.toList())));
            orderQuotaDOList.add(orderQuotaDO);
        }
        demandDBHelper.insert(orderQuotaDOList);
    }

    @Override
    public List<OrderQuotaResp> queryOrderQuota(OrderQuotaQueryReq req) {
        String userName = LoginUtils.getUserNameWithSystem();
        WhereContent whereContent = quotaCheckPermission(userName, "");

        // 只查出当月以后的
//        whereContent.addAnd("demand_year > ? or (demand_year = ? and demand_month >= ?)",
//                LocalDate.now().getYear(), LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getDemandYearMonth, req.getDemandYearMonth());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getCustomerUin, req.getCustomerUin());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getIndustryDept, req.getIndustryDept());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getAppId, req.getAppId());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getCustomerUin, req.getCustomerUin());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getCustomerShortName, req.getCustomerShortName());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getRegionName, req.getRegionName());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getZoneName, req.getZoneName());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getCustomhouseTitle, req.getCustomhouseTitle());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getBillType, req.getBillType());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getQuotaType, req.getQuotaType());
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getQuotaManageType, req.getQuotaManageType());

        List<String> purchaseInstanceType = mrpV2DictService.getPurchaseInstanceType();
        Map<String, StaticZoneDO> zoneMap = dictService.getAllPlanZoneInfosGroupByName();

        if (req.getIsPurchaseInstanceType() != null) {
            if (req.getIsPurchaseInstanceType()) {
                if (ListUtils.isNotEmpty(req.getInstanceType())) {
                    req.getInstanceType().retainAll(purchaseInstanceType);
                } else {
                    req.setInstanceType(purchaseInstanceType);
                }
                // 机型取交集后， 若无数据，返回空列表。
                if (ListUtils.isEmpty(req.getInstanceType())) {
                    return new ArrayList<>();
                }
            } else {
                req.setInstanceTypeNotIn(purchaseInstanceType);
            }
        }
        whereContent.andInIfValueNotEmpty(OrderQuotaDO::getInstanceType, req.getInstanceType());
        whereContent.andNotInIfValueNotEmpty(OrderQuotaDO::getInstanceType, req.getInstanceTypeNotIn());

        List<OrderQuotaResp> all = demandDBHelper.getAll(OrderQuotaResp.class, whereContent.getSql(),
                whereContent.getParams());
        OrderQuotaResp.build(all, purchaseInstanceType, zoneMap);
        return all;
    }

    @Override
    public DownloadBean exportOrderQuota(String fileNamePrefix, OrderExportReq req) {
        List<OrderQuotaResp> orderQuotaList = new ArrayList<>();
        if (req.getQueryReq() != null) {
            orderQuotaList = queryOrderQuota(req.getQueryReq());
        }

        InputStream template = IOUtils.readClasspathResourceInputStream("excel/order/order_quota_export.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("quota", orderQuotaList), writeSheet).finish();
        String fileName = fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    public DownloadBean deliverRecordExport(String fileNamePrefix, Long recordId) {
        List<DeliverQuotaRecordItemDO> orderQuotaList = demandDBHelper.getAll(DeliverQuotaRecordItemDO.class,
                "where deliver_id = ?", recordId);
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/order/order_quota_export.xlsx");
        ByteArrayOutputStream out = new ByteArrayOutputStream();

        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();

        excelWriter.fill(new FillWrapper("quota", orderQuotaList), writeSheet).finish();
        String fileName = fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_QUOTA_LOCK_NAMESPACE,
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000)
    public void refreshOrderQuota(LocalDate localDate) {
        if (localDate == null) {
            localDate = LocalDate.now();
        }
        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("begin_buy_date >= ?",
                cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonthLocalDate(localDate.toString()));
        whereSQL.and("order_status not in (?)",
                Arrays.asList(OrderStatusEnum.DRAFT.getCode(), OrderStatusEnum.CANCELED.getCode()));
        whereSQL.and("order_category = ?", Ppl13weekProductTypeEnum.CVM.getCode());
        whereSQL.and("app_role = ?", Ppl13weekProductTypeEnum.CVM.getCode());
        String quotaSql = ORMUtils.getSql("/sql/order/order_quota.sql");
        quotaSql = quotaSql.replace("${whereCondition}", whereSQL.getSQL());
        List<OrderQuotaDTO> newOrderQuotaList = DBList.ckcldStdCrpDBHelper.getRaw(OrderQuotaDTO.class, quotaSql,
                whereSQL.getParams());

        Map<String, List<OrderQuotaDTO>> newQuotaMap = newOrderQuotaList.stream().collect(Collectors.groupingBy(
                v -> String.join("@", v.getDemandYear().toString(), v.getDemandMonth().toString(), v.getCustomerUin(),
                        v.getZoneName(), v.getBillType(), v.getInstanceType(),
                        v.getQuotaType())));

        List<OrderQuotaDO> oldOrderQuotaList = demandDBHelper.getAll(OrderQuotaDO.class,
                "where demand_year > ? or (demand_year = ? and demand_month >= ?)",
                LocalDate.now().getYear(), LocalDate.now().getYear(), LocalDate.now().getMonthValue());
        Map<String, List<OrderQuotaDO>> oldQuotaMap = oldOrderQuotaList.stream().collect(Collectors.groupingBy(
                v -> String.join("@", v.getDemandYear().toString(), v.getDemandMonth().toString(), v.getCustomerUin(),
                        v.getZoneName(), v.getBillType(), v.getInstanceType(),
                        v.getQuotaType())));

        // 1.先处理前一天有的情况
        oldQuotaMap.forEach((k, v) -> {
            OrderQuotaDO orderQuotaDO = v.get(0);
            List<OrderQuotaDTO> newQuotaData = newQuotaMap.get(k);
            if (ListUtils.isNotEmpty(newQuotaData)) {
                // 如果不为空则更新
                int instanceNum = newQuotaData.stream().mapToInt(OrderQuotaDTO::getInstanceNum).sum();
                int totalCore = newQuotaData.stream().mapToInt(OrderQuotaDTO::getTotalCore).sum();
                int buySatisfyCore = newQuotaData.stream().mapToInt(OrderQuotaDTO::getBuySatisfyCore).sum();
                int moveSatisfyCore = newQuotaData.stream().mapToInt(OrderQuotaDTO::getMoveSatisfyCore).sum();
                int stockSatisfyCore = newQuotaData.stream().mapToInt(OrderQuotaDTO::getStockSatisfyCore).sum();
                orderQuotaDO.setInstanceNum(instanceNum);
                orderQuotaDO.setTotalCore(totalCore);
                orderQuotaDO.setBuySatisfyCore(buySatisfyCore);
                orderQuotaDO.setStockSatisfyCore(stockSatisfyCore + moveSatisfyCore);
                orderQuotaDO.setOrderNumberIds(
                        Strings.join(";", newQuotaData.stream().map(OrderQuotaDTO::getOrderNumberId)
                                .collect(Collectors.toList())));
            } else {
                // 如果为空则将原来的数据调整为0
                orderQuotaDO.setInstanceNum(0);
                orderQuotaDO.setTotalCore(0);
                orderQuotaDO.setBuySatisfyCore(0);
                orderQuotaDO.setStockSatisfyCore(0);
                orderQuotaDO.setOrderNumberIds(null);
            }
            newQuotaMap.remove(k);
        });

        // 2.在处理今天有，前一天没有的情况
        List<OrderQuotaDO> insertList = new ArrayList<>();
        newQuotaMap.forEach((k, v) -> {
            OrderQuotaDO orderQuotaDO = new OrderQuotaDO();
            OrderQuotaDTO orderQuotaDTO = v.get(0);
            BeanUtils.copyProperties(orderQuotaDTO, orderQuotaDO);
            int instanceNum = v.stream().mapToInt(OrderQuotaDTO::getInstanceNum).sum();
            int totalCore = v.stream().mapToInt(OrderQuotaDTO::getTotalCore).sum();
            int buySatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getBuySatisfyCore).sum();
            int moveSatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getMoveSatisfyCore).sum();
            int stockSatisfyCore = v.stream().mapToInt(OrderQuotaDTO::getStockSatisfyCore).sum();
            orderQuotaDO.setBillType(orderQuotaDTO.getBillType().equals(YunxiaoPayModeEnum.CDHPAID.getCode()) ?
                    YunxiaoPayModeEnum.UNDERWRITE.getCode() : orderQuotaDTO.getBillType());
            orderQuotaDO.setInstanceNum(instanceNum);
            orderQuotaDO.setTotalCore(totalCore);
            orderQuotaDO.setBuySatisfyCore(buySatisfyCore);
            orderQuotaDO.setStockSatisfyCore(stockSatisfyCore + moveSatisfyCore);
            orderQuotaDO.setInstanceNumQuota(0);
            orderQuotaDO.setCoreQuota(0);
            orderQuotaDO.setDemandYearMonth(
                    orderQuotaDTO.getDemandYear().toString() + "年" + orderQuotaDTO.getDemandMonth() + "月");
            orderQuotaDO.setSource("ORDER");
            orderQuotaDO.setOrderNumberIds(
                    Strings.join(";", v.stream().map(OrderQuotaDTO::getOrderNumberId).collect(Collectors.toList())));
            orderQuotaDO.setQuotaManageType(orderQuotaDO.getQuotaType().contains(".") ? "规格管控" : "核心管控");
            orderQuotaDO.setDeliverQuotaNum(0);
            insertList.add(orderQuotaDO);
        });

        demandDBHelper.update(oldOrderQuotaList);
        demandDBHelper.insert(insertList);
    }


    @Override
    public WhereContent quotaCheckPermission(String username, String tablePrefix) {
        WhereContent whereContent = new WhereContent();

        List<IndustryDemandAuthDO> userDemandAuth = demandDBHelper.getAll(IndustryDemandAuthDO.class,
                "where user_name = ?",
                username);

        List<IndustryDemandIndustryWarZoneDictDO> customerDict = demandDBHelper.getAll(
                IndustryDemandIndustryWarZoneDictDO.class);

        if (ListUtils.isNotEmpty(userDemandAuth)) {
            Map<String, IndustryDemandAuthDO> roleMap = userDemandAuth.stream()
                    .collect(Collectors.toMap(IndustryDemandAuthDO::getRole, v -> v));

            if (roleMap.get(IndustryDemandAuthRoleEnum.ADMIN.getCode()) != null) {
                // 管理员 不校验权限
                WhereContent roleContent = new WhereContent();
                roleContent.addAnd("1 = 1");
                whereContent.addOr(roleContent);
            }

            if (roleMap.get(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode()) != null) {
                // 13周预测确认人 可以看到设置行业的相关单据
                List<String> industryList = Arrays.asList(
                        roleMap.get(IndustryDemandAuthRoleEnum.PPL_COMD_INTERFACE.getCode()).getIndustry()
                                .split(";"));
                if (ListUtils.isNotEmpty(industryList)) {
                    WhereContent roleContent = new WhereContent();
                    roleContent.addAnd("industry_dept in (?)", industryList);
                    whereContent.addOr(roleContent);
                }
            }

            if (roleMap.get(IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode()) != null) {

                // 13周数据关注人 可以看到设置行业-战区-客户的相关单据
                WhereContent roleContent = new WhereContent();
                // 行业
                IndustryDemandAuthDO industryDemandAuthDO = roleMap.get(
                        IndustryDemandAuthRoleEnum.INDUSTRY_DATA_FOLLOWER.getCode());
                List<String> industryList = Arrays.asList(
                        industryDemandAuthDO.getIndustry().split(";"));
                if (ListUtils.isNotEmpty(industryList)) {
                    roleContent.addAnd("industry_dept in (?)", industryList);
                }

                // 战区
                if (!industryDemandAuthDO.getIsAllWarZone()) {
                    List<String> warZoneList = new ArrayList<>(Arrays.asList(
                            industryDemandAuthDO.getWarZoneName().split(";")));
                    if (industryDemandAuthDO.getIsAllWarZone()) {
                        warZoneList.add("未关联");
                    }
                    if (ListUtils.isNotEmpty(warZoneList)) {
                        roleContent.addAnd("war_zone in (?)", warZoneList);
                    }
                }

                // 客户
                if (!industryDemandAuthDO.getIsAllCustomer()) {
                    List<String> commonCustomerList = Arrays.asList(
                            industryDemandAuthDO.getCommonCustomerName().split(";"));

                    if (ListUtils.isNotEmpty(commonCustomerList)) {
                        List<String> collect = customerDict.stream()
                                .filter(v -> commonCustomerList.contains(v.getCommonCustomerName()))
                                .map(IndustryDemandIndustryWarZoneDictDO::getCustomerName)
                                .collect(Collectors.toList());
                        roleContent.addAnd("customer_short_name in (?)", collect);
                    }
                }

                if (roleContent.getParams().length == 0) {
                    roleContent.addAnd(" 1 = 2");
                }
                whereContent.addOr(roleContent);
            }

        }

        return whereContent;
    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_QUOTA_LOCK_NAMESPACE,
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "当前有其他用户正在操作配额，请稍后刷新重试。")
    public ReturnT<List<ErrorMessage>> importOrderQuota(MultipartFile file, String industryDept) {

        try {
            // 1.声明单字段Checker
            // 声明不为空checker
            NotBlankColumnChecker notBlankChecker = new NotBlankColumnChecker();

            // 声明配额数量范围checker
            NumberColumnChecker numberColumnChecker = new NumberColumnChecker(0, null);

            // 声明需求年月checker
            RegexpColumnChecker regexpColumnChecker = new RegexpColumnChecker("^\\d{4}年\\d{1,2}月$");

            // 声明可用区checker
            NotInStringListColumnChecker zoneNameChecker = new NotInStringListColumnChecker(
                    dictService.queryZoneNameList());

            // 声明实例类型checker
            NotInStringListColumnChecker instanceTypeChecker = new NotInStringListColumnChecker(
                    pplDictService.queryInstanceType(null));

            // 声明账单类型checker
            NotInStringListColumnChecker billTypeChecker = new NotInStringListColumnChecker(
                    YunxiaoPayModeEnum.nameList());

            // 2.声明行Checker
            // 声明配额行checker
            Map<String, StaticZoneDO> allPlanZoneInfosGroupByName = dictService.getAllPlanZoneInfosGroupByName();
            List<String> instanceType = pplDictService.queryAllInstanceType();
            List<String> instanceModel = pplDictService.queryAllInstanceModel();
            QuotaRowHandler quotaRowHandler = new QuotaRowHandler(allPlanZoneInfosGroupByName, instanceType,
                    instanceModel);
            QuotaResultHandler quotaResultHandler = new QuotaResultHandler();

            // 3.构建excel读取builder
            DotExcelBuilder<OrderQuotaExcelDTO> builder = DotExcelReadUtil.createBuilder(
                            OrderQuotaExcelDTO.class, ExcelGroupEnum.ORDER_QUOTA_IMPORT, 2)
                    .inputStream(file.getInputStream());

            // 4.注册字段校验器
            OrderQuotaExcelDTO demo = new OrderQuotaExcelDTO();
            builder.registerValueCheckerByGetter(demo::getQuotaNum, numberColumnChecker)
                    .registerValueCheckerByGetter(demo::getCustomerUin, notBlankChecker)
                    .registerValueCheckerByGetter(demo::getCustomerUin, new CustomerUinColumnChecker())
                    .registerValueCheckerByGetter(demo::getDemandYearMonth, regexpColumnChecker)
                    .registerValueCheckerByGetter(demo::getInstanceType, instanceTypeChecker)
                    .registerValueCheckerByGetter(demo::getZoneName, zoneNameChecker)
                    .registerValueCheckerByGetter(demo::getBillTypeName, billTypeChecker);

            if (industryDept != null) {
                // 行业部门checker
                NotInStringListColumnChecker industryDeptChecker = new NotInStringListColumnChecker(
                        Arrays.asList(industryDept));
                builder.registerValueCheckerByGetter(demo::getIndustryDept, industryDeptChecker);
            }

            // 5.注册行校验器
            builder.registerRowChecker(quotaRowHandler);

            // 6.注册结果校验器 (当前场景不需要 略过)
            builder.registerResultChecker(quotaResultHandler);

            // 7.读取Excel并校验
            ReadResult<OrderQuotaExcelDTO> result = DotExcelReadUtil.read(builder);

            if (ListUtils.isEmpty(result.getErrors())) {
                if (ListUtils.isEmpty(result.getData())) {
                    throw new BizException("导入配额数据为空，请检查excel");
                }
                // 数据导入
                importDataToQuota(result);
            }
            return ListUtils.isEmpty(result.getErrorsAndSort()) ? ReturnT.ok(result.getErrorsAndSort())
                    : ReturnT.fail(result.getErrorsAndSort());
        } catch (Exception e) {
            if (e instanceof BizException) {
                throw BizException.makeThrow(e.getMessage());
            } else {
                throw BizException.makeThrow("导入失败，联系 oliverychen 解决。" + e.getMessage());
            }
        }

    }

    @Override
    @Transactional("demandTransactionManager")
    @Synchronized(namespace = OrderConstant.ORDER_QUOTA_LOCK_NAMESPACE,
            throwExceptionIfNotGetLock = true, waitLockMillisecond = 1000,
            customExceptionMessage = "当前有其他用户正在操作配额，请稍后刷新重试。")
    public DeliverQuotaRecordDO pushQuotaToYunXiao(String yearMonth, String operateNote) {
        OrderQuotaQueryReq req = new OrderQuotaQueryReq();
        req.setDemandYearMonth(Arrays.asList(yearMonth));
        req.setInstanceType(mrpV2DictService.getPurchaseInstanceType());
        List<OrderQuotaResp> orderQuotaResps = queryOrderQuota(req);
        // 只推送 采购机型、 （可用配额数 != 0 ||   （可用配额数 = 0&& 已下发配额数 !=0） ）
        orderQuotaResps = orderQuotaResps.stream()
                .filter(v -> v.getQuotaNum() != 0 || (v.getQuotaNum() == 0 && v.getDeliverQuotaNum() != 0))
                .collect(Collectors.toList());

        if (ListUtils.isEmpty(orderQuotaResps)) {
            throw new BizException("当前需求年月： " + yearMonth + "无有效数据");
        }

        // 记录日志
        DeliverQuotaRecordDO record = record(yearMonth, orderQuotaResps, operateNote);

        CrpQuotaCreateReq pushReq = new CrpQuotaCreateReq();
        String description = LoginUtils.getUserNameWithSystem() + "下发了" + yearMonth + "的全量需求";

        pushReq.setDemandMonth(
                orderQuotaResps.get(0).getDemandYear() + "-" + cloud.demand.app.common.utils.DateUtils.fillZeroMonth(
                        orderQuotaResps.get(0).getDemandMonth()));
        pushReq.setDescription(description);
        List<QuotaItem> quotaItems = CrpQuotaCreateReq.buildQuotaItem(orderQuotaResps);
        pushReq.setItems(quotaItems);

        CrpQuotaCreateResp crpQuotaCreateResp = yunxiaoAPIService.crpQuotaPush(pushReq);
        if (crpQuotaCreateResp.getSuccess()) {
            for (OrderQuotaResp orderQuotaResp : orderQuotaResps) {
                orderQuotaResp.setDeliverQuotaNum(orderQuotaResp.getQuotaNum());
            }
            List<Long> quotaIds = orderQuotaResps.stream().map(OrderQuotaResp::getId).collect(Collectors.toList());
            demandDBHelper.executeRaw("update order_quota set deliver_quota_num = "
                            + "(CASE WHEN quota_manage_type = '核心管控' THEN core_quota else instance_num_quota END) where id in (?)",
                    quotaIds);
            record.setDeliverStatus("已下发");
            record.setPushVersion(crpQuotaCreateResp.getVersion());
            demandDBHelper.update(record);
        } else {
            record.setDeliverStatus("下发失败");
            record.setErrorMsg(crpQuotaCreateResp.getErrorMsg());
            demandDBHelper.update(record);
        }
        return record;
    }

    public DeliverQuotaRecordDO record(String yearMonth, List<OrderQuotaResp> orderQuotaResps, String operateNote) {
        DeliverQuotaRecordDO deliverQuotaRecordDO = new DeliverQuotaRecordDO();
        deliverQuotaRecordDO.setDemandYearMonth(yearMonth);
        deliverQuotaRecordDO.setOperateUser(LoginUtils.getUserName());
        deliverQuotaRecordDO.setDeliverTime(new Date());
        deliverQuotaRecordDO.setOperateNote(operateNote);
        deliverQuotaRecordDO.setDeliverStatus("下发中");
        deliverQuotaRecordDO.setDeliverTotal(orderQuotaResps.stream().mapToInt(OrderQuotaResp::getCoreQuota).sum());

        demandDBHelper.insert(deliverQuotaRecordDO);

        List<DeliverQuotaRecordItemDO> list = new ArrayList<>();
        for (OrderQuotaResp orderQuotaResp : orderQuotaResps) {
            DeliverQuotaRecordItemDO itemDO = new DeliverQuotaRecordItemDO();
            BeanUtils.copyProperties(orderQuotaResp, itemDO);
            itemDO.setId(null);
            itemDO.setCreateTime(null);
            itemDO.setUpdateTime(null);
            itemDO.setDeliverId(deliverQuotaRecordDO.getId());
            list.add(itemDO);
        }
        demandDBHelper.insertBatchWithoutReturnId(list);
        return deliverQuotaRecordDO;
    }

    @Override
    public List<DeliverQuotaRecordDO> queryDeliverRecord(DeliverRecordQueryReq req) {
        WhereSQL whereSQL = new WhereSQL();

        if (StringUtils.isNotBlank(req.getStartYearMonth())) {
            whereSQL.and(" demand_year_month >= ?", req.getStartYearMonth());
        }
        if (StringUtils.isNotBlank(req.getEndYearMonth())) {
            whereSQL.and(" demand_year_month <= ?", req.getEndYearMonth());
        }
        if (StringUtils.isNotBlank(req.getStartDeliverTime())) {
            whereSQL.and(" deliver_time >= ?", req.getStartDeliverTime());
        }
        if (StringUtils.isNotBlank(req.getEndDeliverTime())) {
            whereSQL.and(" deliver_time <= ?", req.getEndDeliverTime());
        }
        if (ListUtils.isNotEmpty(req.getOperateUser())) {
            whereSQL.and(" operate_user in (?)", req.getOperateUser());
        }
        whereSQL.addOrderBy("id desc");
        List<DeliverQuotaRecordDO> all = demandDBHelper.getAll(DeliverQuotaRecordDO.class, whereSQL.getSQL(),
                whereSQL.getParams());
        if (ListUtils.isEmpty(all)) {
            return new ArrayList<>();
        }

        return all;
    }

    @Override
    public QuotaDetailResp getDeliverDetail(QuotaDetailReq req) {
        DeliverQuotaRecordDO recordDO = demandDBHelper.getOne(DeliverQuotaRecordDO.class,
                "where demand_year_month = ? order by id desc limit 1", req.getDemandYearMonth());
        QuotaDetailResp quotaDetailResp = new QuotaDetailResp();

        WhereSQL whereSQL = new WhereSQL();
        whereSQL.and("demand_year_month = ?", req.getDemandYearMonth());
        whereSQL.and(
                "deliver_quota_num != (CASE WHEN quota_manage_type = '核心管控' THEN core_quota else instance_num_quota END)");
        if (StringUtils.isNotBlank(req.getIndustryDept())) {
            whereSQL.and("industry_dept = ?", req.getIndustryDept());
        }
        List<OrderQuotaDO> result = demandDBHelper.getAll(OrderQuotaDO.class,
                whereSQL.getSQL(), whereSQL.getParams());

        if (recordDO != null) {
            quotaDetailResp.setLastDeliverTime(recordDO.getDeliverTime());
            quotaDetailResp.setOperateUser(recordDO.getOperateUser());
            if (ListUtils.isEmpty(result)) {
                quotaDetailResp.setCurrentStatus("已同步");
            } else {
                quotaDetailResp.setCurrentStatus("已修改待下发");
            }
        } else {
            quotaDetailResp.setCurrentStatus("新建");
        }
        return quotaDetailResp;
    }

    @Override
    public List<DeliverSummaryDTO> queryDeliverSummary(String demandYearMonth) {
        OrderQuotaQueryReq req = new OrderQuotaQueryReq();
        req.setDemandYearMonth(Arrays.asList(demandYearMonth));
        req.setInstanceType(mrpV2DictService.getPurchaseInstanceType());
        List<OrderQuotaResp> orderQuotaResps = queryOrderQuota(req);
        // 只推送 采购机型、 （可用配额数 != 0 ||   （可用配额数 = 0&& 已下发配额数 !=0） ）
        orderQuotaResps = orderQuotaResps.stream()
                .filter(v -> v.getQuotaNum() != 0 || (v.getQuotaNum() == 0 && v.getDeliverQuotaNum() != 0))
                .collect(Collectors.toList());

        if (ListUtils.isEmpty(orderQuotaResps)) {
            return new ArrayList<>();
        }
        Map<String, List<OrderQuotaResp>> collect = orderQuotaResps.stream()
                .collect(Collectors.groupingBy(v -> v.getIndustryDept()));
        List<DeliverSummaryDTO> result = new ArrayList<>();
        collect.forEach(((k, v) -> {
            DeliverSummaryDTO dto = new DeliverSummaryDTO();
            dto.setDemandYearMonth(demandYearMonth);
            dto.setIndustryDept(k);
            dto.setOrderNum(v.stream().mapToInt(OrderQuotaResp::getTotalCore).sum());
            dto.setQuotaNum(v.stream().mapToInt(OrderQuotaResp::getCoreQuota).sum());
            result.add(dto);
        }));
        return result;
    }

    @Override
    @TaskLog(taskName = "initQuotaMonitor")
    public void initQuotaMonitor() {
        LocalDate dataDate = LocalDate.now().minusDays(1);
        DBList.ckcldStdCrpDBHelper.executeRaw(
                "ALTER TABLE std_crp.ads_order_quota_df_local ON CLUSTER default_cluster DROP PARTITION ?",
                dataDate);

        List<String> purchaseInstanceType = mrpV2DictService.getPurchaseInstanceType();
        List<StaticZoneDO> allZoneInfos = dictService.getAllZoneInfos();
        List<String> regionList = allZoneInfos.stream().map(StaticZoneDO::getRegion).distinct()
                .collect(Collectors.toList());

        Set<String> appId = new HashSet<String>();
        // 查询出订单所有AppId
        List<Long> appIdList = demandDBHelper.getRaw(Long.class,
                "select distinct app_id from order_info where deleted = 0");
        // 查询头部客户的客户简称
        List<String> bigCustomerShortName = dictService.getBigCustomerShortName();
        List<DwdTxyAppidInfoCfDO> bigCustomerList = DBList.ckcldStdCrpDBHelper.getAll(DwdTxyAppidInfoCfDO.class,
                "where customer_short_name in (?) or appid in (?)",
                bigCustomerShortName, appIdList);
        List<String> bigCustomerAppIdList = bigCustomerList.stream().map(v -> v.getAppid().toString())
                .collect(Collectors.toList());
        Map<String, DwdTxyAppidInfoCfDO> appIdMap = bigCustomerList.stream()
                .collect(Collectors.toMap(o -> String.valueOf(o.getAppid()), v -> v, (v1, v2) -> v1));
        appId.addAll(appIdList.stream().map(String::valueOf).collect(Collectors.toList()));
        appId.addAll(bigCustomerAppIdList);
//        // todo 测试环境没数据加上的， 发布生产后干掉。
//        appId.add("1300243876");
        log.info("appIdMap的size为：" + appIdMap.size());
        List<AdsOrderQuotaDfDO> result = new ArrayList<>();
        //获取月初切片数据
        List<AdsOrderQuotaDfDO> firstDayData = DBList.ckcldStdCrpDBHelper.getAll(AdsOrderQuotaDfDO.class,
                "where stat_time = ?",
                cloud.demand.app.common.utils.DateUtils.getFirstDayOfMonthLocalDate(dataDate.toString()));
        Map<String, List<AdsOrderQuotaDfDO>> firstDataMap = ListUtils.toMapList(firstDayData,
                o -> String.join("@", o.getAppId(), o.getZoneName(), o.getPayMode(), o.getQuotaKey()), o -> o);

        //获取crp配额
        List<OrderQuotaDO> crpData = demandDBHelper.getAll(OrderQuotaDO.class,
                "where demand_year = ? and demand_month = ?", dataDate.getYear(),
                dataDate.getMonth().getValue());
        Map<String, List<OrderQuotaDO>> crpMap = ListUtils.toMapList(crpData,
                o -> String.join("@", o.getAppId(), o.getZoneName(), o.getBillType(), o.getQuotaType()), o -> o);
        Map<Long, StaticZoneDO> allPlanZoneInfos = dictService.getAllPlanZoneInfos();
        Set<String> missSet = new HashSet<>();
        for (String region : regionList) {

            List<String> appIds = new ArrayList<>(appId);
            List<QuotaDetail> quotaList = new ArrayList<>();

            // 由于appid数量过多，下游服务撑不住，因此appid分批查询
            BatchUtil.syncBatchExec(appIds,500,
                    (batch,processed) -> {
                        QuotaQueryReq req = new QuotaQueryReq(region, new ArrayList<>(batch));
                        QuotaQueryResp quotaQueryResp = yunxiaoAPIService.queryQuota(req);
                        quotaList.addAll(quotaQueryResp.getQuotaList());
                    });

            for (QuotaDetail quotaDetail : quotaList) {
                AdsOrderQuotaDfDO item = new AdsOrderQuotaDfDO();
                DwdTxyAppidInfoCfDO dwdTxyAppidInfoCfDO = appIdMap.get(quotaDetail.getAppId());
                //赋值
                item.setStatTime(dataDate);
                item.setDemandYear(dataDate.getYear());
                item.setDemandMonth(dataDate.getMonth().getValue());
                item.setDemandYearMonth(dataDate.getYear() + "年" + dataDate.getMonth().getValue() + "月");
                //客户信息
                item.setAppId(quotaDetail.getAppId());
                if (dwdTxyAppidInfoCfDO != null) {
                    item.setCustomerUin(String.valueOf(dwdTxyAppidInfoCfDO.getUin()));
                    item.setCustomerShortName(dwdTxyAppidInfoCfDO.getCustomerShortName());
                    item.setCustomerName(dwdTxyAppidInfoCfDO.getCustomerName());
                    item.setIndustryDept(dwdTxyAppidInfoCfDO.getIndustryDept());
                } else {
                    missSet.add(quotaDetail.getAppId());
                }
                //地理信息
                StaticZoneDO staticZoneDO = allPlanZoneInfos.get(Long.parseLong(quotaDetail.getZoneId()));
                if (staticZoneDO == null) {
                    log.info("initQuotaMonitor 缺失zoneId信息:" + quotaDetail.getZoneId());
                    continue;
                }
                item.setZoneName(staticZoneDO.getZoneName());
                item.setCustomhouseTitle(staticZoneDO.getCustomhouseTitle());
                item.setRegionName(staticZoneDO.getRegionName());

                //云霄配额信息
                item.setQuotaId(quotaDetail.getQuotaId());
                item.setQuotaKey(quotaDetail.getQuotaKey());
                item.setQuotaUnit(quotaDetail.getQuotaUnit());
                item.setYunxiaoCurrentQuotaNum(quotaDetail.getQuota());
                item.setQuotaUsage(quotaDetail.getQuotaUsage());
                item.setPayMode(quotaDetail.getPayMode());
                String quotaKey = quotaDetail.getQuotaKey();
                if (quotaKey.contains(".")) {
                    String[] split = quotaKey.split("\\.");
                    item.setInstanceType(split[0]);
                } else {
                    item.setInstanceType(quotaKey);
                }
                item.setIsPurchaseInstanceType(purchaseInstanceType.contains(item.getInstanceType()));
                String key = String.join("@", item.getAppId(), item.getZoneName(), item.getPayMode(),
                        item.getQuotaKey());
                if (!firstDataMap.isEmpty()) {
                    List<AdsOrderQuotaDfDO> list = firstDataMap.get(key);
                    item.setYunxiaoMonthBeginningQuotaNum(NumberUtils.sum(list,
                            AdsOrderQuotaDfDO::getYunxiaoCurrentQuotaNum).intValue());
                } else {
                    item.setYunxiaoMonthBeginningQuotaNum(quotaDetail.getQuota());
                }
                //crp配额信息
                List<OrderQuotaDO> crpList = crpMap.get(key);
                item.setCrpSuggestQuotaNum(NumberUtils.sum(crpList, o -> o.getQuotaNum()).intValue());
                item.setCrpDeliverQuotaNum(NumberUtils.sum(crpList, o -> o.getDeliverQuotaNum()).intValue());
                if (dataDate.getYear() == 2024 && dataDate.getMonthValue() == 10 && item.getCrpDeliverQuotaNum() != 0) {
                    item.setYunxiaoMonthBeginningQuotaNum(0);
                }
                if (item.getQuotaUnit().equals("VCPU")) {
                    item.setCoreToQuotaUsage(item.getQuotaUsage());
                    item.setCoreToYunxiaoMonthBeginningQuotaNum(item.getYunxiaoMonthBeginningQuotaNum());
                    item.setCoreToYunxiaoCurrentQuotaNum(item.getYunxiaoCurrentQuotaNum());
                    item.setCoreToCrpSuggestQuotaNum(item.getCrpSuggestQuotaNum());
                    item.setCoreToCrpDeliverQuotaNum(item.getCrpDeliverQuotaNum());
                } else {
                    // 如果是规格管控 换算为核心数
                    Tuple2<Integer, Integer> cpuRamNum = P2PInstanceModelParse.parseInstanceModel(
                            item.getQuotaKey());
                    Integer coreNum = cpuRamNum._1;
                    item.setCoreToQuotaUsage(item.getQuotaUsage() * coreNum);
                    item.setCoreToYunxiaoMonthBeginningQuotaNum(item.getYunxiaoMonthBeginningQuotaNum() * coreNum);
                    item.setCoreToYunxiaoCurrentQuotaNum(item.getYunxiaoCurrentQuotaNum() * coreNum);
                    item.setCoreToCrpSuggestQuotaNum(item.getCrpSuggestQuotaNum() * coreNum);
                    item.setCoreToCrpDeliverQuotaNum(item.getCrpDeliverQuotaNum() * coreNum);
                }
                result.add(item);
            }
        }
        if (!missSet.isEmpty()) {
            List<String> tempList = new ArrayList<>(missSet);
            List<OrderInfoDO> missInfo = demandDBHelper.getRaw(OrderInfoDO.class,
                    "select * from order_info where app_id in (?)",
                    tempList);
            Map<String, List<OrderInfoDO>> missMap = ListUtils.toMapList(missInfo, OrderInfoDO::getAppId, o -> o);
            result.forEach(o -> {
                if (missMap.containsKey(o.getAppId())) {
                    List<OrderInfoDO> orderInfoDOList = missMap.get(o.getAppId());
                    OrderInfoDO info = orderInfoDOList.get(0);
                    o.setCustomerUin(String.valueOf(info.getCustomerUin()));
                    o.setCustomerShortName(info.getCustomerShortName());
                    o.setCustomerName(info.getCustomerName());
                    o.setIndustryDept(info.getIndustryDept());
                }
            });
        }
        log.info("appId缺失列表: " + missSet);
        DBList.ckcldStdCrpDBHelper.insert(result);
    }

    @Override
    public List<QuotaMonitorResp> queryQuotaMonitor(QuotaMonitorReq req) {
        WhereContent whereContent = new WhereContent();
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getStatTime, req.getStatTime());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getDemandYearMonth, req.getDemandYearMonth());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getCustomerUin, req.getCustomerUin());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getIndustryDept, req.getIndustryDept());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getAppId, req.getAppId());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getCustomerShortName, req.getCustomerShortName());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getRegionName, req.getRegionName());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getZoneName, req.getZoneName());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getCustomhouseTitle, req.getCustomhouseTitle());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getPayMode, req.getBillType());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getQuotaKey, req.getQuotaType());
        whereContent.andInIfValueNotEmpty(AdsOrderQuotaDfDO::getInstanceType, req.getInstanceType());
        if (req.getIsPurchaseInstanceType() != null) {
            whereContent.andEqual(AdsOrderQuotaDfDO::getIsPurchaseInstanceType, req.getIsPurchaseInstanceType());
        }
        if (ListUtils.isNotEmpty(req.getQuotaManageType()) && req.getQuotaManageType().size() == 1) {
            whereContent.andIn(AdsOrderQuotaDfDO::getQuotaUnit,
                    req.getQuotaManageType().get(0).equals("核心管控") ? "VCPU" : "AMOUNT");
        }
        List<QuotaMonitorResp> result = DBList.ckcldStdCrpDBHelper.getAll(QuotaMonitorResp.class, whereContent.getSql(),
                whereContent.getParams());
        result.forEach(o -> {
            o.setCrpSuggestTotalQuotaNum(o.getYunxiaoMonthBeginningQuotaNum() + o.getCrpSuggestQuotaNum());
            o.setYunxiaoCurrentLeave(o.getYunxiaoCurrentQuotaNum() - o.getQuotaUsage());
            o.setActualGap(o.getCrpSuggestTotalQuotaNum() - o.getYunxiaoCurrentQuotaNum());
            o.setCoreToCrpSuggestTotalQuotaNum(
                    o.getCoreToYunxiaoMonthBeginningQuotaNum() + o.getCoreToCrpSuggestQuotaNum());
            o.setCoreToYunxiaoCurrentLeave(o.getCoreToYunxiaoCurrentQuotaNum() - o.getCoreToQuotaUsage());
            o.setCoreToActualGap(o.getCoreToCrpSuggestTotalQuotaNum() - o.getCoreToYunxiaoCurrentQuotaNum());
        });
        return result;
    }

    @Override
    public DownloadBean exportQuotaMonitor(String fileNamePrefix, QuotaMonitorReq req) {
        List<QuotaMonitorResp> result = new ArrayList<>();
        if (req != null) {
            result = queryQuotaMonitor(req);
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        InputStream template = IOUtils.readClasspathResourceInputStream("excel/order/quota_monitor_export.xlsx");
        ExcelWriter excelWriter = EasyExcel.write(out).withTemplate(template)
                .registerConverter(new LocalDateStringConverter("yyyy-MM-dd"))
                .registerConverter(new LocalTimeStringConverter("HH:mm:ss"))
                .build();
        WriteSheet writeSheet = EasyExcel.writerSheet().build();
        excelWriter.fill(new FillWrapper("monitor", result), writeSheet).finish();
        String fileName = fileNamePrefix + DateUtils.format(new Date(), "-yyyyMMdd-HHmmss") + ".xlsx";
        return new DownloadBean(fileName, out.toByteArray());
    }


    public void importDataToQuota(ReadResult<OrderQuotaExcelDTO> result) {
        List<OrderQuotaExcelDTO> data = result.getData();
        Map<String, List<OrderQuotaExcelDTO>> importMap = data.stream()
                .collect(Collectors.groupingBy(v -> String.join("@", v.getDemandYearMonth(), v.getCustomerUin(),
                        v.getZoneName(), v.getBillType(), v.getInstanceType(),
                        v.getQuotaType())));

        List<OrderQuotaDO> existData = demandDBHelper.getAll(OrderQuotaDO.class);
        Map<String, List<OrderQuotaDO>> existMap = existData.stream()
                .collect(Collectors.groupingBy(v -> String.join("@", v.getDemandYearMonth(), v.getCustomerUin(),
                        v.getZoneName(), v.getBillType(), v.getInstanceType(),
                        v.getQuotaType())));

        List<OrderQuotaDO> updateList = new ArrayList<>();
        List<OrderQuotaDO> insertList = new ArrayList<>();
        importMap.forEach((k, v) -> {
            if (v.size() > 1) {
                throw new BizException("导入的数据存在相同维度的数据： " + k
                        + ";  请删除重复数据后重试。如重试后还未解决，联系 oliverychen。");
            }
            OrderQuotaExcelDTO orderQuotaExcelDTO = v.get(0);
            Boolean isSmallCore = orderQuotaExcelDTO.getInstanceType().equals(orderQuotaExcelDTO.getQuotaType());
            if (existMap.get(k) != null) {
                // 之前存在则更新
                OrderQuotaDO orderQuotaDO = existMap.get(k).get(0);
                orderQuotaDO.setInstanceNumQuota(isSmallCore ? 0 : orderQuotaExcelDTO.getQuotaNum());
                orderQuotaDO.setCoreQuota(isSmallCore ? orderQuotaExcelDTO.getQuotaNum()
                        : orderQuotaExcelDTO.getQuotaNum() * orderQuotaExcelDTO.getSingleInstanceModelCpuNum());
                updateList.add(orderQuotaDO);
            } else {
                // 之前不存在则新增
                OrderQuotaDO orderQuotaDO = new OrderQuotaDO();
                BeanUtils.copyProperties(orderQuotaExcelDTO, orderQuotaDO);
                String[] parts = orderQuotaDO.getDemandYearMonth().split("年|月");
                orderQuotaDO.setDemandYear(Integer.parseInt(parts[0]));
                orderQuotaDO.setDemandMonth(Integer.parseInt(parts[1]));
                orderQuotaDO.setSource("COMD");
                orderQuotaDO.setStockSatisfyCore(0);
                orderQuotaDO.setBuySatisfyCore(0);
                orderQuotaDO.setTotalCore(0);
                orderQuotaDO.setInstanceNum(0);
                orderQuotaDO.setInstanceNumQuota(isSmallCore ? 0 : orderQuotaExcelDTO.getQuotaNum());
                orderQuotaDO.setCoreQuota(isSmallCore ? orderQuotaExcelDTO.getQuotaNum()
                        : orderQuotaExcelDTO.getQuotaNum() * orderQuotaExcelDTO.getSingleInstanceModelCpuNum());
                orderQuotaDO.setQuotaManageType(orderQuotaDO.getQuotaType().contains(".") ? "规格管控" : "核心管控");
                orderQuotaDO.setDeliverQuotaNum(0);
                insertList.add(orderQuotaDO);
            }
        });
        demandDBHelper.update(updateList);
        demandDBHelper.insertBatchWithoutReturnId(insertList);

    }
}
