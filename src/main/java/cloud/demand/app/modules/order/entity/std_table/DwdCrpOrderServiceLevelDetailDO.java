package cloud.demand.app.modules.order.entity.std_table;


import cloud.demand.app.modules.p2p.ppl13week.service.filler.CustomerInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.dbhelper.annotation.Table;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@Table("dwd_crp_order_service_level_detail")
public class DwdCrpOrderServiceLevelDetailDO implements CustomerInfoFiller {

    /** 统计日期<br/>Column: [stat_date] */
    @Column(value = "stat_date")
    private LocalDate statDate;

    /** 产品 */
    @Column(value = "product")
    private String product;

    /** 计费类型 */
    @Column(value = "bill_type")
    private String billType;

    /** 客户简称 */
    @Column(value = "customer_uin")
    private String customerUin;

    @Column("order_label")
    private String orderLabel;

    @Column("order_node_code")
    private String orderNodeCode;

    /**
     * uin类型，0内部 1外部
     */
    @Column(value = "uin_type")
    private Integer uinType;

    /** 是否主力机型，true表示是主力机型，fasle表示非主力机型 */
    @Column(value = "main_instance_type")
    private Boolean mainInstanceType;

    /** 是否主力园区，true表示是主力园区，false表示非主力园区 */
    @Column(value = "main_zone")
    private Boolean mainZone;

    /** 提前多少周<br/>Column: [advance_week] */
    @Column(value = "advance_week")
    private Integer advanceWeek;

    /** 预测提前期范围描述<br/>Column: [advance_week_range_remark] */
    @Column(value = "advance_week_range_remark")
    private String advanceWeekRangeRemark;

    /** 通用客户简称<br/>Column: [common_customer_short_name] */
    @Column(value = "common_customer_short_name")
    private String commonCustomerShortName;

    /** 共识需求核心数（带提前期范围维度的）<br/>Column: [consensus_core] */
    @Column(value = "consensus_core")
    private BigDecimal consensusCore;

    /** 客户简称<br/>Column: [customer_short_name] */
    @Column(value = "customer_short_name")
    private String customerShortName;

    /** 境内外<br/>Column: [customhouse_title] */
    @Column(value = "customhouse_title")
    private String customhouseTitle;

    /** 弹性类型<br/>Column: [elastic_type] */
    @Column(value = "elastic_type")
    private String elasticType;

    /** 行业部门<br/>Column: [industry_dept] */
    @Column(value = "industry_dept")
    private String industryDept;

    /** 实例类型<br/>Column: [instance_type] */
    @Column(value = "instance_type")
    private String instanceType;

    /** 满足核心数（相同维度下的多个提前期范围的满足核心数是同一个值）<br/>Column: [match_core] */
    @Column(value = "match_core")
    private BigDecimal matchCore;

    /** 满足核心数（不带带提前期范围维度的）<br/>Column: [match_core_no_tage_range] */
    @Column(value = "match_core_no_tage_range")
    private BigDecimal matchCoreNoTageRange;

    /** 是否为新机型，true表示是新机型，false表示旧机型<br/>Column: [new_instance_type] */
    @Column(value = "new_instance_type")
    private Boolean newInstanceType;

    /** 订单号<br/>Column: [order_number] */
    @Column(value = "order_number")
    private String orderNumber;

    /** 订单类型<br/>Column: [order_type] */
    @Column(value = "order_type")
    private String orderType;

    /** 提前期信息<br/>Column: [real_tag_result] */
    @Column(value = "real_tag_result")
    private String realTagResult;

    /** 地域名称<br/>Column: [region_name] */
    @Column(value = "region_name")
    private String regionName;

    /** 服务水平 XX%<br/>Column: [service_level] */
    @Column(value = "service_level")
    private String serviceLevel;

    /** 行业战区<br/>Column: [war_zone] */
    @Column(value = "war_zone")
    private String warZone;

    /** 需求年月<br/>Column: [year_month] */
    @Column(value = "year_month")
    private String yearMonth;

    /** 可用区名称<br/>Column: [zone_name] */
    @Column(value = "zone_name")
    private String zoneName;

    @Override
    public String provideCustomerUin() {
        return this.customerUin;
    }

    @Override
    public void fillCId(String cId) {

    }

    @Override
    public void fillCName(String cName) {

    }

    @Override
    public void fillPanShiWarZone(String panShiWarZone) {

    }

    @Override
    public void fillUinType(Integer uinType) {
        this.uinType = uinType;
    }

    @Override
    public void fillCustomerSocietyType(Integer customerSocietyType) {

    }
}
