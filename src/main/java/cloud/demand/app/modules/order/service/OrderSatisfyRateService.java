package cloud.demand.app.modules.order.service;

import cloud.demand.app.modules.order.dto.req.BackupServiceLevelDetailReq;
import cloud.demand.app.modules.order.dto.req.ConsensusReq;
import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import cloud.demand.app.modules.order.dto.req.SupplyPlanDetailMatchCoreUpdateReq;
import cloud.demand.app.modules.order.dto.resp.OrderSupplySatisfyModifiedRecordResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteBuyDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteServiceLevelOverviewResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteSoldOutDTO;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderServiceLevelDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.DemandDTO;
import cloud.demand.app.modules.order.dto.resp.supply.satisfaction.StockDTO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailSatisfyDO;
import cloud.demand.app.modules.order.entity.OrderSupplyPlanDetailSatisfyVersionDO;
import java.time.LocalDate;
import java.util.List;

public interface OrderSatisfyRateService {

    /**
     *  获取库存数据，用于订单满足度的计算 <br/>
     *  获取逻辑可见<a href ="https://iwiki.woa.com/p/4012087655">订单满足度计算</a> 中的库存数据获取部分
     */
    List<StockDTO> queryStockForOrderSatisfaction(LocalDate statDate, Integer statDays);

    /**
     *  获取需求、履约、预扣数据，用于订单满足度的计算 <br/>
     *  订单范围：新增订单、一次性弹性订单  <br/>
     *  获取逻辑可见<a href ="https://iwiki.woa.com/p/4012087655">订单满足度计算</a> 中的库存数据获取部分
     */
    List<DemandDTO> queryDemandForOrderSatisfaction(LocalDate statDate, Integer statDays);

    /**
     *  补偿订单满足度计算，针对应该有满足度信息实际上却没有的订单，获取其开始购买日期，对这天进行满足度信息重算
     */
    void compensateOrderSatisfactionCalc();

    /**
     *  大盘满足的模拟对冲的满足度计算，以共识需求维度 <br/>
     *  获取逻辑可见<a href ="https://iwiki.woa.com/p/4012087655">订单满足度计算</a> 中的大盘满足部分<br/>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     * @param statDate 统计日期，定时任务执行当前的就行，若是需要运维手动执行，则传入需要重新生成数据的计算日期
     * @param statDays 统计天数，当前都默认为统计日期 14天内的需求进行统计
     */
    void orderSatisfactionCalc(LocalDate statDate, Integer statDays);


    /**
     *  大盘满足的累计预扣满足量计算，共识需求维度 <br/>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     * @param statDate 统计日期，定时任务执行当前的就行，若是需要运维手动执行，则传入需要重新生成数据的计算日期
     */
    void orderCumulativeDeductCal(LocalDate statDate);

    /**
     *  GPU订单大盘满足、搬迁满足、采购方式的满足量计算，共识需求维度 <br/>
     * @param minBeginBuyDate 订单最小开始购买日期，默认当前日期
     */
    void calcGpuMatchRate(LocalDate minBeginBuyDate);

    /**
     *  GPU订单大盘满足方式的满足量计算，共识需求维度. <br/>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     */
    void calcGpuSatisfyOneOrder(String orderNumber);

    /**
     * 运维解决2月21号满足度错误数据
     */
    void opsOrderForError2_21(String orderNumber);

    /**
     * 计算出供应方案明细的满足度信息
     */
    void calcSupplyPlanDetailMatchCore(String orderNumber);

    /**
     *  运维计算应当有满足度数据却没有非大盘满足满足度信息的订单，生成非大盘满足的供应方案明细的满足度信息。<br/>
     *  当 orderNumber 非空时，只计算指定的 orderNumber
     */
    void opsCalcForNotSatisfyMatch(String orderNumber);

    /**
     *  计算采购、搬迁供应方案的满足度信息 </br>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     * @param includeCloseOrder 是否包含关闭订单的订单，默认不包含，刷新全量历史数据时使用
     */
    void refreshSatisfyForNotSatisfyMatch(boolean includeCloseOrder);

    /**
     *  计算采购、搬迁供应方案的满足度信息<br/>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     */
    void refreshSatisfyForNotSatisfyMatchOneOrder(String orderNumber);



    /**
     *  非大盘满足的供应方案计算生成方案明细的满足度信息</br>
     *  日弹性、周弹性、月弹性订单使用{@link #calcElasticOrderSatisfyOneOrder(String)}计算 </br>
     * @param details 非大盘满足的供应方案明细
     * @param satisfyVersion 方案明细满足度版本信息，为null时会自动创建
     */
    void calcSupplyPlanDetailMatchCoreForNotSatisfyMatch(List<? extends OrderSupplyPlanDetailDO> details,
            OrderSupplyPlanDetailSatisfyVersionDO satisfyVersion);

    /**
     *  采购满足、搬迁满足的交付共识，当实际交付日期 > 开始购买日期时，行业根据需要进行接受或拒绝，影响对应供应方案的满足度
     */
    void deliverConsensus(ConsensusReq req);

    List<OrderSupplyPlanDetailSatisfyDO> queryAvailableSupplyDetailSatisfyList(List<Long> supplyDetailIds);

    /**
     *  修改供应方案明细的满足度信息
     */
    void modifySupplyPlanDetailMatchCore(SupplyPlanDetailMatchCoreUpdateReq req);

    /**
     *  查询供应方案明细的满足度修改记录
     */
    OrderSupplySatisfyModifiedRecordResp querySatisfyModifiedRecord(Long supplyPlanDetailId);

    /**
     *  根据供应方案明细的满足度信息，向上汇聚计算出不同满足方式下的订单明细满足度信息
     */
    void supplyDetailSatisfyToOrderItemSatisfy(String orderNumber);

    /**
     * 通过订单明细满足度信息获取共识需求满足量信息
     */
    void updateOrderConsensusByNumChange(String orderNumber);

    /**
     *  服务水平看板-官网服务水平分析
     */
    OfficialWebsiteServiceLevelOverviewResp officialWebsiteServiceLevelOverview(ServiceLevelQueryReq req);

    /**
     *  服务水平看板-官网服务水平分析-购买成功数据
     */
    List<OfficialWebsiteBuyDTO> officialWebsiteBuyData(ServiceLevelQueryReq req);

    /**
     *  服务水平看板-官网服务水平分析-售罄数据
     */
    List<OfficialWebsiteSoldOutDTO> officialWebsiteSoldOutData(ServiceLevelQueryReq req);

    /**
     *  订单服务水平-客户、可用区、机型维度的信息。<br/>
     *  只查询登陆用户数据权限范围内的数据。<br/>
     *  管理员可以看所有数据。<br/>
     *  行业数据关注人可以查询配置权限范围内的行业、产品、战区、客户<br/>
     */
    List<OrderServiceLevelDTO> orderServiceLevelDetailWithAuth(ServiceLevelQueryReq req);

    /**
     *  订单服务水平-客户、可用区、机型维度的信息。不考虑数据权限
     */
    List<OrderServiceLevelDTO> orderServiceLevelDetail(ServiceLevelQueryReq req);

    /**
     *  订单服务水平概览。会检查数据权限。
     */
    OrderServiceLeveResp orderServiceLevelOverview(ServiceLevelQueryReq req);

    /**
     *  备份服务水平数据到CK中
     */
    void backupServiceLevelDetail(BackupServiceLevelDetailReq req);

    /**
     *  订单服务水平-年月产品维度报表（简单）
     */
    void syncSimpleOrderServiceLevelSummary();

    /**
     *  订单满足缺口预警-行业级推送 <br/>
     *  推送人：各个行业接口人 <br/>
     *  条件：行业缺口订单>1 && 满足阈值缺口比例>=20% <br/>
     */
    void industrySatisfyGapAlert();

    /**
     *  订单满足缺口预警-全局推送 <br/>
     *  推送人：云运管接口人/配置固定人员 <br/>
     *  条件：所有行业缺口订单>1 && 满足阈值缺口比例>=20% <br/>
     */
    void globalSatisfyGapAlert();

    /**
     *  订单满足度数据根据可用区名称来矫正地域信息
     */
    void correctRegionByZoneNameForOrderItemSatisfyRate();

    /**
     *  计算日弹性、周弹性、月弹性订单的满足度信息。<br/>
     *  订单范围： consensus_begin_buy_date <= {maxDate} and consensus_end_buy_date >= {minDate} <br/>
     *          order_type = 'ELASTIC' and elastic_type in ('日弹性', '周弹性', '月弹性')  <br/>
     *          [minDate,maxDate] 与 [consensus_begin_buy_date, consensus_end_buy_date] 有交集的订单才计算 <br/>
     * @param minDate 最早日期，minDate <= maxDate 才会计算， minDate最小为2025-05-01
     * @param maxDate 最晚日期，minDate <= maxDate 才会计算
     */
    void calcElasticOrderSatisfy(LocalDate minDate, LocalDate maxDate);

    /**
     * 计算日弹性、周弹性、月弹性订单的满足度信息。<br/>
     * @param orderNumbers 需要计算的订单号
     */
    void calcElasticOrderSatisfyForOrderNumbers(List<String> orderNumbers);

    /**
     *  计算日弹性、周弹性、月弹性订单的满足度信息。
     * <a href="https://iwiki.woa.com/p/4013891009#%E8%A7%84%E5%88%99%E6%A6%82%E8%A7%88">规则概览</a>
     */
    void calcElasticOrderSatisfyOneOrder(String orderNumber);

    /**
     * 计算周期内弹性订单的月度满足度
     */
    void calcCycleElasticOrderMonthSatisfyForOrderNumbers(List<String> orderNumbers);

    /**
     * 计算周期内弹性订单的月度满足度
     */
    void calcCycleElasticOrderMonthSatisfy(String orderNumber);

}
