package cloud.demand.app.modules.order.dto.req;

import cloud.demand.app.modules.order.dto.resp.service_level.OfficialWebsiteSoldOutDTO.OfficialWebsiteSoldOutDTODetail;
import cloud.demand.app.modules.order.dto.resp.service_level.OrderServiceLeveResp.OrderItemMatchCore;
import cloud.demand.app.modules.order.enums.OrderElasticType;
import cloud.demand.app.modules.order.enums.OrderNodeCodeEnum;
import cloud.demand.app.modules.order.enums.OrderTypeEnum;
import cloud.demand.app.modules.order_report.parse.UinTypeWhereParse;
import cloud.demand.app.modules.p2p.ppl13week.enums.Ppl13weekProductTypeEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoAppRoleEnum;
import cloud.demand.app.modules.p2p.ppl13week.enums.yunxiao.YunxiaoPayModeEnum;
import cn.hutool.core.map.MapBuilder;
import com.pugwoo.dbhelper.sql.WhereSQL;
import com.pugwoo.wooutils.collect.ListUtils;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.Data;
import org.nutz.lang.Strings;
import yunti.boot.exception.BizException;

/** 服务水平看板查询条件 */
@Data
public class ServiceLevelQueryReq {

    /** 产品：CVM&CBS  弹性MapReduce  EKS官网 , 对官网服务水平不生效 */
    private String product;

    private YearMonth beginMonth;

    private YearMonth endMonth;

    /** 计费模式名称，对官网服务水平不生效 */
    private List<String> billType;

    private List<String> industryDeptList;

    /** 剔除的行业部门 */
    private List<String> rejectIndustryDeptList;

    /** 行业战区，对官网售罄数据不生效 */
    private List<String> warZones;

    /** 客户简称，对官网售罄数据不生效 */
    private List<String> customerShortNames;

    /** 通用客户简称，对官网售罄数据不生效 */
    private List<String> commonCustomerShortNames;

    /** 境内/境外 */
    private String customhouseTitle;

    /** 短的地域名称 */
    private List<String> regionNames;

    private List<String> instanceTypes;

    /** 是否为新机型，true表示是新机型，false表示旧机型 */
    private Boolean newOlderInstanceType;

    /** 是否主力园区，true表示是主力园区，false表示非主力园区 */
    private Boolean mainZone;

    /** 是否主力机型，true表示是主力机型，false表示非主力机型 */
    private Boolean mainInstanceType;

    /** 订单标签
     * @see cloud.demand.app.modules.order.enums.OrderLabelEnum
     * */
    private List<String> orderLabel;

    /** 客户类型：0 - 内部客户，1 - 外部客户 */
    private List<Integer> uinType;

    /** 订单状态：默认为 交付供应、履约跟踪、订单关闭 */
    private List<String> orderNodeCodeList = ListUtils.newArrayList(
            OrderNodeCodeEnum.node_order_supply.getCode(),
            OrderNodeCodeEnum.node_order_following.getCode(),
            OrderNodeCodeEnum.node_order_close.getCode());

    /** 订单类型 {@link OrderTypeEnum#getCode()} */
    private List<String> orderTypeList;

    /** 弹性类型 {@link OrderElasticType#getTypeName()} */
    private List<String> elasticTypeList;

    /** 使用CK的服务水平明细宽表数据，默认false表示不使用 */
    private Boolean useCkData = false;

    private YearMonth ckMaxYearMonth;

    /** 控制预测提前期分段范围的参数，可以不传有与原型一致的默认值 */
    private Map<Integer, String> advanceWeekRangeMap = new MapBuilder<Integer, String>(new HashMap<>())
            .put(13, "提前13周").put(9, "9～12周").put(6, "6～8周").put(0, "5周内").build();

    public static final List<String> PRODUCT_LIST = ListUtils.newArrayList(Ppl13weekProductTypeEnum.CVM.getName(),
            Ppl13weekProductTypeEnum.EMR.getName(), Ppl13weekProductTypeEnum.EKS.getName());



    public void paramsCheck() {
        if (beginMonth == null || endMonth == null) {
            throw new BizException("查询月份不能为空");
        }
        if (product != null) {
            // CVM&CBS    弹性MapReduce    EKS官网
            if (!PRODUCT_LIST.contains(product)) {
                throw new BizException("产品类型不合法");
            }
        }
        if (ListUtils.isNotEmpty(billType)) {
            for (String s : billType) {
                if (!YunxiaoPayModeEnum.nameList().contains(s)) {
                    throw new BizException("计费模式不合法");
                }
            }
        }
        if (customhouseTitle != null) {
            // 境内    境外
            if (!ListUtils.newArrayList("境内", "境外").contains(customhouseTitle)) {
                throw new BizException("境内/境外不合法");
            }
        }
        // 仅允许查询24年及以后的数据
        if (beginMonth.getYear() < 2024) {
            throw new BizException("仅允许查询2024年及以后的数据");
        }
        // 最多允许查询6个月的数据
        long months = beginMonth.until(endMonth, ChronoUnit.MONTHS);
        /*if (months > 5 || months < -5) {
            throw new BizException("最多允许查询6个月的数据");
        }*/
    }

    public WhereSQL toWhereForOfficialWebsiteBuyData() {
        WhereSQL where = new WhereSQL();
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        // 取时间范围内的所有切片
        where.and(" stat_time >= ? and stat_time <= ? ", begin, end);
        where.and(" buy_date >= ? and buy_date <= ? ", begin, end);
        if (ListUtils.isNotEmpty(industryDeptList)) {
            where.and(" industry_dept in (?)", industryDeptList);
        }
        if (ListUtils.isNotEmpty(regionNames)) {
            where.and(" region_name in (?)", regionNames);
        }
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" gins_family in (?)", instanceTypes);
        }
        if (Strings.isNotBlank(customhouseTitle)) {
            where.and(" customhouse_title = ? ", customhouseTitle);
        }
        if (ListUtils.isNotEmpty(customerShortNames)) {
            where.and(" customer_short_name in (?)", customerShortNames);
        }
        if (ListUtils.isNotEmpty(warZones)) {
            where.and(" war_zone_name in (?)", warZones);
        }

        return where;
    }

    public WhereSQL toWhereForOfficialWebsiteSoldOutData() {
        WhereSQL where = new WhereSQL();
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" instance_family in (?)", instanceTypes);
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        where.and(" version >= ? and version <= ? ", begin.format(formatter), end.format(formatter));
        return where;
    }

    public List<OfficialWebsiteSoldOutDTODetail> filterForOfficialWebsiteSoldOutData(
            List<OfficialWebsiteSoldOutDTODetail> input) {
        List<OfficialWebsiteSoldOutDTODetail> res = new ArrayList<>();
        if (ListUtils.isEmpty(input)) {
            return res;
        }
        boolean filterRegionName = ListUtils.isNotEmpty(regionNames);
        boolean filterCustomhouseTitle = customhouseTitle != null;
        boolean filterNewOlderInstanceType = newOlderInstanceType != null;
        boolean filterMainZone = mainZone != null;
        boolean filterMainInstanceType = mainInstanceType != null;
        for (OfficialWebsiteSoldOutDTODetail item : input) {
            if (filterCustomhouseTitle) {
                if (!Objects.equals(customhouseTitle, item.getCustomhouseTitle())) {
                    continue;
                }
            }
            if (filterNewOlderInstanceType) {
                if (!Objects.equals(newOlderInstanceType, item.getNewInstanceType())) {
                    continue;
                }
            }
            if (filterRegionName) {
                if (!regionNames.contains(item.getRegionName())) {
                    continue;
                }
            }
            if (filterMainZone) {
                if (!Objects.equals(mainZone, item.getMainZone())) {
                    continue;
                }
            }
            if (filterMainInstanceType) {
                if (!Objects.equals(mainInstanceType, item.getMainInstanceType())) {
                    continue;
                }
            }
            res.add(item);
        }
        return res;
    }

    public WhereSQL toWhereForOrderServiceLevelDetail(boolean separateNewOldData, boolean userOldData,
            boolean onlyCycleElasticOrder, String beginBuyDateConditionField) {
        WhereSQL where = new WhereSQL();
        if (ListUtils.isNotEmpty(industryDeptList)) {
            where.and(" a.industry_dept in (?)", industryDeptList);
        }
        if (ListUtils.isNotEmpty(rejectIndustryDeptList)) {
            where.and(" a.industry_dept not in (?)", rejectIndustryDeptList);
        }
//        // 不使用订单中的长的地域名称来查询
//        if (ListUtils.isNotEmpty(regionNames)) {
//            where.and(" b.region_name in (?)", regionNames);
//        }
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" b.instance_type in (?)", instanceTypes);
        }
        if (ListUtils.isNotEmpty(customerShortNames)) {
            where.and(" a.customer_short_name in (?)", customerShortNames);
        }
        if (ListUtils.isNotEmpty(warZones)) {
            where.and(" a.war_zone in (?)", warZones);
        }
        if (ListUtils.isNotEmpty(billType)) {
            List<String> billTypeCodes = new ArrayList<>();
            for (String s : billType) {
                YunxiaoPayModeEnum payMode = YunxiaoPayModeEnum.getByName(s);
                if (payMode == null) {
                    throw new BizException("计费模式不合法");
                }
                billTypeCodes.add(payMode.getCode());
            }
            where.and(" b.bill_type in (?) ", billTypeCodes);
        }
        if (product != null) {
            if (Ppl13weekProductTypeEnum.CVM.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.CVM.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.CVM.getName());
            } else if (Ppl13weekProductTypeEnum.EMR.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.EMR.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.EMR.getName());
            } else if (Ppl13weekProductTypeEnum.EKS.getName().equals(product)) {
                where.and(" a.app_role = ? ", YunxiaoAppRoleEnum.EKS.getCode());
                where.and(" a.product = ? ", Ppl13weekProductTypeEnum.EKS.getName());
            }
        }
        LocalDate begin = beginMonth.atDay(1);
        LocalDate end = endMonth.atEndOfMonth();
        if (ckMaxYearMonth != null && useCkData) {
            if (!ckMaxYearMonth.isBefore(beginMonth)) {
                if (endMonth.isAfter(ckMaxYearMonth)) {
                    // 不在ck查询范围内的那一部分，使用业务数据查询
                    YearMonth trueBeginMonth = ckMaxYearMonth.plusMonths(1);
                    begin = trueBeginMonth.atDay(1);
                } else {
                    // 查询范围在 ck的数据范围内， 不用业务数据查询
                    return null;
                }
            }
        }
        if (separateNewOldData) {
            // 新旧数据区分开来查询
            if (userOldData) {
                YearMonth split = YearMonth.of(2025, 1);
                if (endMonth.isAfter(split)) {
                    end = split.atEndOfMonth();
                }
                if (begin.isAfter(end)) {
                    return null;
                }
                // 旧，25年1月份及1月之前的数据
//                where.and(beginBuyDateTableAlias + " .begin_buy_date >= ? and "
//                        + beginBuyDateTableAlias + ".begin_buy_date <= ? ",
//                        begin, end);
                where.and(beginBuyDateConditionField + "  >= ? and "
                                + beginBuyDateConditionField + " <= ? ",
                        begin, end);
            } else {
                YearMonth split = YearMonth.of(2025, 2);
                if (!beginMonth.isAfter(split)) {
                    begin = split.atDay(1);
                }
                if (begin.isAfter(end)) {
                    return null;
                }
                // 新，25年2月份及2月之后的数据
//                where.and(beginBuyDateTableAlias + " .begin_buy_date >= ? and "
//                        + beginBuyDateTableAlias + ".begin_buy_date <= ? ",
//                        begin, end);
                where.and(beginBuyDateConditionField + " >= ? and "
                                + beginBuyDateConditionField + " <= ? ",
                        begin, end);
            }
        } else {
            // 不区分新旧数据
//            where.and(beginBuyDateTableAlias + " .begin_buy_date >= ? and "
//                    + beginBuyDateTableAlias + ".begin_buy_date <= ? ",
//                    begin, end);
            where.and(beginBuyDateConditionField + "  >= ? and "
                            + beginBuyDateConditionField + " <= ? ",
                    begin, end);
        }
        // 必须要 到达开始购买时间
        where.and(" a.begin_buy_date <= ? ", LocalDate.now());
        where.and(" a.deleted = 0 and b.deleted = 0 and a.available_status = 'available' ");
        where.and(" a.order_status in ('PROCESS', 'FINISHED') ");
        if (ListUtils.isEmpty(orderNodeCodeList)) {
            // 默认： 交付供应、履约跟踪、订单关闭
            where.and(" a.order_node_code in ('node_order_supply', 'node_order_following', 'node_order_close') ");
        } else {
            where.and(" a.order_node_code in (?) ", orderNodeCodeList);
        }

        if (ListUtils.isNotEmpty(orderTypeList)) {
            where.and(" a.order_type in (?) ", orderTypeList);
        }
        if (ListUtils.isNotEmpty(elasticTypeList)) {
            // 弹性类型 可以 和 订单类型 一起选择
            WhereSQL elasticWhere = new WhereSQL();
            elasticWhere.and(" a.order_type = ? and a.elastic_type in (?) ",
                    OrderTypeEnum.ELASTIC.getCode(), elasticTypeList);
            if (orderTypeList.contains(OrderTypeEnum.NEW.getCode())) {
                elasticWhere.or(" a.order_type = ? ", OrderTypeEnum.NEW.getCode());
            }
            where.and(elasticWhere);
        }
        if (onlyCycleElasticOrder) {
            where.and(" a.order_type = ? ", OrderTypeEnum.ELASTIC.getCode());
            where.and(" a.elastic_type in (?) ", OrderElasticType.cycleElasticTypes);
        }

        // 订单标签过滤
        if (ListUtils.isNotEmpty(orderLabel)){
            where.and("a.order_label in (?)", orderLabel);
        }
        // 客户类型过滤，0 - 内部客户，1 - 外部客户
        if (ListUtils.isNotEmpty(uinType)){
            WhereSQL whereSQL = UinTypeWhereParse.getWhereSQL(uinType, "a.customer_uin");
            if (whereSQL != null){
                where.and(whereSQL);
            }
        }
        // 报表位数过滤，表示该订单是否需要被服务水平统计
//        where.and("a.report_bit_num & 2 != 0");

        return where;
    }

    public WhereSQL toCKWhereForOrderServiceLevelDetail(LocalDate statDate) {
        if (this.ckMaxYearMonth == null || this.ckMaxYearMonth.isBefore(this.beginMonth)) {
            return null;
        }
        WhereSQL where = new WhereSQL();
        where.and(" stat_date = ? ", statDate);
        YearMonth begin = beginMonth;
        YearMonth end = ckMaxYearMonth.isAfter(this.endMonth) ? this.endMonth : ckMaxYearMonth;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        where.and(" year_month >= ? and year_month <= ? ", formatter.format(begin), formatter.format(end));

        if (ListUtils.isNotEmpty(industryDeptList)) {
            where.and(" industry_dept in (?)", industryDeptList);
        }
        if (ListUtils.isNotEmpty(rejectIndustryDeptList)) {
            where.and(" industry_dept not in (?)", rejectIndustryDeptList);
        }
        if (ListUtils.isNotEmpty(regionNames)) {
            where.and(" region_name in (?)", regionNames);
        }
        if (ListUtils.isNotEmpty(instanceTypes)) {
            where.and(" instance_type in (?)", instanceTypes);
        }
        if (ListUtils.isNotEmpty(customerShortNames)) {
            where.and(" customer_short_name in (?)", customerShortNames);
        }
        if (ListUtils.isNotEmpty(warZones)) {
            where.and(" war_zone in (?)", warZones);
        }
        if (ListUtils.isNotEmpty(billType)) {
            List<String> billTypeCodes = new ArrayList<>();
            for (String s : billType) {
                YunxiaoPayModeEnum payMode = YunxiaoPayModeEnum.getByName(s);
                if (payMode == null) {
                    throw new BizException("计费模式不合法");
                }
                billTypeCodes.add(payMode.getCode());
            }
            where.and(" bill_type in (?) ", billTypeCodes);
        }
        if (product != null) {
            where.and(" product = ? ", product);
        }

        if (ListUtils.isNotEmpty(orderTypeList)) {
            where.and(" order_type in (?) ", orderTypeList);
        }
        if (ListUtils.isNotEmpty(elasticTypeList)) {
            // 弹性类型 可以 和 订单类型 一起选择
            WhereSQL elasticWhere = new WhereSQL();
            elasticWhere.and(" order_type = ? and elastic_type in (?) ",
                    OrderTypeEnum.ELASTIC.getCode(), elasticTypeList);
            if (orderTypeList.contains(OrderTypeEnum.NEW.getCode())) {
                elasticWhere.or(" order_type = ? ", OrderTypeEnum.NEW.getCode());
            }
            where.and(elasticWhere);
        }

        // 客户类型过滤，0 - 内部客户，1 - 外部客户
        if (ListUtils.isNotEmpty(uinType)){
            where.and(" uin_type in (?) ", uinType);
        }
        if (customhouseTitle != null) {
            where.and(" customhouse_title = ? ", customhouseTitle);
        }
        if (mainZone != null) {
            where.and(" main_zone = ? ", mainZone);
        }
        if (mainInstanceType != null) {
            where.and(" main_instance_type = ? ", mainInstanceType);
        }
        if (newOlderInstanceType != null) {
            where.and(" new_instance_type = ? ", newOlderInstanceType);
        }
        if (ListUtils.isNotEmpty(commonCustomerShortNames)) {
            where.and(" common_customer_short_name in (?)", commonCustomerShortNames);
        }
        if (ListUtils.isNotEmpty(orderLabel)) {
            where.and(" order_label in (?)", orderLabel);
        }
        if (ListUtils.isNotEmpty(orderNodeCodeList)) {
            where.and(" order_node_code in (?)", orderNodeCodeList);
        }

        return where;
    }

    public List<OrderItemMatchCore> filterForOrderServiceLevelDetail(List<OrderItemMatchCore> input) {
        List<OrderItemMatchCore> res = new ArrayList<>();
        if (ListUtils.isEmpty(input)) {
            return res;
        }
        boolean filterRegionName = ListUtils.isNotEmpty(regionNames);
        boolean filterCustomhouseTitle = customhouseTitle != null;
        boolean filterNewOlderInstanceType = newOlderInstanceType != null;
        boolean filterMainZone = mainZone != null;
        boolean filterMainInstanceType = mainInstanceType != null;
        boolean filterCommonCustomerShortName = ListUtils.isNotEmpty(commonCustomerShortNames);
        for (OrderItemMatchCore item : input) {
            if (filterRegionName) {
                if (!regionNames.contains(item.getRegionName())) {
                    continue;
                }
            }
            if (filterCustomhouseTitle) {
                if (!Objects.equals(customhouseTitle, item.getCustomhouseTitle())) {
                    continue;
                }
            }
            if (filterNewOlderInstanceType) {
                if (!Objects.equals(newOlderInstanceType, item.getNewInstanceType())) {
                    continue;
                }
            }
            if (filterMainZone) {
                if (!Objects.equals(mainZone, item.getMainZone())) {
                    continue;
                }
            }
            if (filterMainInstanceType) {
                if (!Objects.equals(mainInstanceType, item.getMainInstanceType())) {
                    continue;
                }
            }
            if (filterCommonCustomerShortName) {
                if (!commonCustomerShortNames.contains(item.getCommonCustomerShortName())) {
                    continue;
                }
            }
            res.add(item);
        }
        return res;
    }

}
