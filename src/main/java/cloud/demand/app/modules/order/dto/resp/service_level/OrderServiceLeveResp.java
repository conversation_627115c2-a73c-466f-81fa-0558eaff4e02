package cloud.demand.app.modules.order.dto.resp.service_level;

import cloud.demand.app.modules.order.dto.OrderTagResultItemDto;
import cloud.demand.app.modules.order.dto.req.ServiceLevelQueryReq;
import cloud.demand.app.modules.order.entity.std_table.DwdCrpOrderServiceLevelDetailDO;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.CommonCustomerShortNameFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.IsNewInstanceTypeFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.MainInstanceTypeFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.MainZoneFiller;
import cloud.demand.app.modules.p2p.ppl13week.service.filler.ZoneInfoFiller;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.json.JSON;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.function.Function;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/** 订单服务水平明细 */
@Data
public class OrderServiceLeveResp {

    // 汇总：服务水平 = 满足核心数 / 共识需求核心数
    private BigDecimal serviceLevel;

    private CoreItem coreItem; // 汇总信息

    private List<CoreItem> rangeList;  // 提前期分段范围信息，已按提前期降序排序，例如 提前13周、9～12周、6～8周、5周内

    private Map<String, CoreItem> coreItemMap; // 前端忽略

    private List<OrderServiceLevelDTO> details; // 报表明细维度信息

    private ServiceLevelQueryReq queryReq;

    public static OrderServiceLeveResp create(List<OrderServiceLevelDTO> details, ServiceLevelQueryReq req) {
        OrderServiceLeveResp resp = new OrderServiceLeveResp();
        resp.details = details;
        resp.queryReq = req;
        resp.coreItem = new CoreItem();
        resp.coreItem.advanceWeekRangeRemark = "汇总";
        resp.coreItemMap = new HashMap<>();

        Map<String, Integer> rangeRemarkMap = new HashMap<>();
        req.getAdvanceWeekRangeMap().forEach((k, v) -> rangeRemarkMap.put(v, k));
        BigDecimal rangeSize = BigDecimal.valueOf(req.getAdvanceWeekRangeMap().size());

        for (OrderServiceLevelDTO detail : details) {
            // 满足核心数需要除以提前期分段数，因为相同维度下的多个提前期范围的满足核心数是同一个值
            BigDecimal avgMatchCore = detail.getMatchCore().divide(rangeSize, 4, RoundingMode.HALF_UP);
            resp.coreItem.totalMatchCore = resp.coreItem.totalMatchCore.add(avgMatchCore);
            resp.coreItem.totalConsensusCore = resp.coreItem.totalConsensusCore.add(detail.getConsensusCore());

            CoreItem coreItem = resp.coreItemMap.computeIfAbsent(detail.getAdvanceWeekRangeRemark(), k -> new CoreItem());
            coreItem.advanceWeek = rangeRemarkMap.getOrDefault(detail.getAdvanceWeekRangeRemark(), -1);
            coreItem.advanceWeekRangeRemark = detail.getAdvanceWeekRangeRemark();
            // 没有共识需求核心数的维度数据，不用将满足核心数统计到提前期范围数据中
            if (BigDecimal.ZERO.compareTo(detail.getConsensusCore()) != 0) {
                coreItem.totalMatchCore = coreItem.totalMatchCore.add(detail.getMatchCore());
                coreItem.totalConsensusCore = coreItem.totalConsensusCore.add(detail.getConsensusCore());
            }
        }
        // 返回给前端的明细数据，将共识需求核心数为0的移除
        details.removeIf(detail ->
                detail.getConsensusCore() == null || BigDecimal.ZERO.compareTo(detail.getConsensusCore()) == 0);
        resp.serviceLevel = resp.coreItem.calculateServiceLevel();

        resp.rangeList = new ArrayList<>();
        resp.coreItemMap.forEach((k, v) -> {
            v.calculateServiceLevel();
            resp.rangeList.add(v);
        });
        resp.rangeList.sort(Comparator.comparing(CoreItem::getAdvanceWeek));
        // 提前期降序排序，例如 提前13周、9～12周、6～8周、5周内
        Collections.reverse(resp.rangeList);
        return resp;
    }

    @Data
    public static class CoreItem {
        /** 提前期范围维度描述 */
        private String advanceWeekRangeRemark;
        /** 提前期 */
        private Integer advanceWeek;
        /** 满足核心数 */
        private BigDecimal totalMatchCore = BigDecimal.ZERO;
        /** 共识需求核心数 */
        private BigDecimal totalConsensusCore = BigDecimal.ZERO;
        /** 服务水平 */
        private BigDecimal serviceLevel;

        /** 服务水平计算 */
        private BigDecimal calculateServiceLevel() {
            serviceLevel = BigDecimal.ZERO.compareTo(totalConsensusCore) == 0 ? null
                    : totalMatchCore.divide(totalConsensusCore, 4, RoundingMode.HALF_UP);
            return serviceLevel;
        }
    }

    public static List<OrderServiceLevelDTO> merge(List<OrderItemMatchCore> matchList, List<OrderItemTag> tagList,
            ServiceLevelQueryReq req) {
        Map<String, OrderServiceLevelNoTagRange> map = new HashMap<>();
        Map<String, OrderItemTag> tagMap = ListUtils.toMap(tagList, OrderItemTag::getOrderNumberId, Function.identity());
        for (OrderItemMatchCore orderItem : matchList) {
            String key = orderItem.toKey();
            OrderServiceLevelNoTagRange dto = map.get(key);
            if (dto == null) {
                dto = OrderServiceLevelNoTagRange.create(orderItem);
                map.put(key, dto);
            }

            OrderItemTag tag = tagMap.get(orderItem.getOrderNumberId());
            if (tag != null && tag.getRealTagResult() != null) {
                dto.getRealTagResult().addAll(tag.getRealTagResult());
                List<OrderTagResultItemDto> mergeTag = OrderTagResultItemDto.merge(dto.getRealTagResult());
                dto.setRealTagResult(mergeTag);
            }

            if (orderItem.getTotalMatchCore() != null) {
                dto.setMatchCoreNoTageRange(dto.getMatchCoreNoTageRange().add(orderItem.getTotalMatchCore()));
            }
        }

        List<OrderServiceLevelDTO> res = new ArrayList<>();
        Collection<OrderServiceLevelNoTagRange> noTagRanges = map.values();
        Map<Integer, String> advanceWeekRangeMap = req.getAdvanceWeekRangeMap();
        List<Integer> advanceWeekRangeList = new ArrayList<>(advanceWeekRangeMap.keySet());
        // advanceWeekRangeList 降序排序，例如 13, 9, 6, 0，方便分段统计
        advanceWeekRangeList.sort((o1, o2) -> Integer.compare(o2, o1));
        // 相同维度的数据，再按提前期范围进行拆分
        for (OrderServiceLevelNoTagRange item : noTagRanges) {
            List<OrderTagResultItemDto> realTagResult = item.getRealTagResult();
            // 降序排序，例如 13, 9, 6, 0，方便分段统计
            SortedMap<Integer, List<OrderTagResultItemDto>> tagRangeMap = new TreeMap<>(
                    (o1, o2) -> Integer.compare(o2, o1));
            // 先初始化，保证每个提前期范围都有空数据
            advanceWeekRangeMap.forEach((key, advanceWeekRangeRemark) -> {
                tagRangeMap.computeIfAbsent(key, k -> new ArrayList<>());
            });
            for (OrderTagResultItemDto tagItem : realTagResult) {
                // 通过提前期范围进行拆分，效果与下面注释的代码效果一样，这里可以通过参数来控制提前期范围分段
                for (Integer week : advanceWeekRangeList) {
                    // 这里是提前期分段描述对应的提前多少周
                    if (tagItem.getAdvanceWeek() >= week) {
                        tagRangeMap.computeIfAbsent(week, k -> new ArrayList<>()).add(tagItem);
                        break;
                    }
                }
//                if (tagItem.getAdvanceWeek() >= 13) {
//                    tagRangeMap.computeIfAbsent("提前13周", k -> new ArrayList<>()).add(tagItem);
//                } else if (tagItem.getAdvanceWeek() >= 9) {
//                    tagRangeMap.computeIfAbsent("9-12周", k -> new ArrayList<>()).add(tagItem);
//                } else if (tagItem.getAdvanceWeek() >= 6) {
//                    tagRangeMap.computeIfAbsent("6-8周", k -> new ArrayList<>()).add(tagItem);
//                } else {
//                    tagRangeMap.computeIfAbsent("5周内", k -> new ArrayList<>()).add(tagItem);
//                }
            }
            tagRangeMap.forEach((k, v) -> {
                OrderServiceLevelDTO dto = new OrderServiceLevelDTO();
                BeanUtils.copyProperties(item, dto);
                BigDecimal consensusCore = BigDecimal.ZERO;
                for (OrderTagResultItemDto tagResultItemDto : v) {
                    consensusCore = consensusCore.add(BigDecimal.valueOf(tagResultItemDto.getCore()));
                }
                dto.setConsensusCore(consensusCore);
                // 相同维度下的多个提前期范围的满足核心数是同一个值
                dto.setMatchCore(item.getMatchCoreNoTageRange());
                // 提前期分段描述，例如：提前13周， 9～12周，6-8周，5周内 这种，可以通过传参控制
                dto.setAdvanceWeekRangeRemark(advanceWeekRangeMap.get(k));
                // 这里是提前期分段描述对应的提前多少周
                dto.setAdvanceWeek(k);
                dto.calculateServiceLevel();
                res.add(dto);
            });
        }
        return res;
    }

    @Data
    public static class OrderServiceLevelDTO extends OrderServiceLevelNoTagRange {

        /** 共识需求核心数（带提前期范围维度的） */
        private BigDecimal consensusCore = BigDecimal.ZERO;

        /** 满足核心数（相同维度下的多个提前期范围的满足核心数是同一个值） */
        private BigDecimal matchCore = BigDecimal.ZERO;

        /** 预测提前期范围描述 */
        private String advanceWeekRangeRemark;

        private Integer advanceWeek;

        // 服务水平 XX%
        private String serviceLevel;

        private static final BigDecimal oneHundred = BigDecimal.valueOf(100);

        public void calculateServiceLevel() {
            if (consensusCore != null && BigDecimal.ZERO.compareTo(consensusCore) != 0) {
                BigDecimal rate = matchCore.divide(consensusCore, 4, RoundingMode.HALF_UP);
                rate = rate.min(BigDecimal.ONE);
                serviceLevel = oneHundred.multiply(rate).setScale(2, RoundingMode.HALF_UP) + "%";
            } else {
                serviceLevel = "";
            }
        }

        public DwdCrpOrderServiceLevelDetailDO toDwdCrpOrderServiceLevelDetailDO(LocalDate statDate, String product) {
            DwdCrpOrderServiceLevelDetailDO item = new DwdCrpOrderServiceLevelDetailDO();
            BeanUtils.copyProperties(this, item);
            item.setStatDate(statDate);
            item.setProduct(product);
            item.setRealTagResult(JSON.toJson(this.getRealTagResult()));
            return item;
        }

        public static OrderServiceLevelDTO from(DwdCrpOrderServiceLevelDetailDO item) {
            if (item == null) {
                return null;
            }
            OrderServiceLevelDTO dto = new OrderServiceLevelDTO();
            BeanUtils.copyProperties(item, dto);
            dto.setRealTagResult(JSON.parseToList(item.getRealTagResult(), OrderTagResultItemDto.class));
            return dto;
        }

    }

    @Data
    public static class OrderServiceLevelNoTagRange extends OrderServiceLevelDimension {

        /** 满足核心数（不带带提前期范围维度的） */
        private BigDecimal matchCoreNoTageRange = BigDecimal.ZERO;

        private List<OrderTagResultItemDto> realTagResult = new ArrayList<>();

        private static OrderServiceLevelNoTagRange create(OrderServiceLevelDimension dim) {
            OrderServiceLevelNoTagRange dto = new OrderServiceLevelNoTagRange();
            BeanUtils.copyProperties(dim, dto);
            return dto;
        }
    }

    /** 订单服务水平报表需要的统计维度 */
    @Data
    public static class OrderServiceLevelDimension {

        @Column("order_number")
        private String orderNumber;

        @Column("industry_dept")
        private String industryDept;

        @Column("war_zone")
        private String warZone;

        @Column("zone_name")
        private String zoneName;

        @Column("customer_short_name")
        private String customerShortName;

        // 使用短的地域名称，不使用订单数据中的地域名称
        private String regionName;

        @Column("instance_type")
        private String instanceType;

        /** 年月（yyyyMM） */
        @Column("yearMonth")
        private String yearMonth;

        @Column("order_type")
        private String orderType;

        @Column("elastic_type")
        private String elasticType;

        @Column("bill_type")
        private String billType;

        @Column("customer_uin")
        private String customerUin;

        @Column("order_label")
        private String orderLabel;

        @Column("order_node_code")
        private String orderNodeCode;

        /** 境内/境外 */
        private String customhouseTitle;

        /** 是否为新机型，true表示是新机型，false表示旧机型 */
        private Boolean newInstanceType;

        /** 通用客户简称 */
        private String commonCustomerShortName;

        /** 是否主力机型，true表示是主力机型，fasle表示非主力机型 */
        private Boolean mainInstanceType;

        /** 是否主力园区，true表示是主力园区，false表示非主力园区 */
        private Boolean mainZone;

        public String toKey() {
            return String.format("%s_%s_%s_%s_%s_%s_%s_%s_%s_%s", industryDept, warZone, customerShortName,
                    regionName, instanceType, yearMonth, orderNumber, zoneName, orderType, elasticType);
        }
    }

    @Data
    public static class OrderItemMatchCore extends DetailDimension implements ZoneInfoFiller, MainZoneFiller,
            MainInstanceTypeFiller, IsNewInstanceTypeFiller, CommonCustomerShortNameFiller {

        @Column("order_number_id")
        private String orderNumberId;

        @Column("totalMatchCore")
        private BigDecimal totalMatchCore;

        @Column("totalDemandCore")
        private BigDecimal totalDemandCore;

        // 为true时表示已经处理过重复的满足度数据
        private boolean resolvedRepeatMatchCore = false;

        /**
         *   相同订单、可用区、实例类型、需求年月维度下，是同一个满足度，需要分配满足度到不同的orderNumberId，实现满足度去重
         */
        public static void resolveRepeatMatchCore(List<OrderItemMatchCore> list)  {
            Map<String, List<OrderItemMatchCore>> map = ListUtils.toMapList(list,
                    o-> String.join("_", o.getOrderNumber(), o.getZoneName(), o.getInstanceType(), o.getYearMonth()),
                    Function.identity());
            for (List<OrderItemMatchCore> values : map.values()) {
                if (ListUtils.isEmpty(values)) {
                    continue;
                }
                OrderItemMatchCore first = values.get(0);
                if (first == null || first.resolvedRepeatMatchCore) {
                    // 已经处理过重复的满足度数据，跳过
                    continue;
                }
                // 可待分配的满足度核心数
                BigDecimal waitAllocation = first.totalMatchCore == null ? BigDecimal.ZERO : first.totalMatchCore;
                for (OrderItemMatchCore item : values) {
                    item.resolvedRepeatMatchCore = true;
                    if (waitAllocation.compareTo(BigDecimal.ZERO) <= 0) {
                        // 没有可分配的满足度，将明细数据满足度设置为0
                        item.totalMatchCore = BigDecimal.ZERO;
                    } else {
                        // 分配的满足度不能大于 totalDemandCore
                        BigDecimal allocation = item.totalDemandCore == null ? BigDecimal.ZERO : item.totalDemandCore;
                        BigDecimal allocate = waitAllocation.min(allocation);
                        item.totalMatchCore = allocate;
                        waitAllocation = waitAllocation.subtract(allocate);
                    }
                }
            }
        }

        @Override
        public void fillIsNewInstanceType(Boolean isNewInstanceType) {
            setNewInstanceType(isNewInstanceType);
        }

        @Override
        public String provideInstanceType() {
            return getInstanceType();
        }

        @Override
        public void fillIsMainInstanceType(boolean isMainInstanceType) {
            setMainInstanceType(isMainInstanceType);
        }

        @Override
        public void fillIsMainZone(boolean isMainZone) {
            setMainZone(isMainZone);
        }

        @Override
        public String provideZoneName() {
            return getZoneName();
        }

        @Override
        public void fillZone(String zone) {

        }

        @Override
        public void fillAreaName(String areaName) {

        }

        @Override
        public void fillCustomhouseTitle(String customhouseTitle) {
            setCustomhouseTitle(customhouseTitle);
        }

        @Override
        public void fillRegionName(String regionName) {
            setRegionName(regionName);
        }

        @Override
        public String provideCustomerShortName() {
            return getCustomerShortName();
        }

        @Override
        public String provideIndustryDept() {
            return getIndustryDept();
        }

        @Override
        public void fillCommonCustomerShortName(String commonCustomerShortName) {
            setCommonCustomerShortName(commonCustomerShortName);
        }
    }

    @Data
    public static class OrderItemTag {

        @Column("order_number_id")
        private String orderNumberId;

        /**
         * 实际打标结果(修正后)<br/>Column: [real_tag_result]
         */
        @Column(value = "real_tag_result", isJSON = true)
        private List<OrderTagResultItemDto> realTagResult = new ArrayList<>();

    }

    @Data
    public static class DetailDimension extends OrderServiceLevelDimension {

    }

}
