package cloud.demand.app.modules.mrpv3.entity.dwd.purchase;

import cloud.demand.app.modules.mrpv2.enums.DemandTypeEnum;
import cloud.demand.app.modules.mrpv3.entity.getter.common.ISimpleGetter;
import cloud.demand.app.modules.mrpv3.enums.indexField.purchase.IHostDeliveredNum;
import cloud.demand.app.modules.mrpv3.enums.indexField.purchase.IHostDeliveredRealNum;
import cloud.demand.app.modules.mrpv3.enums.indexField.purchase.IProductOrderNum;
import cloud.demand.app.modules.mrpv3.utils.CommonUtils;
import java.math.BigDecimal;
import lombok.Data;

/** 供应计划指标 */
@Data
public class PurchaseDO implements ISimpleGetter,
        IProductOrderNum, // 产品采购下单量（已执行预测）
        IHostDeliveredNum,  // 产品采购到货量（需求月份）
        IHostDeliveredRealNum // 产品采购到货量（交付月份）
{

    /** 业务类型 */
    private String bizType;

    /** 行业部门 */
    private String industryDept;

    /** 境内外 */
    private String customhouseTitle;

    /** 地域名称 */
    private String regionName;

    /** 可用区名称 */
    private String zoneName;

    /** 设备类型 */
    private String deviceType;

    /** 实例类型 */
    private String instanceType;

    /** 客户 UIN */
    private String uin;

    /** 需求类型 */
    private String demandType;

    /** 数据产品 */
    private String product;

    /** 产品大类 */
    private String productClass;

    /** 年月 */
    private String yearMonth;

    /** 客户简称 */
    private String customerShortName;

    /** crp战区 */
    private String warZone;

    /** 已执行预测 */
    private BigDecimal productOrderNum;

    /** 采购到货（需求月份） */
    private BigDecimal hostDeliveredNum;

    /** 采购到货（交付月份） */
    private BigDecimal hostDeliveredRealNum;

    /** {@link cloud.demand.app.modules.mrpv3.enums.MrpV3IndexEnum#purchase } */
    @Override
    public BigDecimal getNum1() {
        return productOrderNum; // 产品采购下单量（已执行预测）
    }

    @Override
    public BigDecimal getNum2() {
        return hostDeliveredNum; // 产品采购到货量（需求月份）
    }

    @Override
    public BigDecimal getNum3() {
        return hostDeliveredRealNum; // 产品采购到货量（交付月份）
    }

    @Override
    public String getCountryName() {
        return null;
    }

    @Override
    public String getAreaName() {
        return null;
    }

    /**
     * db 查询类转处理类（注意：这里还未清洗数据，num 值也未存入处理类）
     * @param reportDO 全年需求报表查询数据
     * @return 处理类
     */
    public static PurchaseDO transform(AdsTcresDemandReportDO reportDO){
        PurchaseDO ret = new PurchaseDO();
        ret.setDeviceType(reportDO.getDeviceType());
        ret.setIndustryDept(reportDO.getIndustryDept());
        ret.setCustomhouseTitle(reportDO.getCustomhouseTitle());
        ret.setZoneName(reportDO.getZoneName());
        ret.setRegionName(reportDO.getRegionName());
        ret.setDemandType(DemandTypeEnum.NEW.getName());
        ret.setProduct(reportDO.getDataProduct());
        ret.setProductClass(CommonUtils.getProductClassByProduct(ret.getProduct()));
        ret.setYearMonth(reportDO.getYearMonth());
        ret.setCustomerShortName(reportDO.getCustomerShortName());
        return ret;

    }
}
