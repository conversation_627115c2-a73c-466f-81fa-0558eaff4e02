package cloud.demand.app.modules.mrpv3.utils;

import cloud.demand.app.modules.mrpv3.enums.IFieldGetSetEnum;
import cloud.demand.app.modules.order_report.enums.IFieldEnum;
import cloud.demand.app.modules.soe.utils.SoeCommonUtils;
import com.pugwoo.wooutils.collect.ListUtils;
import lombok.Data;
import lombok.SneakyThrows;
import yunti.boot.exception.ITException;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class ResultItemWithReq<R> {
    private final R req;
    private final Map<String, IFieldGetSetEnum> fieldMap;

    private Map<String, String> reqValueMap;

    private final Set<String> dims;

    /** 忽略空值 */
    private final boolean ignoreEmpty;

    private final Map<String,Set<String>> dimValueMap;


    public ResultItemWithReq(R req, IFieldGetSetEnum[] fields,List<String> dims,boolean ignoreEmpty,Map<String,Set<String>> dimValueMap) {
        this.req = req;
        this.fieldMap = Arrays.stream(fields).collect(Collectors.toMap(IFieldGetSetEnum::name, item->item));
        this.ignoreEmpty = ignoreEmpty;
        this.dimValueMap = dimValueMap;
        this.dims = new HashSet<>(dims);
        init();
    }

    @SneakyThrows
    private void init(){
        if (req == null){
            return;
        }
        Class<?> aClass = req.getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        Map<String,Field> map = new HashMap<>();
        for (Field declaredField : declaredFields) {
            map.put(declaredField.getName(),declaredField);
        }
        reqValueMap = new HashMap<>();
        for (IFieldGetSetEnum value : fieldMap.values()) {
            Field field = map.get(value.name());
            if (field != null){
                field.setAccessible(true);
                Object o = field.get(req);
                if (o instanceof List){
                    List<String> list = (List<String>) o;
                    if (ignoreEmpty){
                        list = list.stream().filter(SoeCommonUtils::isNotBlank).collect(Collectors.toList());
                    }
                    if (ListUtils.isNotEmpty(list)){
                        if (list.size() == 1){
                            reqValueMap.put(value.name(), list.get(0));
                        }else {
                            Set<String> set = dimValueMap.get(value.name());
                            if (set != null){
                                Set<String> collect = list.stream().filter(set::contains).collect(Collectors.toSet());
                                if (collect.size() == set.size()){
                                    reqValueMap.put(value.name(), String.join("&", collect));
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    public boolean hasDim(String fieldName){
        return dims.contains(fieldName);
    }

    /**
     * @param t 类
     * @param fieldName 字段
     * @return 返回字段值
     */
    public String getFieldValue(Object t,String fieldName) {
        if (!fieldMap.containsKey(fieldName)){
            throw new ITException("field 不存在:"+fieldName);
        }
        if (hasDim(fieldName)) {
            if (t == null){
                return null;
            }
            Function<Object, Object> fieldGetter = fieldMap.get(fieldName).getFieldGetter();
            return (String)fieldGetter.apply(t);
        }else {
            return reqValueMap.get(fieldName);
        }
    }
}
