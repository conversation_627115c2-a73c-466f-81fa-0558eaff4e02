package cloud.demand.app.modules.mrpv3.task.monitor;

import cloud.demand.app.modules.mrpv2.entity.SimpleCommonTask;
import cloud.demand.app.modules.mrpv3.task.process.MonitorMrpV3TaskProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.process.ISopProcess;
import cloud.demand.app.modules.sop_device.sopTask.frame.work.AbstractSopWork;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;

/** 监控任务-客户级别准确率 */
public abstract class MonitorCommonWork extends AbstractSopWork<SimpleCommonTask> {
    @Resource
    private MonitorMrpV3TaskProcess process;
    @Override
    public ISopProcess<SimpleCommonTask> getTask() {
        return process;
    }

    /** 5 分钟执行一次 */
    @Scheduled(fixedRate = 5 * 60 * 1000)
    @Override
    public void work() {
        super.work();
    }
}
