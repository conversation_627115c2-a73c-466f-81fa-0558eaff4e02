package cloud.demand.app.modules.asset_diff_detail;

import cloud.demand.app.common.utils.ORMUtils;
import cloud.demand.app.entity.cubes.StoRmdbServersDO;
import cloud.demand.app.entity.rrp.ReportErpServerDiffDetailDO;
import cloud.demand.app.entity.yunti.CloudDemandCsigDeviceExtendInfoDO;
import cloud.demand.app.modules.common.enums.ProductTypeEnum;
import cloud.demand.app.modules.common.service.DictService;
import cloud.demand.app.modules.common.service.TaskLog;
import cloud.demand.app.modules.common.service.TaskLogService;
import com.google.common.collect.ImmutableMap;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.dbhelper.annotation.Column;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.nutz.lang.Lang;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * 无单据不逻辑补全
 * 有单据，库存切片无变化
 */
@Service
@Slf4j
public class HasOrderNoDiffService {

    @Resource
    private DBHelper rrpDBHelper;
    @Resource
    private DBHelper ckcubesDBHelper;
    @Resource
    private TaskLogService taskLogService;
    @Resource
    private DictService dictService;

    /**
     * 人工生成无单据的记录，是自动生成无单据数据后的一层处理
     */
    @TaskLog(taskName = "HasOrderNoDiff")
    public void manualGenDiff(String statTime){
        //  不包含COS
        List<String> products = Lang.list(ProductTypeEnum.CVM.getCode(), ProductTypeEnum.METAL.getCode(), ProductTypeEnum.GPU.getCode(),
                ProductTypeEnum.CBS.getCode(), ProductTypeEnum.CDB.getCode(), ProductTypeEnum.NETWORK.getCode(),
                ProductTypeEnum.CRS.getCode(), ProductTypeEnum.CMONGO.getCode(), ProductTypeEnum.COS.getCode());

        for (String product : products) {
            //  1、获取几个单据表中的固资结果
            Map<String, AssetGroupDTO> fromOrderTables = getFromOrderTables(statTime, product);
            //  2、从无单据表中获取到的固资结果
            Map<String, AssetGroupDTO> fromNoOrderTable = getFromNoOrderTable(statTime, product);
            //  3、进行比较
            List<ReportErpServerDiffDetailDO> diffAssets = getDiffAssets(statTime, product, fromOrderTables, fromNoOrderTable);
            //  4、插入数据
            replaceAll(diffAssets, statTime, product);
        }
    }

    /**
     * 替换当天的有单无库存变化数据
     */
    private void replaceAll(List<ReportErpServerDiffDetailDO> diffAssets, String statTime, String productType){
        rrpDBHelper.delete(ReportErpServerDiffDetailDO.class,
                "where stat_time = ? and product_type = ? and is_manual_gen = 1", statTime, productType);
        rrpDBHelper.insertBatchWithoutReturnId(diffAssets);
    }

    /**
     * 生成有单据库存无变化的固资DO
     */
    private List<ReportErpServerDiffDetailDO> getDiffAssets(String statTime, String productType,
                                      Map<String, AssetGroupDTO> hasOrder, Map<String, AssetGroupDTO> noOrder){
        List<ReportErpServerDiffDetailDO> result = Lang.list();

        Set<String> allAssetIds = new HashSet<>();
        allAssetIds.addAll(hasOrder.keySet());

        if (ListUtils.isEmpty(allAssetIds)){
            return result;
        }

        //  因逻辑核心数而导致的差异
        Integer coreNumDiff = 0;
        for (String id : allAssetIds) {
            AssetGroupDTO hasOrderDTO = hasOrder.get(id);
            AssetGroupDTO noOrderDTO = noOrder.get(id);
            Integer status = getStatusCode(hasOrderDTO, noOrderDTO);
            coreNumDiff += getDiffCoreNum(id, hasOrderDTO, noOrderDTO);
            if(status == 0){
                continue;
            }
            ReportErpServerDiffDetailDO diffDO = null;
            if (status > 0){
                diffDO = manualGenDiffDO(statTime, id, productType, "转出", hasOrderDTO == null ? null : hasOrderDTO.getType());
            }else if (status < 0){
                diffDO = manualGenDiffDO(statTime, id, productType, "转入", hasOrderDTO == null ? null : hasOrderDTO.getType());
            }
            if (diffDO != null){
                result.add(diffDO);
            }
        }
        log.info("因逻辑核心数而导致差异{}核", coreNumDiff);
        return result;
    }

    private Integer getDiffCoreNum(String id, AssetGroupDTO hasOrderDTO, AssetGroupDTO noOrderDTO){
        if (hasOrderDTO == null || noOrderDTO == null){
            return 0;
        }
        BigDecimal hasOrderCoreNum = hasOrderDTO.getLogicCpuCore();
        BigDecimal noOrderCoreNum = noOrderDTO.getLogicCpuCore();
        Integer hasOrderCoreIntValue = hasOrderCoreNum == null ? 0 : hasOrderCoreNum.intValue();
        Integer noOrderCoreIntValue = noOrderCoreNum == null ? 0 : noOrderCoreNum.intValue();
        if(!Objects.equals(hasOrderCoreIntValue, noOrderCoreIntValue)){
            return hasOrderCoreIntValue - noOrderCoreIntValue;
        }
        return 0;
    }

    /**
     * 手工处理无单据数据
     */
    private ReportErpServerDiffDetailDO manualGenDiffDO(String statTime, String assetId,
                                                        String productType, String diffType, String orderType){
        ReportErpServerDiffDetailDO diffDO = new ReportErpServerDiffDetailDO();
        LocalDate nextDay = DateUtils.toLocalDate(DateUtils.addTime(DateUtils.parse(statTime), Calendar.DATE, 1));
        ORMUtils.WhereContent whereContent = new ORMUtils.WhereContent();
        whereContent.andEqual("DAY", nextDay);
        whereContent.andEqual("LogicPcCode", assetId);
        List<StoRmdbServersDO> all =
                ckcubesDBHelper.getAll(StoRmdbServersDO.class, whereContent.getSql(), whereContent.getParams());
        if(ListUtils.isEmpty(all)){
            String msg = "DAY={"+ DateUtils.formatDate(nextDay) + "}, {"+ assetId + "}的固资未找到!";
            taskLogService.genRunLog("manualGenDiff", "manualGenDiffDO", msg);
            return null;
        }

        StoRmdbServersDO serverDO = all.get(0);
        diffDO.setStatTime(DateUtils.toLocalDate(DateUtils.parse(statTime)));
        diffDO.setProductType(productType);
        diffDO.setAssetId(assetId);
        diffDO.setDeviceType(serverDO.getDeviceType());
        //  无单据
        diffDO.setHasOrder(0);
        diffDO.setOrderType(orderType);
        diffDO.setLogicNum(
                getLogicNum(statTime, assetId, ProductTypeEnum.getByCode(productType), orderType, serverDO.getDeviceType()));
        diffDO.setLogicCpuCore(BigDecimal.valueOf(serverDO.getCpuLogicCore() == null ? 0 : serverDO.getCpuLogicCore()));
        //  手动后置生成
        diffDO.setIsManualGen(1);
        diffDO.setDiffType(diffType);
        return diffDO;
    }


    /**
     * 不同产品的逻辑数获取
     */
    private BigDecimal getLogicNum(String statTime, String assetId, ProductTypeEnum productType, String orderType, String deviceType){
        BigDecimal logicNum = BigDecimal.ZERO;
        if(productType == null || StringUtils.isBlank(deviceType)){
            return logicNum;
        }
        switch (productType){
            case CVM:
                CloudDemandCsigDeviceExtendInfoDO cvmInfo =
                        dictService.getCsigDeviceExtendInfoByDeviceType(deviceType);
                if (cvmInfo != null) {
                    logicNum = cvmInfo.getSaleCore();
                }
                break;
            case METAL:
                CloudDemandCsigDeviceExtendInfoDO metalInfo =
                        dictService.getCsigDeviceExtendInfoByDeviceType(deviceType);
                if (metalInfo != null) {
                    logicNum = metalInfo.getSaleCore();
                }
                break;
            case GPU:
                // GPU卡数以腾讯云的字典表为准
                Integer deviceGpuCard = dictService.getDeviceGpuCard(deviceType);
                logicNum = BigDecimal.valueOf(deviceGpuCard);
                break;
            case CBS:
                logicNum = dictService.getDeviceCbsStore(deviceType);
                break;
            case CDB:
                logicNum = dictService.getDeviceCdbStore(deviceType);
                break;
            case CRS:
                logicNum = dictService.getDeviceCrsMemCap(deviceType);
                break;
            case CMONGO:
                logicNum = dictService.getCmongoDeviceMem(deviceType);
                break;
            case NETWORK:
                logicNum = BigDecimal.ONE;
                break;
            case COS:
                String sql = null;
                switch (orderType){
                    case "采购":
                        sql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/cos/get_plan_product_purchase.sql");
                        break;
                    case "退回":
                        sql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/cos/get_plan_product_return.sql");
                        break;
                    case "转移-转入":
                        sql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/cos/get_plan_product_transfer_in.sql");
                        break;
                    case "转移-转出":
                        sql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/cos/get_plan_product_transfer_out.sql");
                        break;
                }
                List<String> planProductName = rrpDBHelper.getRaw(String.class, sql, statTime, assetId);
                if (ListUtils.isNotEmpty(planProductName)){
                    logicNum = dictService.getDeviceCosStore(planProductName.get(0),deviceType);
                }
                break;
        }
        return logicNum;
    }

    /**
     *
     * @param hasOrderDTO 有单据表中该固资DTO
     * @param noOrderDTO 无单据表中该固资DTO
     * @return 返回0说明刚好抵消，返回正数补充转入，返回负数补充转出
     */
    private Integer getStatusCode(AssetGroupDTO hasOrderDTO, AssetGroupDTO noOrderDTO){
        BigDecimal hasOrderCount = BigDecimal.ZERO;
        BigDecimal noOrderCount = BigDecimal.ZERO;

        if (hasOrderDTO != null){
            hasOrderCount = BigDecimal.valueOf(hasOrderDTO.getCount());
        }
        if(noOrderDTO != null){
            noOrderCount = BigDecimal.valueOf(noOrderDTO.getCount());
        }
        return add(hasOrderCount, noOrderCount).intValue();
    }

    private BigDecimal add(BigDecimal num1, BigDecimal num2){
        if (num1 == null){
            num1 = BigDecimal.ZERO;
        }
        if (num2 == null){
            num2 = BigDecimal.ZERO;
        }
        return num1.add(num2);
    }

    /**
     * 从固资差异表中拿到所有有单据的差异
     * @param statTime
     * @return
     */
    private Map<String, AssetGroupDTO> getFromNoOrderTable(String statTime, String productType){
        ImmutableMap<String, String> params =
                ImmutableMap.of("statTime", statTime, "productType", productType);

        List<AssetGroupDTO> noOrderResult = Lang.list();
        String sql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/no_order.sql");
        noOrderResult.addAll(rrpDBHelper.getRaw(AssetGroupDTO.class, sql, params));
        return ListUtils.toMap(noOrderResult, AssetGroupDTO::getAssetId, o -> o);
    }

    /**
     * 从几个单据数据源获取数据，按照固资维度加和
     * 转入算正数、转出算负数
     */
    private Map<String, AssetGroupDTO> getFromOrderTables(String statTime, String productType){
        ImmutableMap<String, String> params =
                ImmutableMap.of("statTime", statTime, "productType", productType);
        String hasOrderSql = ORMUtils.getSql("/sql/asset_diff/manual_gen_no_order/has_order.sql");
        List<AssetGroupDTO> orderResult = rrpDBHelper.getRaw(AssetGroupDTO.class, hasOrderSql, params);
        return ListUtils.toMap(orderResult, AssetGroupDTO::getAssetId, o -> o);
    }

    @Data
    public static class AssetGroupDTO{
        @Column("asset_id")
        private String assetId;

        @Column("logic_cpu_core")
        private BigDecimal logicCpuCore;

        @Column("type")
        private String type;

        @Column("c")
        private int count;
    }




}
