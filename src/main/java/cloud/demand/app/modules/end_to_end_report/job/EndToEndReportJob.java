package cloud.demand.app.modules.end_to_end_report.job;

import cloud.demand.app.modules.end_to_end_report.service.EndToEndReportGenDataService;
import com.pugwoo.wooutils.redis.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 端到端视图定时任务
 */

@Slf4j
@Service
public class EndToEndReportJob {

    @Resource
    private EndToEndReportGenDataService endToEndReportGenDataService;

    @Synchronized(waitLockMillisecond = 100, throwExceptionIfNotGetLock = false)
    @Scheduled(cron = "0 25 0 * * ?")
    public String genEnd2EndData(){
        endToEndReportGenDataService.genDeviceIncreaseData(null);
        endToEndReportGenDataService.genStockReturnDeviceDetail(null);
        return "done";
    }


}

