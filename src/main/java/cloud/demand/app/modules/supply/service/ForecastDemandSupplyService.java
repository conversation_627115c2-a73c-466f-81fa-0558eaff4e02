package cloud.demand.app.modules.supply.service;

import cloud.demand.app.modules.supply.model.DictColEnum;
import cloud.demand.app.modules.supply.model.SupplyForecastReq;
import cloud.demand.app.modules.supply.model.SupplyForecastResp;
import java.util.List;

public interface ForecastDemandSupplyService {

    SupplyForecastResp queryDemandSupply(SupplyForecastReq req);

    /**
     * 获取字典
     *
     * @return
     * @param key
     */
    List<String> getDictBy(DictColEnum key);
}
