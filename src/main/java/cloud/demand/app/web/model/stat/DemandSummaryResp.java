package cloud.demand.app.web.model.stat;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 需求汇总返回体
 */
@Data
public class DemandSummaryResp {

    @Data
    public static class ColumnName {
        private String title;
        private String dataIndex;

        public ColumnName(String title, String dataIndex) {
            this.title = title;
            this.dataIndex = dataIndex;
        }
    }

    private List<ChangeAnalysisResp.ColumnName> tableTitle;

    private List<Map<String, Object>> data;

}
