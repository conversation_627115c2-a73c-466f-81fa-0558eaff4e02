-- 内部业务
select stat_time,
       '内部业务'            type,
       '未分类'        deptOrProduct,
       '购买'              demand_type,
       case
           when biz_type = 'cvm' and cpu_or_gpu = 'CPU' then 'CVM'
           when biz_type = 'baremetal' and cpu_or_gpu = 'CPU' then '裸金属'
           when cpu_or_gpu = 'GPU' then 'GPU' end as `product_type`,
       sum(case when cpu_or_gpu = 'CPU' then diff_freecpu when cpu_or_gpu = 'GPU' then diff_freegpu end) total
from ppl_billing_scale_monthly
where stat_time in (:statTime)
  and customer_uin not in (:uins)
  and app_role not in ('正常售卖', 'CDH', '预扣包', 'LH')
    ${DEMAND_TYPE} > 0
    ${FILTER}
group by stat_time, deptOrProduct, product_type

union all

select stat_time,
       '内部业务'            type,
       '未分类'        deptOrProduct,
       '退回'              demand_type,
       case
           when biz_type = 'cvm' and cpu_or_gpu = 'CPU' then 'CVM'
           when biz_type = 'baremetal' and cpu_or_gpu = 'CPU' then '裸金属'
           when cpu_or_gpu = 'GPU' then 'GPU' end as `product_type`,
       sum(case when cpu_or_gpu = 'CPU' then diff_freecpu when cpu_or_gpu = 'GPU' then diff_freegpu end) total
from ppl_billing_scale_monthly
where stat_time in (:statTime)
  and customer_uin not in (:uins)
  and app_role not in ('正常售卖', 'CDH', '预扣包', 'LH')
    ${DEMAND_TYPE} < 0
    ${FILTER}
group by stat_time, deptOrProduct, product_type
