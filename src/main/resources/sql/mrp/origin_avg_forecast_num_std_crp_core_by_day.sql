select t.*, null order_type
from (
-- CVM
-- PASS产品全部算内部业务
         select multiIf(category = '内部业务' or product in (${PAAS}), '内领业务', '外部行业') biz_type,
                multiIf(biz_type = '外部行业', industry_dept, biz_type = '内领业务', product,
                        '(空值)')                                                              industry_or_product,
                industry_dept,
                multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
                        customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
                        '(空值)')                                                              region_type,
                region_name,
                zone_name,
                instance_type                                                                  gins_family,
                customer_uin                                                                   uin,
                multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
                        demand_type = 'RETURN', '退回',
                        '(空值)')                                                              `demand_type`,
                'CVM'                                                                          data_product,
                product                                                                        product_class,
                formatDateTime(begin_buy_date, '%Y-%m')                                        `year_month`,
                customer_name,
                customer_short_name,
                multiIf(customer_society_type = 0, '个人', customer_society_type = 1, '企业',
                        '(空值)')                                                              customer_person_type,
                customer_tab_type                                                              `customer_type`,
                cname                                                                          customer_group,
                war_zone                                                                       war_zone_name,
                multiIf(uin_type = 0, '内部', uin_type = 1, '外部', '(空值)')                  customer_inside_or_outside,
                is_spike,
                sum(if(demand_type = '退回', -min_core_day, min_core_day))                     min_core_day_num,
                sum(if(demand_type = '退回', -max_core_day, max_core_day))                     max_core_day_num
         from std_crp.dws_crp_ppl_item_version_532_new_core_day_mif
         where product in ('CVM&CBS', ${PAAS})
           and ((source in ('IMPORT', 'APPLY_AUTO_FILL')) or source = 'FORECAST')
         group by product, biz_type, industry_or_product, industry_dept, region_type, region_name, zone_name,
                  gins_family, uin,
                  `demand_type`, data_product, `year_month`, customer_name, customer_short_name,
                  `customer_type`, is_spike,
                  customer_group, war_zone_name, customer_person_type, customer_inside_or_outside
         union all
         -- GPU
         select multiIf(category = '内部业务', '内领业务', '外部行业')        biz_type,
                multiIf(biz_type = '外部行业', industry_dept, biz_type = '内领业务', product,
                        '(空值)')                                             industry_or_product,
                industry_dept,
                multiIf(customhouse_title = '国内', '境内', customhouse_title = '海外', '境外',
                        customhouse_title = '境内', '境内', customhouse_title = '境外', '境外',
                        '(空值)')                                             region_type,
                region_name,
                zone_name,
                instance_type                                                 gins_family,
                customer_uin                                                  uin,
                multiIf(demand_type = 'NEW', '新增', demand_type = 'ELASTIC', '弹性',
                        demand_type = 'RETURN', '退回',
                        '(空值)')                                             `demand_type`,
                'GPU'                                                         data_product,
                product                                                       product_class,
                formatDateTime(begin_buy_date, '%Y-%m')                       `year_month`,
                customer_name,
                customer_short_name,
                multiIf(customer_society_type = 0, '个人', customer_society_type = 1, '企业',
                        '(空值)')                                             customer_person_type,
                customer_tab_type                                             `customer_type`,
                cname                                                         customer_group,
                war_zone                                                      war_zone_name,
                multiIf(uin_type = 0, '内部', uin_type = 1, '外部', '(空值)') customer_inside_or_outside,
                is_spike,
                sum(if(demand_type = '退回', -min_core_day, min_core_day))    min_core_day_num,
                sum(if(demand_type = '退回', -max_core_day, max_core_day))    max_core_day_num
         from std_crp.dws_crp_ppl_item_version_532_new_core_day_mif
         where product = 'GPU(裸金属&CVM)'
           and ((source in ('IMPORT', 'APPLY_AUTO_FILL')) or source = 'FORECAST')
         group by product, biz_type, industry_or_product, industry_dept, region_type, region_name, zone_name,
                  gins_family, uin,
                  `demand_type`, data_product, `year_month`, customer_name, customer_short_name,
                  `customer_type`, is_spike,
                  customer_group, war_zone_name, customer_person_type, customer_inside_or_outside) t
where t.`year_month` >= ?