
select b.order_number ,
       b.order_number_id ,
       a.customer_uin ,
       a.industry_dept ,
       a.war_zone ,
       b.zone_name ,
       a.customer_short_name ,
       b.region_name ,
       b.instance_type ,
       a.order_type ,
       b.bill_type ,
       a.customer_uin ,
       a.order_label,
       a.order_node_code,
       IF(a.order_type = 'ELASTIC', a.elastic_type, null) as elastic_type,
       DATE_FORMAT(c.begin_buy_date, '%Y%m') as yearMonth, -- 使用弹性订单月度满足量的所属月
       sum(IFNULL(c.satisfied_cpu_num, 0)) as totalMatchCore,
       max(b.total_core) as totalDemandCore
FROM order_item b
left join order_info a on a.id = b.order_info_id
left join order_cycle_elastic_month_satisfy c
          on b.order_number = c.order_number and b.instance_type = c.instance_type
          and b.zone_name = c.zone_name and c.deleted = 0 and c.available = 1
    ${where}
-- 没有计算弹性满足量的不用统计
and c.id is not null
group by b.order_number ,
         b.order_number_id ,
         a.customer_uin ,
         a.industry_dept ,
         a.war_zone ,
         b.zone_name ,
         a.customer_short_name ,
         b.region_name ,
         b.instance_type ,
         a.order_type ,
         b.bill_type ,
         a.customer_uin ,
         a.order_label,
         a.order_node_code,
         DATE_FORMAT(c.begin_buy_date, '%Y%m')
