
select b.order_number ,
       b.order_number_id ,
       a.customer_uin ,
       a.industry_dept ,
       a.war_zone ,
       b.zone_name ,
       a.customer_short_name ,
       b.region_name ,
       b.instance_type ,
       a.order_type ,
       b.bill_type ,
       a.customer_uin ,
       a.order_label,
       a.order_node_code,
       IF(a.order_type = 'ELASTIC', a.elastic_type, null) as elastic_type,
       DATE_FORMAT(a.begin_buy_date, '%Y%m') as yearMonth,
       sum(IFNULL(c.total_match_core, 0)) as totalMatchCore
FROM order_item b
left join order_info a on a.id = b.order_info_id
left join order_item_satisfy_rate_bk_2_21 c
    on b.order_number_id = c.order_number_id and c.deleted = 0 and c.main_result = 1
${where}
group by b.order_number ,
         b.order_number_id ,
         a.customer_uin ,
         a.industry_dept ,
         a.war_zone ,
         b.zone_name ,
         a.customer_short_name ,
         b.region_name ,
         b.instance_type ,
         a.order_type ,
         b.bill_type ,
         a.customer_uin ,
         a.order_label,
         a.order_node_code,
         DATE_FORMAT(a.begin_buy_date, '%Y%m')
