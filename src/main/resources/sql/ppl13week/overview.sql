select
    `year`,
    `month`,
    `type`,
    SUM(draft) draft,
    SUM(pending) progress,
    SUM(finished) finished
from
    (
        select
            YEAR(begin_buy_date) as `year`,
            MONTH (begin_buy_date) `month`,
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END AS `type`,
            SUM(total_core) as draft,
            0 as pending,
            0 as finished
        from
            ppl_item_draft
        ${draft_sql}
        group by
            YEAR(begin_buy_date),
            MONTH (begin_buy_date),
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END
        union ALL
        select
            YEAR(begin_buy_date) as `year`,
            MONTH (begin_buy_date) `month`,
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END AS `type`,
            0 as draft,
            0 as pending,
            SUM(total_core)as finished
        from
            ppl_item pl
        ${order_sql}
        group by
            YEAR(begin_buy_date),
            <PERSON>ONTH (begin_buy_date),
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END
        union ALL
        select
            YEAR(begin_buy_date) as `year`,
            <PERSON>ONTH (begin_buy_date) `month`,
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END AS `type`,
            0 as draft,
            SUM(total_core) as pending,
            0 as finished
        from
            ppl_order_audit_record_item pl
        ${audit_sql}
        group by
            YEAR(begin_buy_date),
            MONTH (begin_buy_date),
            CASE
            WHEN demand_type = 'RETURN' THEN '退回'
            ELSE '新增'
            END
    ) aa
group by
    `year`,
    `month`,
    `type`
