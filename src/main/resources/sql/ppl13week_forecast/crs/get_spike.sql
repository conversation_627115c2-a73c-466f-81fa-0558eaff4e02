with raw as
         (select year,month,uin as customer_uin, any(industry_dept1) as industry_dept, any(customer) as customer_short_name,
    zone_name, any(region_name) as region_name, any(customhouse_title1) as customhouse_title,
    '' as instance_type, '' as biz_range_type,
    sum(case when diff_core>0 then diff_core else 0 end) as new_core,
    sum(case when diff_core<0 then -diff_core else 0 end) as ret_core,
    sum(cur_core) as cur_core
from
    (
    -- 月切
    select year,month,uin,
    any(customer_short_name) as customer,
    any(industry_dept) as industry_dept1,
    zone_name, any(region_name) as region_name, any(customhouse_title) as customhouse_title1,
    sum(case when stat_time=month_end_date then change_service_mem_from_last_month else 0 end) as diff_core,
    sum(case when stat_time=month_end_date then cur_service_mem else 0 end) as cur_core
    from std_crp.dwd_txy_crs_scale_df t
    where stat_time < :predictDate

    and t.region_name != '(空值)'

    -- performance optimize
    and stat_time in (
    SELECT toDate(formatDateTime(subtractDays(addMonths(toDate('2020-01-01'), number + 1), 1), '%Y-%m-%d')) AS last_day_of_month
    FROM numbers(dateDiff('month', toDate('2020-01-01'), toDate('2050-12-31')) + 1)
    )

    group by year,month,uin,zone_name
    )
group by year,month,uin,zone_name)
select * from raw where new_core>:spikeThreshold or ret_core>:spikeThreshold

-- 特别说明：crs在剔除毛刺上为何细到uin而不是客户简称，是因为crs希望在内部客户上也尽量涵盖更多的内部中长尾部门，因此更加适合于用uin而不是客户简称
